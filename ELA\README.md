# 企业官网门户网站

一个现代化、响应式的企业官网门户网站，基于Vue.js 2.5开发，提供完整的企业展示解决方案。

## 🚀 项目特点

### 现代化设计
- 美观的渐变色彩搭配
- 流畅的动画交互效果
- 卡片式布局设计
- 响应式设计，支持多种设备

### 完整的功能模块
- **首页**: 品牌展示、特色介绍、产品服务、数据统计、客户反馈
- **关于我们**: 公司介绍、企业文化、团队展示、发展历程、荣誉资质
- **产品服务**: 产品分类、详细介绍、服务流程、客户案例、技术支持
- **新闻资讯**: 文章分类、搜索功能、精选文章、分页显示、文章详情
- **联系我们**: 联系表单、联系方式、地图位置、多渠道沟通

### 技术特色
- Vue.js 2.5 + Vue Router
- 组件化开发
- ES6+ 语法
- CSS3 动画
- 移动端优先的响应式设计

## 📁 项目结构

```
src/
├── components/           # 页面组件
│   ├── Header.vue       # 导航栏组件
│   ├── Footer.vue       # 页脚组件
│   ├── Home.vue         # 首页
│   ├── About.vue        # 关于我们
│   ├── Products.vue     # 产品服务
│   ├── News.vue         # 新闻资讯
│   └── Contact.vue      # 联系我们
├── router/              # 路由配置
│   └── index.js
├── assets/              # 静态资源
├── App.vue              # 主应用组件
└── main.js              # 应用入口
```

## 🎯 主要功能

### 首页 (Home)
- **英雄区域**: 品牌展示和核心价值主张
- **特色介绍**: 企业核心优势展示
- **产品服务**: 核心产品介绍
- **数据统计**: 企业关键指标展示
- **客户反馈**: 真实客户评价
- **CTA区域**: 引导用户行动

### 关于我们 (About)
- **公司介绍**: 企业使命和核心价值
- **企业文化**: 价值观和文化理念
- **团队介绍**: 核心团队成员展示
- **发展历程**: 企业重要里程碑
- **荣誉资质**: 获得的认证和奖项

### 产品服务 (Products)
- **产品分类**: 可筛选的产品分类
- **产品展示**: 详细的产品信息和特性
- **服务流程**: 标准化的服务流程
- **客户案例**: 成功实施案例
- **技术支持**: 售后服务保障
- **产品演示**: 模态框展示演示内容

### 新闻资讯 (News)
- **文章分类**: 公司动态、行业资讯、技术文章等
- **搜索功能**: 关键词搜索文章
- **精选文章**: 重要文章推荐
- **分页显示**: 优化的分页体验
- **文章详情**: 模态框显示完整内容
- **侧边栏**: 最新动态、热门标签、联系方式

### 联系我们 (Contact)
- **联系表单**: 完整的表单验证和提交
- **联系方式**: 多种联系渠道
- **营业时间**: 详细的服务时间
- **地图展示**: 公司位置信息
- **多渠道联系**: 微信、电话、邮件等

## 🛠 安装和运行

### 环境要求
- Node.js >= 6.0.0
- npm >= 3.0.0

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 生产构建
```bash
npm run build
```

### 代码检查
```bash
npm run lint
```

### 运行测试
```bash
npm test
```

## 🎨 设计系统

### 颜色方案
- 主色调: `#667eea` (蓝紫色)
- 辅助色: `#764ba2` (深紫色)
- 成功色: `#28a745`
- 警告色: `#ffc107`
- 错误色: `#dc3545`
- 文本色: `#333` (深色), `#666` (中等), `#999` (浅色)

### 字体
- 主字体: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif
- 响应式字体大小从移动端到桌面端递增

### 间距系统
- 基础间距: 8px
- 组件间距: 16px, 24px, 32px
- 章节间距: 60px, 80px

## 📱 响应式设计

### 断点设置
- 移动端: <= 480px
- 平板端: <= 768px
- 桌面端: > 768px

### 适配特性
- 弹性网格布局
- 响应式图片
- 移动端优化的导航
- 触摸友好的交互元素

## 🔧 自定义配置

### 企业信息修改
1. 更新 `Header.vue` 中的品牌名称
2. 修改 `Footer.vue` 中的联系信息
3. 替换各页面中的企业介绍内容
4. 更新 `Contact.vue` 中的联系方式

### 样式定制
1. 修改 `App.vue` 中的全局样式变量
2. 更新各组件的颜色和样式
3. 替换 Logo 和品牌元素

### 内容更新
1. 更新各页面的文案内容
2. 替换产品和服务信息
3. 添加真实的客户案例
4. 更新新闻资讯内容

## 🌟 最佳实践

### 性能优化
- 组件懒加载
- 图片优化
- CSS代码分割
- 路由缓存

### SEO优化
- 语义化HTML结构
- Meta标签优化
- 结构化数据
- 网站地图

### 可访问性
- ARIA标签
- 键盘导航支持
- 高对比度设计
- 屏幕阅读器兼容

## 📦 部署建议

### 静态部署
- 适用于 Nginx、Apache
- 需要配置 history 模式路由

### CDN部署
- 静态资源CDN加速
- 全球内容分发

### 容器部署
- Docker 容器化
- Kubernetes 集群部署

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 技术支持

如果您在使用过程中遇到问题或有建议，欢迎：

- 提交 Issue
- 发送邮件至: <EMAIL>
- 访问我们的官网: https://yourcompany.com

---

**注意**: 这是一个演示项目，实际使用时请根据具体需求进行定制和优化。

``` bash
# install dependencies
npm install

# serve with hot reload at localhost:8080
npm run dev

# build for production with minification
npm run build

# build for production and view the bundle analyzer report
npm run build --report

# run unit tests
npm run unit

# run e2e tests
npm run e2e

# run all tests
npm test
```

For a detailed explanation on how things work, check out the [guide](http://vuejs-templates.github.io/webpack/) and [docs for vue-loader](http://vuejs.github.io/vue-loader).
