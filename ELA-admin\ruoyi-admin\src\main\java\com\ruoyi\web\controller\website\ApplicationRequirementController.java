package com.ruoyi.web.controller.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.ApplicationRequirement;
import com.ruoyi.system.service.IApplicationRequirementService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 申请条件Controller
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
@RestController
@RequestMapping("/website/application-requirements")
public class ApplicationRequirementController extends BaseController
{
    @Autowired
    private IApplicationRequirementService applicationRequirementService;

    /**
     * 查询申请条件列表
     */
    @PreAuthorize("@ss.hasPermi('website:application-requirements:list')")
    @GetMapping("/list")
    public TableDataInfo list(ApplicationRequirement applicationRequirement)
    {
        startPage();
        List<ApplicationRequirement> list = applicationRequirementService.selectApplicationRequirementList(applicationRequirement);
        return getDataTable(list);
    }

    /**
     * 查询启用的申请条件列表（公开接口）
     */
    @GetMapping("/public/list")
    public AjaxResult getPublicList()
    {
        List<ApplicationRequirement> list = applicationRequirementService.selectEnabledApplicationRequirementList();
        return AjaxResult.success(list);
    }

    /**
     * 导出申请条件列表
     */
    @PreAuthorize("@ss.hasPermi('website:application-requirements:export')")
    @Log(title = "申请条件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ApplicationRequirement applicationRequirement)
    {
        List<ApplicationRequirement> list = applicationRequirementService.selectApplicationRequirementList(applicationRequirement);
        ExcelUtil<ApplicationRequirement> util = new ExcelUtil<ApplicationRequirement>(ApplicationRequirement.class);
        util.exportExcel(response, list, "申请条件数据");
    }

    /**
     * 获取申请条件详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:application-requirements:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(applicationRequirementService.selectApplicationRequirementById(id));
    }

    /**
     * 新增申请条件
     */
    @PreAuthorize("@ss.hasPermi('website:application-requirements:add')")
    @Log(title = "申请条件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ApplicationRequirement applicationRequirement)
    {
        return toAjax(applicationRequirementService.insertApplicationRequirement(applicationRequirement));
    }

    /**
     * 修改申请条件
     */
    @PreAuthorize("@ss.hasPermi('website:application-requirements:edit')")
    @Log(title = "申请条件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ApplicationRequirement applicationRequirement)
    {
        return toAjax(applicationRequirementService.updateApplicationRequirement(applicationRequirement));
    }

    /**
     * 删除申请条件
     */
    @PreAuthorize("@ss.hasPermi('website:application-requirements:remove')")
    @Log(title = "申请条件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(applicationRequirementService.deleteApplicationRequirementByIds(ids));
    }
} 