import request from '@/utils/request'

// 查询会员申请表列表
export function listMembershipApplication(query) {
  return request({
    url: '/website/membership-application/list',
    method: 'get',
    params: query
  })
}

// 查询会员申请表详细
export function getMembershipApplication(id) {
  return request({
    url: '/website/membership-application/' + id,
    method: 'get'
  })
}

// 新增会员申请表
export function addMembershipApplication(data) {
  return request({
    url: '/website/membership-application',
    method: 'post',
    data: data
  })
}

// 修改会员申请表
export function updateMembershipApplication(data) {
  return request({
    url: '/website/membership-application',
    method: 'put',
    data: data
  })
}

// 删除会员申请表
export function delMembershipApplication(id) {
  return request({
    url: '/website/membership-application/' + id,
    method: 'delete'
  })
}

// 导出会员申请表
export function exportMembershipApplication(query) {
  return request({
    url: '/website/membership-application/export',
    method: 'post',
    data: query
  })
} 