import request from '@/utils/request'

// 查询申请条件列表
export function listApplicationRequirements(query) {
  return request({
    url: '/website/application-requirements/list',
    method: 'get',
    params: query
  })
}

// 查询申请条件详细
export function getApplicationRequirement(id) {
  return request({
    url: '/website/application-requirements/' + id,
    method: 'get'
  })
}

// 新增申请条件
export function addApplicationRequirement(data) {
  return request({
    url: '/website/application-requirements',
    method: 'post',
    data: data
  })
}

// 修改申请条件
export function updateApplicationRequirement(data) {
  return request({
    url: '/website/application-requirements',
    method: 'put',
    data: data
  })
}

// 删除申请条件
export function delApplicationRequirement(id) {
  return request({
    url: '/website/application-requirements/' + id,
    method: 'delete'
  })
}

// 导出申请条件
export function exportApplicationRequirements(query) {
  return request({
    url: '/website/application-requirements/export',
    method: 'post',
    params: query
  })
} 