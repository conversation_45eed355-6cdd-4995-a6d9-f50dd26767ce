<template>
  <section class="content-intro section">
    <div class="container">
      <div class="intro-grid" :class="[`layout-${section.config.layout || 'split'}`]">
        
        <!-- 文本内容 -->
        <div class="intro-text">
          <div class="section-title">
             <h2>{{ section.config.title }}</h2>
             <p v-if="section.config.subtitle">{{ section.config.subtitle }}</p>
          </div>

          <div class="intro-content">
            <p 
              v-for="(paragraph, index) in section.config.content" 
              :key="index"
              class="intro-paragraph"
            >
              {{ paragraph }}
            </p>
          </div>
          
          <!-- 特性列表 -->
          <div class="features-grid" v-if="section.config.features">
            <div 
              v-for="feature in section.config.features" 
              :key="feature.id"
              class="feature-item"
            >
              <div class="feature-icon">
                <i :class="feature.icon"></i>
              </div>
              <div class="feature-content">
                <h3 class="feature-title">{{ feature.title }}</h3>
                <p class="feature-description">{{ feature.description }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 媒体内容 -->
        <div class="intro-media" v-if="section.config.mediaConfig && section.config.mediaConfig.content">
          <div class="media-container" :class="[`media-${section.config.mediaConfig.type}`]">
            <img 
              v-if="section.config.mediaConfig.type === 'image'"
              :src="section.config.mediaConfig.content"
              :alt="section.config.title"
              class="media-image"
            />
            
            <video 
              v-else-if="section.config.mediaConfig.type === 'video'"
              :src="section.config.mediaConfig.content"
              class="media-video"
              autoplay loop muted playsinline
            ></video>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'ContentIntro',
  props: {
    section: {
      type: Object,
      required: true
    }
  }
}
</script>

<style scoped>
.content-intro {
  /* 使用全局 section padding */
}

.intro-grid {
  display: grid;
  gap: 60px;
  align-items: center;
}

/* 布局类型 */
.layout-split {
  grid-template-columns: 1fr 1fr;
}

.layout-centered .intro-text,
.layout-full-width .intro-text {
  text-align: center;
}
.layout-centered .section-title,
.layout-full-width .section-title {
    margin-left: auto;
    margin-right: auto;
}

.intro-text .section-title {
  text-align: left;
  margin-bottom: 24px;
}

.intro-paragraph {
  font-size: 16px;
  line-height: 1.8;
  color: var(--text-dark);
  margin-bottom: 16px;
}

.intro-paragraph:last-child {
  margin-bottom: 0;
}

/* 特性网格 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin-top: 40px;
}

.feature-item {
  display: flex;
  gap: 20px;
  align-items: center;
  padding: 16px;
  background: rgba(18, 18, 36, 0.5);
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px) scale(1.02);
  background: rgba(18, 18, 36, 0.8);
  border-color: var(--primary-color);
  box-shadow: 0 0 20px rgba(0, 240, 255, 0.2);
}

.feature-icon {
  font-size: 24px;
  color: var(--primary-color);
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gradient-secondary);
  border-radius: 50%;
  color: white;
}

.feature-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-heading);
  margin-bottom: 4px;
}

.feature-description {
  font-size: 14px;
  color: var(--text-dark);
  line-height: 1.6;
  margin: 0;
}

/* 媒体容器 */
.intro-media {
  display: flex;
  justify-content: center;
  align-items: center;
}

.media-container {
  width: 100%;
  position: relative;
}

.media-image, .media-video {
  width: 100%;
  height: auto;
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-xl);
  object-fit: cover;
  aspect-ratio: 4/3;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .layout-split {
    grid-template-columns: 1fr;
  }
  
  .intro-text .section-title {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
  }

  .intro-media {
      order: -1;
  }
}

@media (max-width: 768px) {
  .features-grid {
    grid-template-columns: 1fr;
  }
}

</style> 