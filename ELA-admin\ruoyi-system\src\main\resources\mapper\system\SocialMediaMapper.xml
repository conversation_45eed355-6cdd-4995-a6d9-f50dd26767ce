<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SocialMediaMapper">
    
    <resultMap type="SocialMedia" id="SocialMediaResult">
        <result property="id"    column="id"    />
        <result property="platform"    column="platform"    />
        <result property="platformName"    column="platform_name"    />
        <result property="url"    column="url"    />
        <result property="icon"    column="icon"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSocialMediaVo">
        select id, platform, platform_name, url, icon, sort_order, status, create_by, create_time, update_by, update_time, remark from social_media
    </sql>

    <select id="selectSocialMediaList" parameterType="SocialMedia" resultMap="SocialMediaResult">
        <include refid="selectSocialMediaVo"/>
        <where>  
            <if test="platformName != null  and platformName != ''"> and platform_name like concat('%', #{platformName}, '%')</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectSocialMediaById" parameterType="Long" resultMap="SocialMediaResult">
        <include refid="selectSocialMediaVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSocialMedia" parameterType="SocialMedia" useGeneratedKeys="true" keyProperty="id">
        insert into social_media
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="platform != null and platform != ''">platform,</if>
            <if test="platformName != null and platformName != ''">platform_name,</if>
            <if test="url != null">url,</if>
            <if test="icon != null">icon,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="platform != null and platform != ''">#{platform},</if>
            <if test="platformName != null and platformName != ''">#{platformName},</if>
            <if test="url != null">#{url},</if>
            <if test="icon != null">#{icon},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSocialMedia" parameterType="SocialMedia">
        update social_media
        <trim prefix="SET" suffixOverrides=",">
            <if test="platform != null and platform != ''">platform = #{platform},</if>
            <if test="platformName != null and platformName != ''">platform_name = #{platformName},</if>
            <if test="url != null">url = #{url},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSocialMediaById" parameterType="Long">
        delete from social_media where id = #{id}
    </delete>

    <delete id="deleteSocialMediaByIds" parameterType="String">
        delete from social_media where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 