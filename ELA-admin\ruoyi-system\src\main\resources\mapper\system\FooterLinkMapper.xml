<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.FooterLinkMapper">
    
    <resultMap type="FooterLink" id="FooterLinkResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="url"    column="url"    />
        <result property="linkType"    column="link_type"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectFooterLinkVo">
        select id, name, url, link_type, sort_order, status, create_time, update_time, create_by, update_by, remark from footer_links
    </sql>

    <select id="selectFooterLinkList" parameterType="FooterLink" resultMap="FooterLinkResult">
        <include refid="selectFooterLinkVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="url != null  and url != ''"> and url like concat('%', #{url}, '%')</if>
            <if test="linkType != null  and linkType != ''"> and link_type = #{linkType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectFooterLinkById" parameterType="Long" resultMap="FooterLinkResult">
        <include refid="selectFooterLinkVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertFooterLink" parameterType="FooterLink" useGeneratedKeys="true" keyProperty="id">
        insert into footer_links
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="url != null and url != ''">url,</if>
            <if test="linkType != null">link_type,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="url != null and url != ''">#{url},</if>
            <if test="linkType != null">#{linkType},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateFooterLink" parameterType="FooterLink">
        update footer_links
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="url != null and url != ''">url = #{url},</if>
            <if test="linkType != null">link_type = #{linkType},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFooterLinkById" parameterType="Long">
        delete from footer_links where id = #{id}
    </delete>

    <delete id="deleteFooterLinkByIds" parameterType="String">
        delete from footer_links where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 