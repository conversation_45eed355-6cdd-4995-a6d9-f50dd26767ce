<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ActivityMapper">
    
    <resultMap type="Activity" id="ActivityResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="titleEn"    column="title_en"    />
        <result property="coverImageUrl"    column="cover_image_url"    />
        <result property="contentImages"    column="content_images"    typeHandler="com.ruoyi.framework.config.JsonListTypeHandler"/>
        <result property="content"    column="content"    />
        <result property="contentEn"    column="content_en"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="externalUrl"    column="external_url"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="showOnHomepage"    column="show_on_homepage"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectActivityVo">
        select id, title, title_en, cover_image_url, content_images, content, content_en, start_time, end_time, external_url, sort_order, status, show_on_homepage, create_by, create_time, update_by, update_time, remark from activities
    </sql>

    <select id="selectActivityList" parameterType="Activity" resultMap="ActivityResult">
        <include refid="selectActivityVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="titleEn != null  and titleEn != ''"> and title_en like concat('%', #{titleEn}, '%')</if>
            <if test="startTime != null "> and start_time &gt;= #{startTime}</if>
            <if test="endTime != null "> and end_time &lt;= #{endTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="showOnHomepage != null  and showOnHomepage != ''"> and show_on_homepage = #{showOnHomepage}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectActivityById" parameterType="Long" resultMap="ActivityResult">
        <include refid="selectActivityVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertActivity" parameterType="Activity" useGeneratedKeys="true" keyProperty="id">
        insert into activities
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="titleEn != null">title_en,</if>
            <if test="coverImageUrl != null">cover_image_url,</if>
            <if test="contentImages != null">content_images,</if>
            <if test="content != null">content,</if>
            <if test="contentEn != null">content_en,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="externalUrl != null">external_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="showOnHomepage != null">show_on_homepage,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="titleEn != null">#{titleEn},</if>
            <if test="coverImageUrl != null">#{coverImageUrl},</if>
            <if test="contentImages != null">#{contentImages,typeHandler=com.ruoyi.framework.config.JsonListTypeHandler},</if>
            <if test="content != null">#{content},</if>
            <if test="contentEn != null">#{contentEn},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="externalUrl != null">#{externalUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="showOnHomepage != null">#{showOnHomepage},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateActivity" parameterType="Activity">
        update activities
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="titleEn != null">title_en = #{titleEn},</if>
            <if test="coverImageUrl != null">cover_image_url = #{coverImageUrl},</if>
            <if test="contentImages != null">content_images = #{contentImages,typeHandler=com.ruoyi.framework.config.JsonListTypeHandler},</if>
            <if test="content != null">content = #{content},</if>
            <if test="contentEn != null">content_en = #{contentEn},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="externalUrl != null">external_url = #{externalUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="showOnHomepage != null">show_on_homepage = #{showOnHomepage},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteActivityById" parameterType="Long">
        delete from activities where id = #{id}
    </delete>

    <delete id="deleteActivityByIds" parameterType="String">
        delete from activities where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>