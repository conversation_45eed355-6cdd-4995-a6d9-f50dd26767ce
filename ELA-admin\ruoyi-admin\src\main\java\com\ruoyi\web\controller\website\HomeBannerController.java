package com.ruoyi.web.controller.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HomeBanner;
import com.ruoyi.system.service.IHomeBannerService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 首页BannerController
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/website/home/<USER>")
public class HomeBannerController extends BaseController
{
    @Autowired
    private IHomeBannerService homeBannerService;

    /**
     * 查询首页Banner列表
     */
    @PreAuthorize("@ss.hasPermi('website:home:banner:list')")
    @GetMapping("/list")
    public TableDataInfo list(HomeBanner homeBanner)
    {
        startPage();
        List<HomeBanner> list = homeBannerService.selectHomeBannerList(homeBanner);
        return getDataTable(list);
    }

    /**
     * 导出首页Banner列表
     */
    @PreAuthorize("@ss.hasPermi('website:home:banner:export')")
    @Log(title = "首页Banner", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HomeBanner homeBanner)
    {
        List<HomeBanner> list = homeBannerService.selectHomeBannerList(homeBanner);
        ExcelUtil<HomeBanner> util = new ExcelUtil<HomeBanner>(HomeBanner.class);
        util.exportExcel(response, list, "首页Banner数据");
    }

    /**
     * 获取首页Banner详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:home:banner:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(homeBannerService.selectHomeBannerById(id));
    }

    /**
     * 新增首页Banner
     */
    @PreAuthorize("@ss.hasPermi('website:home:banner:add')")
    @Log(title = "首页Banner", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeBanner homeBanner)
    {
        return toAjax(homeBannerService.insertHomeBanner(homeBanner));
    }

    /**
     * 修改首页Banner
     */
    @PreAuthorize("@ss.hasPermi('website:home:banner:edit')")
    @Log(title = "首页Banner", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeBanner homeBanner)
    {
        return toAjax(homeBannerService.updateHomeBanner(homeBanner));
    }

    /**
     * 删除首页Banner
     */
    @PreAuthorize("@ss.hasPermi('website:home:banner:remove')")
    @Log(title = "首页Banner", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(homeBannerService.deleteHomeBannerByIds(ids));
    }
} 