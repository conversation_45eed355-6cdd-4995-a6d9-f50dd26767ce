import request from '@/utils/request'

// 查询会员权益列表
export function listMemberBenefits(query) {
  return request({
    url: '/website/member-benefits/list',
    method: 'get',
    params: query
  })
}

// 查询会员权益详细
export function getMemberBenefits(id) {
  return request({
    url: '/website/member-benefits/' + id,
    method: 'get'
  })
}

// 新增会员权益
export function addMemberBenefits(data) {
  return request({
    url: '/website/member-benefits',
    method: 'post',
    data: data
  })
}

// 修改会员权益
export function updateMemberBenefits(data) {
  return request({
    url: '/website/member-benefits',
    method: 'put',
    data: data
  })
}

// 删除会员权益
export function delMemberBenefits(id) {
  return request({
    url: '/website/member-benefits/' + id,
    method: 'delete'
  })
}

// 导出会员权益
export function exportMemberBenefits(query) {
  return request({
    url: '/website/member-benefits/export',
    method: 'post',
    data: query
  })
}

// 获取启用的会员权益列表(供前端使用)
export function getPublicMemberBenefits() {
  return request({
    url: '/website/member-benefits/public',
    method: 'get'
  })
} 