-- 网站Banner表
DROP TABLE IF EXISTS `website_banner`;
CREATE TABLE `website_banner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title_zh` varchar(200) DEFAULT NULL COMMENT 'Banner标题(中文)',
  `title_en` varchar(200) DEFAULT NULL COMMENT 'Banner标题(英文)',
  `subtitle_zh` varchar(500) DEFAULT NULL COMMENT 'Banner副标题(中文)',
  `subtitle_en` varchar(500) DEFAULT NULL COMMENT 'Banner副标题(英文)',
  `image_url` varchar(500) DEFAULT NULL COMMENT 'Banner图片URL',
  `link_url` varchar(500) DEFAULT NULL COMMENT '链接地址',
  `sort_order` int(4) DEFAULT '0' COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `show_button` char(1) DEFAULT '0' COMMENT '是否显示按钮（0不显示 1显示）',
  `button_text_zh` varchar(50) DEFAULT NULL COMMENT '按钮文字(中文)',
  `button_text_en` varchar(50) DEFAULT NULL COMMENT '按钮文字(英文)',
  `button_link` varchar(500) DEFAULT NULL COMMENT '按钮链接',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='网站Banner表';

-- 插入示例数据
INSERT INTO `website_banner` VALUES 
(1, '推動香港成為全球電商物流樞紐', 'Driving Hong Kong as Global E-commerce Hub', '匯聚業界精英，推動行業發展，培育專業人才，建設可持續的電商物流生態系統', 'Uniting industry leaders, driving sector growth, nurturing professionals, building sustainable e-commerce logistics ecosystem', '/profile/upload/2024/01/01/banner1.jpg', '/about', 1, '0', '1', '加入我們', 'Join Us', '/join-us', 'admin', NOW(), 'admin', NOW(), '首页主Banner'),
(2, '專業服務，卓越品質', 'Professional Service, Excellence Quality', '致力於為客戶提供卓越的產品和服務', 'Committed to providing outstanding products and services', '/profile/upload/2024/01/01/banner2.jpg', '/services', 2, '0', '1', '了解更多', 'Learn More', '/services', 'admin', NOW(), 'admin', NOW(), '服务介绍Banner'); 