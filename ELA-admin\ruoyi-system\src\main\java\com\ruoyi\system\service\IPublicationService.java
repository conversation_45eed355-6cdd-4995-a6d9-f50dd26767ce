package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.Publication;

/**
 * 资讯管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
public interface IPublicationService 
{
    /**
     * 查询资讯管理
     * 
     * @param id 资讯管理主键
     * @return 资讯管理
     */
    public Publication selectPublicationById(Long id);

    /**
     * 查询资讯管理列表
     * 
     * @param publication 资讯管理
     * @return 资讯管理集合
     */
    public List<Publication> selectPublicationList(Publication publication);

    /**
     * 新增资讯管理
     * 
     * @param publication 资讯管理
     * @return 结果
     */
    public int insertPublication(Publication publication);

    /**
     * 修改资讯管理
     * 
     * @param publication 资讯管理
     * @return 结果
     */
    public int updatePublication(Publication publication);

    /**
     * 批量删除资讯管理
     * 
     * @param ids 需要删除的资讯管理主键集合
     * @return 结果
     */
    public int deletePublicationByIds(Long[] ids);

    /**
     * 删除资讯管理信息
     * 
     * @param id 资讯管理主键
     * @return 结果
     */
    public int deletePublicationById(Long id);
}