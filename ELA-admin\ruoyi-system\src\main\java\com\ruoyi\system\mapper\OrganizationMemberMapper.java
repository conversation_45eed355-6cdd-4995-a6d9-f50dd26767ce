package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.OrganizationMember;

/**
 * 组织架构成员Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface OrganizationMemberMapper 
{
    /**
     * 查询组织架构成员
     * 
     * @param id 组织架构成员主键
     * @return 组织架构成员
     */
    public OrganizationMember selectOrganizationMemberById(Long id);

    /**
     * 查询组织架构成员列表
     * 
     * @param organizationMember 组织架构成员
     * @return 组织架构成员集合
     */
    public List<OrganizationMember> selectOrganizationMemberList(OrganizationMember organizationMember);

    /**
     * 新增组织架构成员
     * 
     * @param organizationMember 组织架构成员
     * @return 结果
     */
    public int insertOrganizationMember(OrganizationMember organizationMember);

    /**
     * 修改组织架构成员
     * 
     * @param organizationMember 组织架构成员
     * @return 结果
     */
    public int updateOrganizationMember(OrganizationMember organizationMember);

    /**
     * 删除组织架构成员
     * 
     * @param id 组织架构成员主键
     * @return 结果
     */
    public int deleteOrganizationMemberById(Long id);

    /**
     * 批量删除组织架构成员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrganizationMemberByIds(Long[] ids);
} 