package com.ruoyi.web.controller.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.FooterLink;
import com.ruoyi.system.service.IFooterLinkService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 页脚链接Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/website/footerLink")
public class FooterLinkController extends BaseController
{
    @Autowired
    private IFooterLinkService footerLinkService;

    /**
     * 查询页脚链接列表
     */
    @PreAuthorize("@ss.hasPermi('website:footerLink:list')")
    @GetMapping("/list")
    public TableDataInfo list(FooterLink footerLink)
    {
        startPage();
        List<FooterLink> list = footerLinkService.selectFooterLinkList(footerLink);
        return getDataTable(list);
    }

    /**
     * 导出页脚链接列表
     */
    @PreAuthorize("@ss.hasPermi('website:footerLink:export')")
    @Log(title = "页脚链接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FooterLink footerLink)
    {
        List<FooterLink> list = footerLinkService.selectFooterLinkList(footerLink);
        ExcelUtil<FooterLink> util = new ExcelUtil<FooterLink>(FooterLink.class);
        util.exportExcel(response, list, "页脚链接数据");
    }

    /**
     * 获取页脚链接详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:footerLink:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(footerLinkService.selectFooterLinkById(id));
    }

    /**
     * 新增页脚链接
     */
    @PreAuthorize("@ss.hasPermi('website:footerLink:add')")
    @Log(title = "页脚链接", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FooterLink footerLink)
    {
        return toAjax(footerLinkService.insertFooterLink(footerLink));
    }

    /**
     * 修改页脚链接
     */
    @PreAuthorize("@ss.hasPermi('website:footerLink:edit')")
    @Log(title = "页脚链接", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FooterLink footerLink)
    {
        return toAjax(footerLinkService.updateFooterLink(footerLink));
    }

    /**
     * 删除页脚链接
     */
    @PreAuthorize("@ss.hasPermi('website:footerLink:remove')")
    @Log(title = "页脚链接", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(footerLinkService.deleteFooterLinkByIds(ids));
    }
} 