<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HeroContentMapper">
    
    <resultMap type="HeroContent" id="HeroContentResult">
        <result property="id"    column="id"    />
        <result property="badgeZh"    column="badge_zh"    />
        <result property="badgeEn"    column="badge_en"    />
        <result property="titleZh"    column="title_zh"    />
        <result property="titleEn"    column="title_en"    />
        <result property="subtitleZh"    column="subtitle_zh"    />
        <result property="subtitleEn"    column="subtitle_en"    />
        <result property="buttonsConfig"    column="buttons_config"    />
        <result property="statsConfig"    column="stats_config"    />
        <result property="status"    column="status"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectHeroContentVo">
        select id, badge_zh, badge_en, title_zh, title_en, subtitle_zh, subtitle_en, buttons_config, stats_config, status, sort, create_by, create_time, update_by, update_time, remark from hero_content
    </sql>

    <select id="selectHeroContentList" parameterType="HeroContent" resultMap="HeroContentResult">
        <include refid="selectHeroContentVo"/>
        <where>  
            <if test="badgeZh != null  and badgeZh != ''"> and badge_zh like concat('%', #{badgeZh}, '%')</if>
            <if test="badgeEn != null  and badgeEn != ''"> and badge_en like concat('%', #{badgeEn}, '%')</if>
            <if test="titleZh != null  and titleZh != ''"> and title_zh like concat('%', #{titleZh}, '%')</if>
            <if test="titleEn != null  and titleEn != ''"> and title_en like concat('%', #{titleEn}, '%')</if>
            <if test="subtitleZh != null  and subtitleZh != ''"> and subtitle_zh like concat('%', #{subtitleZh}, '%')</if>
            <if test="subtitleEn != null  and subtitleEn != ''"> and subtitle_en like concat('%', #{subtitleEn}, '%')</if>
            <if test="buttonsConfig != null  and buttonsConfig != ''"> and buttons_config like concat('%', #{buttonsConfig}, '%')</if>
            <if test="statsConfig != null  and statsConfig != ''"> and stats_config like concat('%', #{statsConfig}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
        order by sort asc, create_time desc
    </select>
    
    <select id="selectHeroContentById" parameterType="Long" resultMap="HeroContentResult">
        <include refid="selectHeroContentVo"/>
        where id = #{id}
    </select>

    <select id="selectEnabledHeroContent" resultMap="HeroContentResult">
        <include refid="selectHeroContentVo"/>
        where status = '0'
        order by sort asc, create_time desc
        limit 1
    </select>
        
    <insert id="insertHeroContent" parameterType="HeroContent" useGeneratedKeys="true" keyProperty="id">
        insert into hero_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="badgeZh != null and badgeZh != ''">badge_zh,</if>
            <if test="badgeEn != null and badgeEn != ''">badge_en,</if>
            <if test="titleZh != null and titleZh != ''">title_zh,</if>
            <if test="titleEn != null and titleEn != ''">title_en,</if>
            <if test="subtitleZh != null and subtitleZh != ''">subtitle_zh,</if>
            <if test="subtitleEn != null and subtitleEn != ''">subtitle_en,</if>
            <if test="buttonsConfig != null and buttonsConfig != ''">buttons_config,</if>
            <if test="statsConfig != null and statsConfig != ''">stats_config,</if>
            <if test="status != null">status,</if>
            <if test="sort != null">sort,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="badgeZh != null and badgeZh != ''">#{badgeZh},</if>
            <if test="badgeEn != null and badgeEn != ''">#{badgeEn},</if>
            <if test="titleZh != null and titleZh != ''">#{titleZh},</if>
            <if test="titleEn != null and titleEn != ''">#{titleEn},</if>
            <if test="subtitleZh != null and subtitleZh != ''">#{subtitleZh},</if>
            <if test="subtitleEn != null and subtitleEn != ''">#{subtitleEn},</if>
            <if test="buttonsConfig != null and buttonsConfig != ''">#{buttonsConfig},</if>
            <if test="statsConfig != null and statsConfig != ''">#{statsConfig},</if>
            <if test="status != null">#{status},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
         </trim>
    </insert>

    <update id="updateHeroContent" parameterType="HeroContent">
        update hero_content
        <trim prefix="SET" suffixOverrides=",">
            <if test="badgeZh != null and badgeZh != ''">badge_zh = #{badgeZh},</if>
            <if test="badgeEn != null and badgeEn != ''">badge_en = #{badgeEn},</if>
            <if test="titleZh != null and titleZh != ''">title_zh = #{titleZh},</if>
            <if test="titleEn != null and titleEn != ''">title_en = #{titleEn},</if>
            <if test="subtitleZh != null and subtitleZh != ''">subtitle_zh = #{subtitleZh},</if>
            <if test="subtitleEn != null and subtitleEn != ''">subtitle_en = #{subtitleEn},</if>
            <if test="buttonsConfig != null and buttonsConfig != ''">buttons_config = #{buttonsConfig},</if>
            <if test="statsConfig != null and statsConfig != ''">stats_config = #{statsConfig},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHeroContentById" parameterType="Long">
        delete from hero_content where id = #{id}
    </delete>

    <delete id="deleteHeroContentByIds" parameterType="String">
        delete from hero_content where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 