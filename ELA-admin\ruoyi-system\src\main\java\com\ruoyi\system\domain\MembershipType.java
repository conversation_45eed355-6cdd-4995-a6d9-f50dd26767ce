package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 会员类型对象 membership_types
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
public class MembershipType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 会员类型ID */
    private Long id;

    /** 会员类型代码 */
    @Excel(name = "会员类型代码")
    private String typeCode;

    /** 中文名称 */
    @Excel(name = "中文名称")
    private String nameZh;

    /** 英文名称 */
    @Excel(name = "英文名称")
    private String nameEn;

    /** 中文描述 */
    @Excel(name = "中文描述")
    private String descriptionZh;

    /** 英文描述 */
    @Excel(name = "英文描述")
    private String descriptionEn;

    /** 年费 */
    @Excel(name = "年费")
    private BigDecimal annualFee;

    /** 图标 */
    @Excel(name = "图标")
    private String icon;

    /** 中文权益列表 */
    private List<String> benefitsZh;

    /** 英文权益列表 */
    private List<String> benefitsEn;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTypeCode(String typeCode) 
    {
        this.typeCode = typeCode;
    }

    public String getTypeCode() 
    {
        return typeCode;
    }
    public void setNameZh(String nameZh) 
    {
        this.nameZh = nameZh;
    }

    public String getNameZh() 
    {
        return nameZh;
    }
    public void setNameEn(String nameEn) 
    {
        this.nameEn = nameEn;
    }

    public String getNameEn() 
    {
        return nameEn;
    }
    public void setDescriptionZh(String descriptionZh) 
    {
        this.descriptionZh = descriptionZh;
    }

    public String getDescriptionZh() 
    {
        return descriptionZh;
    }
    public void setDescriptionEn(String descriptionEn) 
    {
        this.descriptionEn = descriptionEn;
    }

    public String getDescriptionEn() 
    {
        return descriptionEn;
    }
    public void setAnnualFee(BigDecimal annualFee) 
    {
        this.annualFee = annualFee;
    }

    public BigDecimal getAnnualFee() 
    {
        return annualFee;
    }
    public void setIcon(String icon) 
    {
        this.icon = icon;
    }

    public String getIcon() 
    {
        return icon;
    }
    public void setBenefitsZh(List<String> benefitsZh) 
    {
        this.benefitsZh = benefitsZh;
    }

    public List<String> getBenefitsZh() 
    {
        return benefitsZh;
    }
    public void setBenefitsEn(List<String> benefitsEn) 
    {
        this.benefitsEn = benefitsEn;
    }

    public List<String> getBenefitsEn() 
    {
        return benefitsEn;
    }
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("typeCode", getTypeCode())
            .append("nameZh", getNameZh())
            .append("nameEn", getNameEn())
            .append("descriptionZh", getDescriptionZh())
            .append("descriptionEn", getDescriptionEn())
            .append("annualFee", getAnnualFee())
            .append("icon", getIcon())
            .append("benefitsZh", getBenefitsZh())
            .append("benefitsEn", getBenefitsEn())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 