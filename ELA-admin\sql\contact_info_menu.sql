-- 联系信息管理菜单
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('联系信息管理', '2000', '1', 'contact-info', 'website/contactInfo/index', 1, 0, 'C', '0', '0', 'website:contactInfo:list', 'fa fa-address-book', 'admin', sysdate(), '', null, '联系信息管理菜单');
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('联系信息查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'website:contactInfo:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('联系信息新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'website:contactInfo:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('联系信息修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'website:contactInfo:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('联系信息删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'website:contactInfo:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('联系信息导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'website:contactInfo:export',       '#', 'admin', sysdate(), '', null, '');

-- 社交媒体管理菜单
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('社交媒体管理', '2000', '2', 'social-media', 'website/socialMedia/index', 1, 0, 'C', '0', '0', 'website:socialMedia:list', 'fa fa-share-alt', 'admin', sysdate(), '', null, '社交媒体管理菜单');
SELECT @parentId := LAST_INSERT_ID();

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('社交媒体查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'website:socialMedia:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('社交媒体新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'website:socialMedia:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('社交媒体修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'website:socialMedia:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('社交媒体删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'website:socialMedia:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('社交媒体导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'website:socialMedia:export',       '#', 'admin', sysdate(), '', null, ''); 