package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 页脚配置对象 footer_config
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class FooterConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long id;

    /** 区域类型（main_links, address, qr_code, copyright） */
    @Excel(name = "区域类型")
    private String sectionType;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 内容（JSON格式） */
    @Excel(name = "内容")
    private String content;

    /** 背景颜色 */
    @Excel(name = "背景颜色")
    private String backgroundColor;

    /** 文字颜色 */
    @Excel(name = "文字颜色")
    private String textColor;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 状态（0启用 1禁用） */
    @Excel(name = "状态", readConverterExp = "0=启用,1=禁用")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setSectionType(String sectionType) 
    {
        this.sectionType = sectionType;
    }

    public String getSectionType() 
    {
        return sectionType;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setBackgroundColor(String backgroundColor) 
    {
        this.backgroundColor = backgroundColor;
    }

    public String getBackgroundColor() 
    {
        return backgroundColor;
    }
    public void setTextColor(String textColor) 
    {
        this.textColor = textColor;
    }

    public String getTextColor() 
    {
        return textColor;
    }
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sectionType", getSectionType())
            .append("title", getTitle())
            .append("content", getContent())
            .append("backgroundColor", getBackgroundColor())
            .append("textColor", getTextColor())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("remark", getRemark())
            .toString();
    }
} 