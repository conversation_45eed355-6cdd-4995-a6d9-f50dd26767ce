<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.LeadershipMessageMapper">
    
    <resultMap type="LeadershipMessage" id="LeadershipMessageResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="titleZh"    column="title_zh"    />
        <result property="titleEn"    column="title_en"    />
        <result property="nameZh"    column="name_zh"    />
        <result property="nameEn"    column="name_en"    />
        <result property="positionZh"    column="position_zh"    />
        <result property="positionEn"    column="position_en"    />
        <result property="companyZh"    column="company_zh"    />
        <result property="companyEn"    column="company_en"    />
        <result property="messageZh"    column="message_zh"    />
        <result property="messageEn"    column="message_en"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="backgroundImageUrl"    column="background_image_url"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectLeadershipMessageVo">
        select id, type, title_zh, title_en, name_zh, name_en, position_zh, position_en, company_zh, company_en, message_zh, message_en, image_url, background_image_url, sort_order, status, create_by, create_time, update_by, update_time, remark from leadership_messages
    </sql>

    <select id="selectLeadershipMessageList" parameterType="LeadershipMessage" resultMap="LeadershipMessageResult">
        <include refid="selectLeadershipMessageVo"/>
        <where>  
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="titleZh != null  and titleZh != ''"> and title_zh like concat('%', #{titleZh}, '%')</if>
            <if test="titleEn != null  and titleEn != ''"> and title_en like concat('%', #{titleEn}, '%')</if>
            <if test="nameZh != null  and nameZh != ''"> and name_zh like concat('%', #{nameZh}, '%')</if>
            <if test="nameEn != null  and nameEn != ''"> and name_en like concat('%', #{nameEn}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectEnabledLeadershipMessageList" resultMap="LeadershipMessageResult">
        <include refid="selectLeadershipMessageVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectLeadershipMessageById" parameterType="Long" resultMap="LeadershipMessageResult">
        <include refid="selectLeadershipMessageVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLeadershipMessage" parameterType="LeadershipMessage" useGeneratedKeys="true" keyProperty="id">
        insert into leadership_messages
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null and type != ''">type,</if>
            <if test="titleZh != null and titleZh != ''">title_zh,</if>
            <if test="titleEn != null and titleEn != ''">title_en,</if>
            <if test="nameZh != null and nameZh != ''">name_zh,</if>
            <if test="nameEn != null and nameEn != ''">name_en,</if>
            <if test="positionZh != null and positionZh != ''">position_zh,</if>
            <if test="positionEn != null and positionEn != ''">position_en,</if>
            <if test="companyZh != null">company_zh,</if>
            <if test="companyEn != null">company_en,</if>
            <if test="messageZh != null and messageZh != ''">message_zh,</if>
            <if test="messageEn != null and messageEn != ''">message_en,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="backgroundImageUrl != null">background_image_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null and type != ''">#{type},</if>
            <if test="titleZh != null and titleZh != ''">#{titleZh},</if>
            <if test="titleEn != null and titleEn != ''">#{titleEn},</if>
            <if test="nameZh != null and nameZh != ''">#{nameZh},</if>
            <if test="nameEn != null and nameEn != ''">#{nameEn},</if>
            <if test="positionZh != null and positionZh != ''">#{positionZh},</if>
            <if test="positionEn != null and positionEn != ''">#{positionEn},</if>
            <if test="companyZh != null">#{companyZh},</if>
            <if test="companyEn != null">#{companyEn},</if>
            <if test="messageZh != null and messageZh != ''">#{messageZh},</if>
            <if test="messageEn != null and messageEn != ''">#{messageEn},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="backgroundImageUrl != null">#{backgroundImageUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateLeadershipMessage" parameterType="LeadershipMessage">
        update leadership_messages
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="titleZh != null and titleZh != ''">title_zh = #{titleZh},</if>
            <if test="titleEn != null and titleEn != ''">title_en = #{titleEn},</if>
            <if test="nameZh != null and nameZh != ''">name_zh = #{nameZh},</if>
            <if test="nameEn != null and nameEn != ''">name_en = #{nameEn},</if>
            <if test="positionZh != null and positionZh != ''">position_zh = #{positionZh},</if>
            <if test="positionEn != null and positionEn != ''">position_en = #{positionEn},</if>
            <if test="companyZh != null">company_zh = #{companyZh},</if>
            <if test="companyEn != null">company_en = #{companyEn},</if>
            <if test="messageZh != null and messageZh != ''">message_zh = #{messageZh},</if>
            <if test="messageEn != null and messageEn != ''">message_en = #{messageEn},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="backgroundImageUrl != null">background_image_url = #{backgroundImageUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeadershipMessageById" parameterType="Long">
        delete from leadership_messages where id = #{id}
    </delete>

    <delete id="deleteLeadershipMessageByIds" parameterType="String">
        delete from leadership_messages where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 