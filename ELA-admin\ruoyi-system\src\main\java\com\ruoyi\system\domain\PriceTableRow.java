package com.ruoyi.system.domain;

// PriceTableRow.java
/**
 * 代表价格表中的一行数据。
 * 注意：这是一个通用模型，根据不同的表，某些字段可能为null。
 */
public class PriceTableRow {
    // 通用字段
    private String destination; // 目的地，如 "美西"
    private String zipCodeRange; // 邮编范围，如 "8, 9"
    private String warehouseCode; // 仓库代码/分区，如 "ONT8, LGB8"
    private String channelCode; // 渠道代码，如 "PO海空联运"
    private String notes; // 备注信息
    private String scheduleAndTransitTime; // 船期/时效
    private String referenceTransitTime; // 参考时效

    // 价格字段 (根据渠道不同，可能有多种)

    // 美国海派 (Air Express) 专线价格
    private Double limitSpeedDPrice12To70; // 限时达-D价 (12-70KG)
    private Double limitSpeedDPrice71Plus; // 限时达-D价 (71KG+)
    private Double clxAirPrice12To70; // 海派CLX-A价 (12-70KG)
    private Double clxAirPrice71Plus; // 海派CLX-A价 (71KG+)
    private Double maxAirPrice12To70; // MAX-B价 (12-70KG)
    private Double maxAirPrice71Plus; // MAX-B价 (71KG+)
    private Double heDeAirPrice12To70; // 合德快提-C价 (12-70KG)
    private Double heDeAirPrice71Plus; // 合德快提-C价 (71KG+)
    private Double oaAirPrice12To70; // OA普船-E价 (12-70KG)
    private Double oaAirPrice71Plus; // OA普船-E价 (71KG+)

    // 美国海卡 (Truck) 专线价格
    private Double clxTruckPrice; // CLX A价
    private Double maxTruckPrice; // MAX B价
    private Double heDeTruckPrice; // 合德快提 C价
    private Double oaTruckPrice; // OA普船 E价

    // 普船T (整柜直送) 价格
    private Double oaTPrice; // OA普船T价 / 通用价格

    // 远程地区专线价格 (波多黎各/夏威夷等)
    private Double price100To299; // 100-299KG
    private Double price300To499; // 300-499KG
    private Double price500To799; // 500-799KG
    private Double price800To999; // 800-999KG
    private String price1000Plus; // 1000KG+ (可能为"单询")

    // 美国空派专线价格
    private Double clxAirExpressPrice12To20; // 12KG-20KG
    private Double clxAirExpressPrice21To70; // 21-70KG
    private Double clxAirExpressPrice71To100; // 71KG-100KG
    private Double clxAirExpressPrice101Plus; // 101KG+

    // Getters and Setters (为简洁起见，此处省略所有getter和setter方法)
    // 在实际项目中，您需要为每个字段编写完整的getter和setter。

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public String getZipCodeRange() {
        return zipCodeRange;
    }

    public void setZipCodeRange(String zipCodeRange) {
        this.zipCodeRange = zipCodeRange;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getScheduleAndTransitTime() {
        return scheduleAndTransitTime;
    }

    public void setScheduleAndTransitTime(String scheduleAndTransitTime) {
        this.scheduleAndTransitTime = scheduleAndTransitTime;
    }

    public String getReferenceTransitTime() {
        return referenceTransitTime;
    }

    public void setReferenceTransitTime(String referenceTransitTime) {
        this.referenceTransitTime = referenceTransitTime;
    }

    public Double getLimitSpeedDPrice12To70() {
        return limitSpeedDPrice12To70;
    }

    public void setLimitSpeedDPrice12To70(Double limitSpeedDPrice12To70) {
        this.limitSpeedDPrice12To70 = limitSpeedDPrice12To70;
    }

    public Double getLimitSpeedDPrice71Plus() {
        return limitSpeedDPrice71Plus;
    }

    public void setLimitSpeedDPrice71Plus(Double limitSpeedDPrice71Plus) {
        this.limitSpeedDPrice71Plus = limitSpeedDPrice71Plus;
    }

    public Double getClxAirPrice12To70() {
        return clxAirPrice12To70;
    }

    public void setClxAirPrice12To70(Double clxAirPrice12To70) {
        this.clxAirPrice12To70 = clxAirPrice12To70;
    }

    public Double getClxAirPrice71Plus() {
        return clxAirPrice71Plus;
    }

    public void setClxAirPrice71Plus(Double clxAirPrice71Plus) {
        this.clxAirPrice71Plus = clxAirPrice71Plus;
    }

    public Double getMaxAirPrice12To70() {
        return maxAirPrice12To70;
    }

    public void setMaxAirPrice12To70(Double maxAirPrice12To70) {
        this.maxAirPrice12To70 = maxAirPrice12To70;
    }

    public Double getMaxAirPrice71Plus() {
        return maxAirPrice71Plus;
    }

    public void setMaxAirPrice71Plus(Double maxAirPrice71Plus) {
        this.maxAirPrice71Plus = maxAirPrice71Plus;
    }

    public Double getHeDeAirPrice12To70() {
        return heDeAirPrice12To70;
    }

    public void setHeDeAirPrice12To70(Double heDeAirPrice12To70) {
        this.heDeAirPrice12To70 = heDeAirPrice12To70;
    }

    public Double getHeDeAirPrice71Plus() {
        return heDeAirPrice71Plus;
    }

    public void setHeDeAirPrice71Plus(Double heDeAirPrice71Plus) {
        this.heDeAirPrice71Plus = heDeAirPrice71Plus;
    }

    public Double getOaAirPrice12To70() {
        return oaAirPrice12To70;
    }

    public void setOaAirPrice12To70(Double oaAirPrice12To70) {
        this.oaAirPrice12To70 = oaAirPrice12To70;
    }

    public Double getOaAirPrice71Plus() {
        return oaAirPrice71Plus;
    }

    public void setOaAirPrice71Plus(Double oaAirPrice71Plus) {
        this.oaAirPrice71Plus = oaAirPrice71Plus;
    }

    public Double getClxTruckPrice() {
        return clxTruckPrice;
    }

    public void setClxTruckPrice(Double clxTruckPrice) {
        this.clxTruckPrice = clxTruckPrice;
    }

    public Double getMaxTruckPrice() {
        return maxTruckPrice;
    }

    public void setMaxTruckPrice(Double maxTruckPrice) {
        this.maxTruckPrice = maxTruckPrice;
    }

    public Double getHeDeTruckPrice() {
        return heDeTruckPrice;
    }

    public void setHeDeTruckPrice(Double heDeTruckPrice) {
        this.heDeTruckPrice = heDeTruckPrice;
    }

    public Double getOaTruckPrice() {
        return oaTruckPrice;
    }

    public void setOaTruckPrice(Double oaTruckPrice) {
        this.oaTruckPrice = oaTruckPrice;
    }

    public Double getOaTPrice() {
        return oaTPrice;
    }

    public void setOaTPrice(Double oaTPrice) {
        this.oaTPrice = oaTPrice;
    }

    public Double getPrice100To299() {
        return price100To299;
    }

    public void setPrice100To299(Double price100To299) {
        this.price100To299 = price100To299;
    }

    public Double getPrice300To499() {
        return price300To499;
    }

    public void setPrice300To499(Double price300To499) {
        this.price300To499 = price300To499;
    }

    public Double getPrice500To799() {
        return price500To799;
    }

    public void setPrice500To799(Double price500To799) {
        this.price500To799 = price500To799;
    }

    public Double getPrice800To999() {
        return price800To999;
    }

    public void setPrice800To999(Double price800To999) {
        this.price800To999 = price800To999;
    }

    public String getPrice1000Plus() {
        return price1000Plus;
    }

    public void setPrice1000Plus(String price1000Plus) {
        this.price1000Plus = price1000Plus;
    }

    public Double getClxAirExpressPrice12To20() {
        return clxAirExpressPrice12To20;
    }

    public void setClxAirExpressPrice12To20(Double clxAirExpressPrice12To20) {
        this.clxAirExpressPrice12To20 = clxAirExpressPrice12To20;
    }

    public Double getClxAirExpressPrice21To70() {
        return clxAirExpressPrice21To70;
    }

    public void setClxAirExpressPrice21To70(Double clxAirExpressPrice21To70) {
        this.clxAirExpressPrice21To70 = clxAirExpressPrice21To70;
    }

    public Double getClxAirExpressPrice71To100() {
        return clxAirExpressPrice71To100;
    }

    public void setClxAirExpressPrice71To100(Double clxAirExpressPrice71To100) {
        this.clxAirExpressPrice71To100 = clxAirExpressPrice71To100;
    }

    public Double getClxAirExpressPrice101Plus() {
        return clxAirExpressPrice101Plus;
    }

    public void setClxAirExpressPrice101Plus(Double clxAirExpressPrice101Plus) {
        this.clxAirExpressPrice101Plus = clxAirExpressPrice101Plus;
    }

    @Override
    public String toString() {
        return "PriceTableRow{" +
                "destination='" + destination + '\'' +
                ", zipCodeRange='" + zipCodeRange + '\'' +
                ", warehouseCode='" + warehouseCode + '\'' +
                ", channelCode='" + channelCode + '\'' +
                ", clxTruckPrice=" + clxTruckPrice +
                ", maxTruckPrice=" + maxTruckPrice +
                ", heDeTruckPrice=" + heDeTruckPrice +
                ", oaTruckPrice=" + oaTruckPrice +
                ", oaTPrice=" + oaTPrice +
                ", notes='" + notes + '\'' +
                '}';
    }
}
