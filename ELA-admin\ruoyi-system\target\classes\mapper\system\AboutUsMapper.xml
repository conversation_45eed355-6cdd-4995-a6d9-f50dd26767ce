<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.AboutUsMapper">
    
    <resultMap type="AboutUs" id="AboutUsResult">
        <result property="id"    column="id"    />
        <result property="titleZh"    column="title_zh"    />
        <result property="titleEn"    column="title_en"    />
        <result property="subtitleZh"    column="subtitle_zh"    />
        <result property="subtitleEn"    column="subtitle_en"    />
        <result property="companyNameZh"    column="company_name_zh"    />
        <result property="companyNameEn"    column="company_name_en"    />
        <result property="descriptionZh"    column="description_zh"    />
        <result property="descriptionEn"    column="description_en"    />
        <result property="statisticsConfig"    column="statistics_config"    />
        <result property="missionCardsConfig"    column="mission_cards_config"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="imageDescZh"    column="image_desc_zh"    />
        <result property="imageDescEn"    column="image_desc_en"    />
        <result property="backgroundImageUrl"    column="background_image_url"    />
        <result property="sort"    column="sort"    />
        <result property="status"    column="status"    />
        <result property="visionTitleZh"    column="vision_title_zh"    />
        <result property="visionTitleEn"    column="vision_title_en"    />
        <result property="visionContentZh"    column="vision_content_zh"    />
        <result property="visionContentEn"    column="vision_content_en"    />
        <result property="visionButtonZh"    column="vision_button_zh"    />
        <result property="visionButtonEn"    column="vision_button_en"    />
        <result property="visionButtonUrl"    column="vision_button_url"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAboutUsVo">
        select id, title_zh, title_en, subtitle_zh, subtitle_en, company_name_zh, company_name_en, description_zh, description_en, statistics_config, mission_cards_config, image_url, image_desc_zh, image_desc_en, background_image_url, sort, status, vision_title_zh, vision_title_en, vision_content_zh, vision_content_en, vision_button_zh, vision_button_en, vision_button_url, create_time, update_time from about_us
    </sql>

    <select id="selectAboutUsList" parameterType="AboutUs" resultMap="AboutUsResult">
        <include refid="selectAboutUsVo"/>
        <where>  
            <if test="titleZh != null  and titleZh != ''"> and title_zh like concat('%', #{titleZh}, '%')</if>
            <if test="titleEn != null  and titleEn != ''"> and title_en like concat('%', #{titleEn}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort asc, create_time desc
    </select>
    
    <select id="selectAboutUsById" parameterType="Long" resultMap="AboutUsResult">
        <include refid="selectAboutUsVo"/>
        where id = #{id}
    </select>
    
    <select id="selectAboutUsActive" resultMap="AboutUsResult">
        <include refid="selectAboutUsVo"/>
        where status = '0'
        order by sort asc, create_time desc
        limit 1
    </select>
        
    <insert id="insertAboutUs" parameterType="AboutUs" useGeneratedKeys="true" keyProperty="id">
        insert into about_us
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="titleZh != null and titleZh != ''">title_zh,</if>
            <if test="titleEn != null and titleEn != ''">title_en,</if>
            <if test="subtitleZh != null and subtitleZh != ''">subtitle_zh,</if>
            <if test="subtitleEn != null and subtitleEn != ''">subtitle_en,</if>
            <if test="companyNameZh != null and companyNameZh != ''">company_name_zh,</if>
            <if test="companyNameEn != null and companyNameEn != ''">company_name_en,</if>
            <if test="descriptionZh != null and descriptionZh != ''">description_zh,</if>
            <if test="descriptionEn != null and descriptionEn != ''">description_en,</if>
            <if test="statisticsConfig != null and statisticsConfig != ''">statistics_config,</if>
            <if test="missionCardsConfig != null and missionCardsConfig != ''">mission_cards_config,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="imageDescZh != null and imageDescZh != ''">image_desc_zh,</if>
            <if test="imageDescEn != null and imageDescEn != ''">image_desc_en,</if>
            <if test="backgroundImageUrl != null and backgroundImageUrl != ''">background_image_url,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="visionTitleZh != null and visionTitleZh != ''">vision_title_zh,</if>
            <if test="visionTitleEn != null and visionTitleEn != ''">vision_title_en,</if>
            <if test="visionContentZh != null and visionContentZh != ''">vision_content_zh,</if>
            <if test="visionContentEn != null and visionContentEn != ''">vision_content_en,</if>
            <if test="visionButtonZh != null and visionButtonZh != ''">vision_button_zh,</if>
            <if test="visionButtonEn != null and visionButtonEn != ''">vision_button_en,</if>
            <if test="visionButtonUrl != null and visionButtonUrl != ''">vision_button_url,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="titleZh != null and titleZh != ''">#{titleZh},</if>
            <if test="titleEn != null and titleEn != ''">#{titleEn},</if>
            <if test="subtitleZh != null and subtitleZh != ''">#{subtitleZh},</if>
            <if test="subtitleEn != null and subtitleEn != ''">#{subtitleEn},</if>
            <if test="companyNameZh != null and companyNameZh != ''">#{companyNameZh},</if>
            <if test="companyNameEn != null and companyNameEn != ''">#{companyNameEn},</if>
            <if test="descriptionZh != null and descriptionZh != ''">#{descriptionZh},</if>
            <if test="descriptionEn != null and descriptionEn != ''">#{descriptionEn},</if>
            <if test="statisticsConfig != null and statisticsConfig != ''">#{statisticsConfig},</if>
            <if test="missionCardsConfig != null and missionCardsConfig != ''">#{missionCardsConfig},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="imageDescZh != null and imageDescZh != ''">#{imageDescZh},</if>
            <if test="imageDescEn != null and imageDescEn != ''">#{imageDescEn},</if>
            <if test="backgroundImageUrl != null and backgroundImageUrl != ''">#{backgroundImageUrl},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="visionTitleZh != null and visionTitleZh != ''">#{visionTitleZh},</if>
            <if test="visionTitleEn != null and visionTitleEn != ''">#{visionTitleEn},</if>
            <if test="visionContentZh != null and visionContentZh != ''">#{visionContentZh},</if>
            <if test="visionContentEn != null and visionContentEn != ''">#{visionContentEn},</if>
            <if test="visionButtonZh != null and visionButtonZh != ''">#{visionButtonZh},</if>
            <if test="visionButtonEn != null and visionButtonEn != ''">#{visionButtonEn},</if>
            <if test="visionButtonUrl != null and visionButtonUrl != ''">#{visionButtonUrl},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAboutUs" parameterType="AboutUs">
        update about_us
        <trim prefix="SET" suffixOverrides=",">
            <if test="titleZh != null and titleZh != ''">title_zh = #{titleZh},</if>
            <if test="titleEn != null and titleEn != ''">title_en = #{titleEn},</if>
            <if test="subtitleZh != null and subtitleZh != ''">subtitle_zh = #{subtitleZh},</if>
            <if test="subtitleEn != null and subtitleEn != ''">subtitle_en = #{subtitleEn},</if>
            <if test="companyNameZh != null and companyNameZh != ''">company_name_zh = #{companyNameZh},</if>
            <if test="companyNameEn != null and companyNameEn != ''">company_name_en = #{companyNameEn},</if>
            <if test="descriptionZh != null and descriptionZh != ''">description_zh = #{descriptionZh},</if>
            <if test="descriptionEn != null and descriptionEn != ''">description_en = #{descriptionEn},</if>
            <if test="statisticsConfig != null and statisticsConfig != ''">statistics_config = #{statisticsConfig},</if>
            <if test="missionCardsConfig != null and missionCardsConfig != ''">mission_cards_config = #{missionCardsConfig},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="imageDescZh != null and imageDescZh != ''">image_desc_zh = #{imageDescZh},</if>
            <if test="imageDescEn != null and imageDescEn != ''">image_desc_en = #{imageDescEn},</if>
            <if test="backgroundImageUrl != null and backgroundImageUrl != ''">background_image_url = #{backgroundImageUrl},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="visionTitleZh != null">vision_title_zh = #{visionTitleZh},</if>
            <if test="visionTitleEn != null">vision_title_en = #{visionTitleEn},</if>
            <if test="visionContentZh != null">vision_content_zh = #{visionContentZh},</if>
            <if test="visionContentEn != null">vision_content_en = #{visionContentEn},</if>
            <if test="visionButtonZh != null">vision_button_zh = #{visionButtonZh},</if>
            <if test="visionButtonEn != null">vision_button_en = #{visionButtonEn},</if>
            <if test="visionButtonUrl != null">vision_button_url = #{visionButtonUrl},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAboutUsById" parameterType="Long">
        delete from about_us where id = #{id}
    </delete>

    <delete id="deleteAboutUsByIds" parameterType="String">
        delete from about_us where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 