package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.LeadershipMessage;

/**
 * 领导力消息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ILeadershipMessageService 
{
    /**
     * 查询领导力消息
     * 
     * @param id 领导力消息主键
     * @return 领导力消息
     */
    public LeadershipMessage selectLeadershipMessageById(Long id);

    /**
     * 查询领导力消息列表
     * 
     * @param leadershipMessage 领导力消息
     * @return 领导力消息集合
     */
    public List<LeadershipMessage> selectLeadershipMessageList(LeadershipMessage leadershipMessage);

    /**
     * 查询启用的领导力消息列表（按排序）
     * 
     * @return 领导力消息集合
     */
    public List<LeadershipMessage> selectEnabledLeadershipMessageList();

    /**
     * 新增领导力消息
     * 
     * @param leadershipMessage 领导力消息
     * @return 结果
     */
    public int insertLeadershipMessage(LeadershipMessage leadershipMessage);

    /**
     * 修改领导力消息
     * 
     * @param leadershipMessage 领导力消息
     * @return 结果
     */
    public int updateLeadershipMessage(LeadershipMessage leadershipMessage);

    /**
     * 批量删除领导力消息
     * 
     * @param ids 需要删除的领导力消息主键集合
     * @return 结果
     */
    public int deleteLeadershipMessageByIds(Long[] ids);

    /**
     * 删除领导力消息信息
     * 
     * @param id 领导力消息主键
     * @return 结果
     */
    public int deleteLeadershipMessageById(Long id);
} 