import Vue from 'vue'
import Router from 'vue-router'
import Home from '@/components/Home'
import About from '@/components/About'
import Contact from '@/components/Contact'

Vue.use(Router)

export default new Router({
  mode: 'history',
  routes: [
    {
      path: '/',
      name: 'Home',
      component: Home
    },
    {
      path: '/about',
      name: 'About',
      component: About
    },
    {
      path: '/about/:categorySlug',
      name: 'AboutCategory',
      component: () => import('@/components/about/Council.vue'),
      props: route => ({
        categorySlug: route.params.categorySlug,
        categoryId: route.query.categoryId,
        categoryName: route.query.category || route.params.categorySlug
      })
    },
    // 保持向后兼容的固定路由
    {
      path: '/about/council',
      name: 'Council',
      component: () => import('@/components/about/Council.vue')
    },
    {
      path: '/about/advisors',
      name: 'Advisors',
      component: () => import('@/components/about/Advisors.vue')
    },
    {
      path: '/about/youth-committee',
      name: 'YouthCommittee',
      component: () => import('@/components/about/YouthCommittee.vue')
    },
    {
      path: '/pr-events',
      name: 'PREvents',
      component: () => import('@/components/PREvents')
    },
    {
      path: '/activity/:id',
      name: 'ActivityDetail',
      component: () => import('@/components/ActivityDetail')
    },
    {
      path: '/events',
      name: 'Events',
      component: () => import('@/components/Events')
    },
    {
      path: '/press-releases',
      name: 'PressReleases',
      component: () => import('@/components/PressReleases')
    },
    {
      path: '/publication',
      name: 'Publication',
      component: () => import('@/components/Publication')
    },
    {
      path: '/join-us',
      name: 'JoinUs',
      component: () => import('@/components/JoinUs')
    },
    {
      path: '/contact',
      name: 'Contact',
      component: Contact
    },
    {
      path: '/products',
      redirect: '/pr-events'
    },
    {
      path: '/news',
      redirect: '/press-releases'
    },
    {
      path: '/careers',
      redirect: '/join-us'
    },
    {
      path: '*',
      redirect: '/'
    }
  ],
  scrollBehavior (to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  }
})
