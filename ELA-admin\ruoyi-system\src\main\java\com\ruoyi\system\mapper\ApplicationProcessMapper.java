package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ApplicationProcess;

/**
 * 申请流程Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
public interface ApplicationProcessMapper 
{
    /**
     * 查询申请流程
     * 
     * @param id 申请流程主键
     * @return 申请流程
     */
    public ApplicationProcess selectApplicationProcessById(Long id);

    /**
     * 查询申请流程列表
     * 
     * @param applicationProcess 申请流程
     * @return 申请流程集合
     */
    public List<ApplicationProcess> selectApplicationProcessList(ApplicationProcess applicationProcess);

    /**
     * 新增申请流程
     * 
     * @param applicationProcess 申请流程
     * @return 结果
     */
    public int insertApplicationProcess(ApplicationProcess applicationProcess);

    /**
     * 修改申请流程
     * 
     * @param applicationProcess 申请流程
     * @return 结果
     */
    public int updateApplicationProcess(ApplicationProcess applicationProcess);

    /**
     * 删除申请流程
     * 
     * @param id 申请流程主键
     * @return 结果
     */
    public int deleteApplicationProcessById(Long id);

    /**
     * 批量删除申请流程
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteApplicationProcessByIds(Long[] ids);
} 