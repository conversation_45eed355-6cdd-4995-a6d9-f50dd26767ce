package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.MembershipTypeMapper;
import com.ruoyi.system.domain.MembershipType;
import com.ruoyi.system.service.IMembershipTypeService;

/**
 * 会员类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
@Service
public class MembershipTypeServiceImpl implements IMembershipTypeService 
{
    @Autowired
    private MembershipTypeMapper membershipTypeMapper;

    /**
     * 查询会员类型
     * 
     * @param id 会员类型主键
     * @return 会员类型
     */
    @Override
    public MembershipType selectMembershipTypeById(Long id)
    {
        return membershipTypeMapper.selectMembershipTypeById(id);
    }

    /**
     * 查询会员类型列表
     * 
     * @param membershipType 会员类型
     * @return 会员类型
     */
    @Override
    public List<MembershipType> selectMembershipTypeList(MembershipType membershipType)
    {
        return membershipTypeMapper.selectMembershipTypeList(membershipType);
    }

    /**
     * 新增会员类型
     * 
     * @param membershipType 会员类型
     * @return 结果
     */
    @Override
    public int insertMembershipType(MembershipType membershipType)
    {
        membershipType.setCreateTime(DateUtils.getNowDate());
        return membershipTypeMapper.insertMembershipType(membershipType);
    }

    /**
     * 修改会员类型
     * 
     * @param membershipType 会员类型
     * @return 结果
     */
    @Override
    public int updateMembershipType(MembershipType membershipType)
    {
        membershipType.setUpdateTime(DateUtils.getNowDate());
        return membershipTypeMapper.updateMembershipType(membershipType);
    }

    /**
     * 批量删除会员类型
     * 
     * @param ids 需要删除的会员类型主键
     * @return 结果
     */
    @Override
    public int deleteMembershipTypeByIds(Long[] ids)
    {
        return membershipTypeMapper.deleteMembershipTypeByIds(ids);
    }

    /**
     * 删除会员类型信息
     * 
     * @param id 会员类型主键
     * @return 结果
     */
    @Override
    public int deleteMembershipTypeById(Long id)
    {
        return membershipTypeMapper.deleteMembershipTypeById(id);
    }
} 