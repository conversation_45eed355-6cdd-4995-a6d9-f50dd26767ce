package com.ruoyi.web.controller.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.FooterConfig;
import com.ruoyi.system.domain.FooterLink;
import com.ruoyi.system.service.IFooterConfigService;
import com.ruoyi.system.service.IFooterLinkService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 页脚配置Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/website/footerConfig")
public class FooterConfigController extends BaseController
{
    @Autowired
    private IFooterConfigService footerConfigService;

    @Autowired
    private IFooterLinkService footerLinkService;

    /**
     * 查询页脚配置列表
     */
    @PreAuthorize("@ss.hasPermi('website:footerConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(FooterConfig footerConfig)
    {
        startPage();
        List<FooterConfig> list = footerConfigService.selectFooterConfigList(footerConfig);
        return getDataTable(list);
    }

    /**
     * 导出页脚配置列表
     */
    @PreAuthorize("@ss.hasPermi('website:footerConfig:export')")
    @Log(title = "页脚配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FooterConfig footerConfig)
    {
        List<FooterConfig> list = footerConfigService.selectFooterConfigList(footerConfig);
        ExcelUtil<FooterConfig> util = new ExcelUtil<FooterConfig>(FooterConfig.class);
        util.exportExcel(response, list, "页脚配置数据");
    }

    /**
     * 获取页脚配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:footerConfig:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(footerConfigService.selectFooterConfigById(id));
    }

    /**
     * 新增页脚配置
     */
    @PreAuthorize("@ss.hasPermi('website:footerConfig:add')")
    @Log(title = "页脚配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FooterConfig footerConfig)
    {
        return toAjax(footerConfigService.insertFooterConfig(footerConfig));
    }

    /**
     * 修改页脚配置
     */
    @PreAuthorize("@ss.hasPermi('website:footerConfig:edit')")
    @Log(title = "页脚配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FooterConfig footerConfig)
    {
        return toAjax(footerConfigService.updateFooterConfig(footerConfig));
    }

    /**
     * 删除页脚配置
     */
    @PreAuthorize("@ss.hasPermi('website:footerConfig:remove')")
    @Log(title = "页脚配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(footerConfigService.deleteFooterConfigByIds(ids));
    }

    /**
     * 公开接口：获取页脚配置（无需认证）
     */
    @GetMapping("/public/list")
    public AjaxResult getPublicFooterConfig()
    {
        FooterConfig query = new FooterConfig();
        query.setStatus("0"); // 只获取启用的配置
        List<FooterConfig> configs = footerConfigService.selectFooterConfigList(query);
        return success(configs);
    }

    /**
     * 公开接口：获取页脚链接（无需认证）
     */
    @GetMapping("/public/links")
    public AjaxResult getPublicFooterLinks()
    {
        FooterLink query = new FooterLink();
        query.setStatus("0"); // 只获取启用的链接
        List<FooterLink> links = footerLinkService.selectFooterLinkList(query);
        return success(links);
    }
} 