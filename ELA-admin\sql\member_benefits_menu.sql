-- 会员权益菜单 SQL
-- 获取网站管理菜单ID
SELECT @websiteMenuId := menu_id FROM sys_menu WHERE menu_name = '网站管理' AND parent_id = '0' LIMIT 1;

-- 添加会员权益菜单（网站管理的子菜单）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('会员权益', @websiteMenuId, '7', 'member-benefits', 'website/member-benefits/index', 1, 0, 'C', '0', '0', 'website:member-benefits:list', 'fa fa-gift', 'admin', sysdate(), '', null, '会员权益菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员权益查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'website:member-benefits:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员权益新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'website:member-benefits:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员权益修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'website:member-benefits:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员权益删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'website:member-benefits:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员权益导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'website:member-benefits:export',       '#', 'admin', sysdate(), '', null, ''); 