import request from '@/utils/request'

// 获取组织架构分类列表
export function getOrganizationCategories() {
  return request({
    url: '/web/organization/categories',
    method: 'get'
  })
}

// 根据分类ID获取组织架构数据
export function getOrganizationData(categoryId) {
  return request({
    url: `/web/organization/data/${categoryId}`,
    method: 'get'
  })
}

// 根据分类名称获取组织架构数据
export function getOrganizationDataByName(categoryName) {
  return request({
    url: `/web/organization/data/name/${categoryName}`,
    method: 'get'
  })
}

// 获取特定类型的成员列表
export function getMembersByType(categoryId, memberType) {
  return request({
    url: `/web/organization/members/${categoryId}/${memberType}`,
    method: 'get'
  })
}

// 获取领导层成员
export function getLeadershipMembers(categoryId) {
  return request({
    url: `/web/organization/leadership/${categoryId}`,
    method: 'get'
  })
} 