package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.ContactInfo;

/**
 * 联系信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IContactInfoService 
{
    /**
     * 查询联系信息
     * 
     * @param id 联系信息主键
     * @return 联系信息
     */
    public ContactInfo selectContactInfoById(Long id);

    /**
     * 查询联系信息列表
     * 
     * @param contactInfo 联系信息
     * @return 联系信息集合
     */
    public List<ContactInfo> selectContactInfoList(ContactInfo contactInfo);

    /**
     * 新增联系信息
     * 
     * @param contactInfo 联系信息
     * @return 结果
     */
    public int insertContactInfo(ContactInfo contactInfo);

    /**
     * 修改联系信息
     * 
     * @param contactInfo 联系信息
     * @return 结果
     */
    public int updateContactInfo(ContactInfo contactInfo);

    /**
     * 批量删除联系信息
     * 
     * @param ids 需要删除的联系信息主键集合
     * @return 结果
     */
    public int deleteContactInfoByIds(Long[] ids);

    /**
     * 删除联系信息信息
     * 
     * @param id 联系信息主键
     * @return 结果
     */
    public int deleteContactInfoById(Long id);
} 