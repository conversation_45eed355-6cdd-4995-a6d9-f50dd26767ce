package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ApplicationProcessMapper;
import com.ruoyi.system.domain.ApplicationProcess;
import com.ruoyi.system.service.IApplicationProcessService;

/**
 * 申请流程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
@Service
public class ApplicationProcessServiceImpl implements IApplicationProcessService 
{
    @Autowired
    private ApplicationProcessMapper applicationProcessMapper;

    /**
     * 查询申请流程
     * 
     * @param id 申请流程主键
     * @return 申请流程
     */
    @Override
    public ApplicationProcess selectApplicationProcessById(Long id)
    {
        return applicationProcessMapper.selectApplicationProcessById(id);
    }

    /**
     * 查询申请流程列表
     * 
     * @param applicationProcess 申请流程
     * @return 申请流程
     */
    @Override
    public List<ApplicationProcess> selectApplicationProcessList(ApplicationProcess applicationProcess)
    {
        return applicationProcessMapper.selectApplicationProcessList(applicationProcess);
    }

    /**
     * 新增申请流程
     * 
     * @param applicationProcess 申请流程
     * @return 结果
     */
    @Override
    public int insertApplicationProcess(ApplicationProcess applicationProcess)
    {
        return applicationProcessMapper.insertApplicationProcess(applicationProcess);
    }

    /**
     * 修改申请流程
     * 
     * @param applicationProcess 申请流程
     * @return 结果
     */
    @Override
    public int updateApplicationProcess(ApplicationProcess applicationProcess)
    {
        return applicationProcessMapper.updateApplicationProcess(applicationProcess);
    }

    /**
     * 批量删除申请流程
     * 
     * @param ids 需要删除的申请流程主键
     * @return 结果
     */
    @Override
    public int deleteApplicationProcessByIds(Long[] ids)
    {
        return applicationProcessMapper.deleteApplicationProcessByIds(ids);
    }

    /**
     * 删除申请流程信息
     * 
     * @param id 申请流程主键
     * @return 结果
     */
    @Override
    public int deleteApplicationProcessById(Long id)
    {
        return applicationProcessMapper.deleteApplicationProcessById(id);
    }
} 