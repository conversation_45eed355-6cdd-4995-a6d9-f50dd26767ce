<template>
  <section class="contact-cta section">
    <div class="cta-background"></div>
    <div class="container">
      <div class="cta-content">
        <h2 class="cta-title" v-if="section.config.title">{{ section.config.title }}</h2>
        <p class="cta-subtitle" v-if="section.config.subtitle">{{ section.config.subtitle }}</p>
        
        <div class="cta-buttons" v-if="section.config.buttonText && section.config.buttonLink">
          <router-link :to="section.config.buttonLink" class="btn btn-secondary">
            {{ section.config.buttonText }}
          </router-link>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'ContactCTA',
  props: {
    section: {
      type: Object,
      required: true
    }
  }
}
</script>

<style scoped>
.contact-cta {
  position: relative;
  overflow: hidden;
  padding: 80px 0;
  margin-top: 60px;
}

.cta-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0.8;
  z-index: 0;
}

.container {
  position: relative;
  z-index: 1;
}

.cta-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.cta-title {
  font-size: 36px;
  font-weight: 700;
  color: var(--text-heading);
  margin-bottom: 16px;
  text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.cta-subtitle {
  font-size: 18px;
  color: var(--text-light);
  margin-bottom: 32px;
  opacity: 0.9;
  line-height: 1.7;
}

.cta-buttons .btn {
  padding: 14px 32px;
  font-size: 16px;
  transform: scale(1);
  transition: transform 0.3s ease;
}

.cta-buttons .btn:hover {
  transform: scale(1.05);
}

@media (max-width: 768px) {
  .cta-title {
    font-size: 28px;
  }
  .cta-subtitle {
    font-size: 16px;
  }
}
</style> 