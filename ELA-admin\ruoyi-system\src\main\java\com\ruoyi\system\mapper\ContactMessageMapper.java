package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ContactMessage;

/**
 * 联系消息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ContactMessageMapper 
{
    /**
     * 查询联系消息
     * 
     * @param id 联系消息主键
     * @return 联系消息
     */
    public ContactMessage selectContactMessageById(Long id);

    /**
     * 查询联系消息列表
     * 
     * @param contactMessage 联系消息
     * @return 联系消息集合
     */
    public List<ContactMessage> selectContactMessageList(ContactMessage contactMessage);

    /**
     * 新增联系消息
     * 
     * @param contactMessage 联系消息
     * @return 结果
     */
    public int insertContactMessage(ContactMessage contactMessage);

    /**
     * 修改联系消息
     * 
     * @param contactMessage 联系消息
     * @return 结果
     */
    public int updateContactMessage(ContactMessage contactMessage);

    /**
     * 删除联系消息
     * 
     * @param id 联系消息主键
     * @return 结果
     */
    public int deleteContactMessageById(Long id);

    /**
     * 批量删除联系消息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteContactMessageByIds(Long[] ids);
} 