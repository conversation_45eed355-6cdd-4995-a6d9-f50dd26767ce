package com.ruoyi.web.controller.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Publication;
import com.ruoyi.system.service.IPublicationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 资讯管理Controller
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
@RestController
@RequestMapping("/website/publications")
public class PublicationController extends BaseController
{
    @Autowired
    private IPublicationService publicationService;

    /**
     * 查询资讯管理列表
     */
    @PreAuthorize("@ss.hasPermi('website:publications:list')")
    @GetMapping("/list")
    public TableDataInfo list(Publication publication)
    {
        startPage();
        List<Publication> list = publicationService.selectPublicationList(publication);
        return getDataTable(list);
    }

    /**
     * 导出资讯管理列表
     */
    @PreAuthorize("@ss.hasPermi('website:publications:export')")
    @Log(title = "资讯管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Publication publication)
    {
        List<Publication> list = publicationService.selectPublicationList(publication);
        ExcelUtil<Publication> util = new ExcelUtil<Publication>(Publication.class);
        util.exportExcel(response, list, "资讯管理数据");
    }

    /**
     * 获取资讯管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:publications:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(publicationService.selectPublicationById(id));
    }

    /**
     * 新增资讯管理
     */
    @PreAuthorize("@ss.hasPermi('website:publications:add')")
    @Log(title = "资讯管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Publication publication)
    {
        publication.setCreateBy(getUsername());
        return toAjax(publicationService.insertPublication(publication));
    }

    /**
     * 修改资讯管理
     */
    @PreAuthorize("@ss.hasPermi('website:publications:edit')")
    @Log(title = "资讯管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Publication publication)
    {
        publication.setUpdateBy(getUsername());
        return toAjax(publicationService.updatePublication(publication));
    }

    /**
     * 删除资讯管理
     */
    @PreAuthorize("@ss.hasPermi('website:publications:remove')")
    @Log(title = "资讯管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(publicationService.deletePublicationByIds(ids));
    }
}