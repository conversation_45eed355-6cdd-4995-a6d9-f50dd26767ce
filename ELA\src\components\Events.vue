<template>
  <div class="events-page">
    <div class="page-header">
      <div class="container">
        <h1>{{ currentLang === 'zh' ? '活動' : 'Events' }}</h1>
        <p class="page-description">
          {{ currentLang === 'zh' 
            ? '探索我們豐富多彩的活動' 
            : 'Explore our diverse range of events' 
          }}
        </p>
      </div>
    </div>
    
    <div class="container">
      <div class="content-section">
        <h2>{{ currentLang === 'zh' ? '最新活動' : 'Latest Events' }}</h2>
        <!-- 活动列表 -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Events',
  props: {
    currentLang: {
      type: String,
      default: 'zh'
    }
  }
}
</script>

<style scoped>
.events-page {
  padding-top: 80px;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #0056b3 0%, #004494 100%);
  color: white;
  padding: 60px 0;
  text-align: center;
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.page-description {
  font-size: 1.2rem;
  opacity: 0.9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.content-section {
  padding: 60px 0;
}

.content-section h2 {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: var(--text-primary);
}
</style> 