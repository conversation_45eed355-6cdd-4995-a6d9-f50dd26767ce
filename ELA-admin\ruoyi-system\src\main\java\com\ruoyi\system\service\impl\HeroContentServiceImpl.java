package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.HeroContentMapper;
import com.ruoyi.system.domain.HeroContent;
import com.ruoyi.system.service.IHeroContentService;

/**
 * 首页Hero内容Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class HeroContentServiceImpl implements IHeroContentService 
{
    @Autowired
    private HeroContentMapper heroContentMapper;

    /**
     * 查询首页Hero内容
     * 
     * @param id 首页Hero内容主键
     * @return 首页Hero内容
     */
    @Override
    public HeroContent selectHeroContentById(Long id)
    {
        return heroContentMapper.selectHeroContentById(id);
    }

    /**
     * 查询首页Hero内容列表
     * 
     * @param heroContent 首页Hero内容
     * @return 首页Hero内容
     */
    @Override
    public List<HeroContent> selectHeroContentList(HeroContent heroContent)
    {
        return heroContentMapper.selectHeroContentList(heroContent);
    }

    /**
     * 查询启用的首页Hero内容
     * 
     * @return 首页Hero内容
     */
    @Override
    public HeroContent selectEnabledHeroContent()
    {
        return heroContentMapper.selectEnabledHeroContent();
    }

    /**
     * 新增首页Hero内容
     * 
     * @param heroContent 首页Hero内容
     * @return 结果
     */
    @Override
    public int insertHeroContent(HeroContent heroContent)
    {
        return heroContentMapper.insertHeroContent(heroContent);
    }

    /**
     * 修改首页Hero内容
     * 
     * @param heroContent 首页Hero内容
     * @return 结果
     */
    @Override
    public int updateHeroContent(HeroContent heroContent)
    {
        return heroContentMapper.updateHeroContent(heroContent);
    }

    /**
     * 批量删除首页Hero内容
     * 
     * @param ids 需要删除的首页Hero内容主键
     * @return 结果
     */
    @Override
    public int deleteHeroContentByIds(Long[] ids)
    {
        return heroContentMapper.deleteHeroContentByIds(ids);
    }

    /**
     * 删除首页Hero内容信息
     * 
     * @param id 首页Hero内容主键
     * @return 结果
     */
    @Override
    public int deleteHeroContentById(Long id)
    {
        return heroContentMapper.deleteHeroContentById(id);
    }
} 