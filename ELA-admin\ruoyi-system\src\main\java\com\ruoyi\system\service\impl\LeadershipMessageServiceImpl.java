package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.LeadershipMessageMapper;
import com.ruoyi.system.domain.LeadershipMessage;
import com.ruoyi.system.service.ILeadershipMessageService;

/**
 * 领导力消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class LeadershipMessageServiceImpl implements ILeadershipMessageService 
{
    @Autowired
    private LeadershipMessageMapper leadershipMessageMapper;

    /**
     * 查询领导力消息
     * 
     * @param id 领导力消息主键
     * @return 领导力消息
     */
    @Override
    public LeadershipMessage selectLeadershipMessageById(Long id)
    {
        return leadershipMessageMapper.selectLeadershipMessageById(id);
    }

    /**
     * 查询领导力消息列表
     * 
     * @param leadershipMessage 领导力消息
     * @return 领导力消息
     */
    @Override
    public List<LeadershipMessage> selectLeadershipMessageList(LeadershipMessage leadershipMessage)
    {
        return leadershipMessageMapper.selectLeadershipMessageList(leadershipMessage);
    }

    /**
     * 查询启用的领导力消息列表（按排序）
     * 
     * @return 领导力消息集合
     */
    @Override
    public List<LeadershipMessage> selectEnabledLeadershipMessageList()
    {
        return leadershipMessageMapper.selectEnabledLeadershipMessageList();
    }

    /**
     * 新增领导力消息
     * 
     * @param leadershipMessage 领导力消息
     * @return 结果
     */
    @Override
    public int insertLeadershipMessage(LeadershipMessage leadershipMessage)
    {
        leadershipMessage.setCreateTime(DateUtils.getNowDate());
        return leadershipMessageMapper.insertLeadershipMessage(leadershipMessage);
    }

    /**
     * 修改领导力消息
     * 
     * @param leadershipMessage 领导力消息
     * @return 结果
     */
    @Override
    public int updateLeadershipMessage(LeadershipMessage leadershipMessage)
    {
        leadershipMessage.setUpdateTime(DateUtils.getNowDate());
        return leadershipMessageMapper.updateLeadershipMessage(leadershipMessage);
    }

    /**
     * 批量删除领导力消息
     * 
     * @param ids 需要删除的领导力消息主键
     * @return 结果
     */
    @Override
    public int deleteLeadershipMessageByIds(Long[] ids)
    {
        return leadershipMessageMapper.deleteLeadershipMessageByIds(ids);
    }

    /**
     * 删除领导力消息信息
     * 
     * @param id 领导力消息主键
     * @return 结果
     */
    @Override
    public int deleteLeadershipMessageById(Long id)
    {
        return leadershipMessageMapper.deleteLeadershipMessageById(id);
    }
} 