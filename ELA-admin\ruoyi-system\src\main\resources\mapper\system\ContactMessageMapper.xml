<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ContactMessageMapper">
    
    <resultMap type="ContactMessage" id="ContactMessageResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="email"    column="email"    />
        <result property="subject"    column="subject"    />
        <result property="message"    column="message"    />
        <result property="status"    column="status"    />
        <result property="processedBy"    column="processed_by"    />
        <result property="processedTime"    column="processed_time"    />
        <result property="processedNote"    column="processed_note"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectContactMessageVo">
        select id, name, email, subject, message, status, processed_by, processed_time, processed_note, create_time, update_time, create_by, update_by, remark from contact_messages
    </sql>

    <select id="selectContactMessageList" parameterType="ContactMessage" resultMap="ContactMessageResult">
        <include refid="selectContactMessageVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="email != null  and email != ''"> and email like concat('%', #{email}, '%')</if>
            <if test="subject != null  and subject != ''"> and subject like concat('%', #{subject}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="processedBy != null  and processedBy != ''"> and processed_by like concat('%', #{processedBy}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectContactMessageById" parameterType="Long" resultMap="ContactMessageResult">
        <include refid="selectContactMessageVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertContactMessage" parameterType="ContactMessage" useGeneratedKeys="true" keyProperty="id">
        insert into contact_messages
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="subject != null">subject,</if>
            <if test="message != null and message != ''">message,</if>
            <if test="status != null">status,</if>
            <if test="processedBy != null">processed_by,</if>
            <if test="processedTime != null">processed_time,</if>
            <if test="processedNote != null">processed_note,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="subject != null">#{subject},</if>
            <if test="message != null and message != ''">#{message},</if>
            <if test="status != null">#{status},</if>
            <if test="processedBy != null">#{processedBy},</if>
            <if test="processedTime != null">#{processedTime},</if>
            <if test="processedNote != null">#{processedNote},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateContactMessage" parameterType="ContactMessage">
        update contact_messages
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="subject != null">subject = #{subject},</if>
            <if test="message != null and message != ''">message = #{message},</if>
            <if test="status != null">status = #{status},</if>
            <if test="processedBy != null">processed_by = #{processedBy},</if>
            <if test="processedTime != null">processed_time = #{processedTime},</if>
            <if test="processedNote != null">processed_note = #{processedNote},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteContactMessageById" parameterType="Long">
        delete from contact_messages where id = #{id}
    </delete>

    <delete id="deleteContactMessageByIds" parameterType="String">
        delete from contact_messages where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 