package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SocialMedia;

/**
 * 社交媒体信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ISocialMediaService 
{
    /**
     * 查询社交媒体信息
     * 
     * @param id 社交媒体信息主键
     * @return 社交媒体信息
     */
    public SocialMedia selectSocialMediaById(Long id);

    /**
     * 查询社交媒体信息列表
     * 
     * @param socialMedia 社交媒体信息
     * @return 社交媒体信息集合
     */
    public List<SocialMedia> selectSocialMediaList(SocialMedia socialMedia);

    /**
     * 新增社交媒体信息
     * 
     * @param socialMedia 社交媒体信息
     * @return 结果
     */
    public int insertSocialMedia(SocialMedia socialMedia);

    /**
     * 修改社交媒体信息
     * 
     * @param socialMedia 社交媒体信息
     * @return 结果
     */
    public int updateSocialMedia(SocialMedia socialMedia);

    /**
     * 批量删除社交媒体信息
     * 
     * @param ids 需要删除的社交媒体信息主键集合
     * @return 结果
     */
    public int deleteSocialMediaByIds(Long[] ids);

    /**
     * 删除社交媒体信息信息
     * 
     * @param id 社交媒体信息主键
     * @return 结果
     */
    public int deleteSocialMediaById(Long id);
} 