<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标题中文" prop="titleZh">
        <el-input
          v-model="queryParams.titleZh"
          placeholder="请输入标题中文"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="内容状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['website:aboutus:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['website:aboutus:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['website:aboutus:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="aboutUsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键ID" align="center" prop="id" />
      <el-table-column label="标题中文" align="center" prop="titleZh" :show-overflow-tooltip="true" />
      <el-table-column label="标题英文" align="center" prop="titleEn" :show-overflow-tooltip="true" />
      <el-table-column label="公司名称中文" align="center" prop="companyNameZh" :show-overflow-tooltip="true" />
      <el-table-column label="公司名称英文" align="center" prop="companyNameEn" :show-overflow-tooltip="true" />
      <el-table-column label="图片预览" align="center" prop="imageUrl" width="100">
        <template slot-scope="scope">
          <image-preview v-if="scope.row.imageUrl" :src="scope.row.imageUrl" :width="50" :height="50"/>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['website:aboutus:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['website:aboutus:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改关于我们内容对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1300px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane label="基本信息" name="basic">
            <el-row>
              <el-col :span="12">
                <el-form-item label="标题中文" prop="titleZh">
                  <el-input v-model="form.titleZh" placeholder="请输入标题中文" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="标题英文" prop="titleEn">
                  <el-input v-model="form.titleEn" placeholder="请输入标题英文" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="副标题中文" prop="subtitleZh">
                  <el-input v-model="form.subtitleZh" placeholder="请输入副标题中文" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="副标题英文" prop="subtitleEn">
                  <el-input v-model="form.subtitleEn" placeholder="请输入副标题英文" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="公司名称中文" prop="companyNameZh">
                  <el-input v-model="form.companyNameZh" placeholder="请输入公司名称中文" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="公司名称英文" prop="companyNameEn">
                  <el-input v-model="form.companyNameEn" placeholder="请输入公司名称英文" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="详细描述中文" prop="descriptionZh">
              <el-input v-model="form.descriptionZh" type="textarea" :rows="5" placeholder="请输入详细描述中文" />
            </el-form-item>
            <el-form-item label="详细描述英文" prop="descriptionEn">
              <el-input v-model="form.descriptionEn" type="textarea" :rows="5" placeholder="请输入详细描述英文" />
            </el-form-item>
            <el-row>
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-radio-group v-model="form.status">
                    <el-radio
                      v-for="dict in dict.type.sys_normal_disable"
                      :key="dict.value"
                      :label="dict.value"
                    >{{dict.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="排序" prop="sort">
                  <el-input-number v-model="form.sort" controls-position="right" placeholder="请输入排序" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          
          <el-tab-pane label="统计数据" name="stats">
            <div style="margin-bottom: 20px;">
              <el-button type="primary" size="small" @click="addStatItem">
                <i class="el-icon-plus"></i> 添加统计数据
              </el-button>
            </div>
            <el-table :data="statsData" border>
              <el-table-column label="数值" min-width="100">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.number" placeholder="如：500+" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="单位中文" min-width="100">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.unitZh" placeholder="如：年" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="单位英文" min-width="100">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.unitEn" placeholder="如：Years" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="标签中文" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.labelZh" placeholder="如：全球客户" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="标签英文" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.labelEn" placeholder="如：Global Customers" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center">
                <template slot-scope="scope">
                  <el-button 
                    type="text" 
                    size="small" 
                    @click="removeStatItem(scope.$index)"
                    style="color: #f56c6c;"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div v-if="statsData.length === 0" style="text-align: center; padding: 40px; color: #909399;">
              暂无统计数据，点击上方按钮添加
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="使命卡片" name="mission">
            <div style="margin-bottom: 20px;">
              <el-button type="primary" size="small" @click="addMissionCard">
                <i class="el-icon-plus"></i> 添加使命卡片
              </el-button>
            </div>
            <el-table :data="missionCardsData" border>
              <el-table-column label="图标" min-width="150">
                <template slot-scope="scope">
                  <icon-select v-model="scope.row.icon" />
                </template>
              </el-table-column>
              <el-table-column label="标题中文" min-width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.titleZh" placeholder="如：创新" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="标题英文" min-width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.titleEn" placeholder="如：Innovation" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="描述中文" min-width="200">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.descriptionZh" type="textarea" :rows="2" placeholder="如：持续创新，引领行业发展" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="描述英文" min-width="200">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.descriptionEn" type="textarea" :rows="2" placeholder="如：Continuous innovation, leading industry development" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center">
                <template slot-scope="scope">
                  <el-button 
                    type="text" 
                    size="small" 
                    @click="removeMissionCard(scope.$index)"
                    style="color: #f56c6c;"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div v-if="missionCardsData.length === 0" style="text-align: center; padding: 40px; color: #909399;">
              暂无使命卡片，点击上方按钮添加
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="愿景配置" name="vision">
            <el-form-item label="愿景标题中文" prop="visionTitleZh">
              <el-input v-model="form.visionTitleZh" placeholder="请输入愿景标题中文" />
            </el-form-item>
            <el-form-item label="愿景标题英文" prop="visionTitleEn">
              <el-input v-model="form.visionTitleEn" placeholder="请输入愿景标题英文" />
            </el-form-item>
            <el-form-item label="愿景内容中文" prop="visionContentZh">
              <el-input v-model="form.visionContentZh" type="textarea" :rows="5" placeholder="请输入愿景内容中文" />
            </el-form-item>
            <el-form-item label="愿景内容英文" prop="visionContentEn">
              <el-input v-model="form.visionContentEn" type="textarea" :rows="5" placeholder="请输入愿景内容英文" />
            </el-form-item>
            <el-form-item label="愿景按钮文字中文" prop="visionButtonZh">
              <el-input v-model="form.visionButtonZh" placeholder="请输入按钮文字中文，如：阅读更多" />
            </el-form-item>
            <el-form-item label="愿景按钮文字英文" prop="visionButtonEn">
              <el-input v-model="form.visionButtonEn" placeholder="请输入按钮文字英文，如：Read More" />
            </el-form-item>
            <el-form-item label="愿景按钮链接" prop="visionButtonUrl">
              <el-input v-model="form.visionButtonUrl" placeholder="请输入按钮链接地址" />
            </el-form-item>
            <div style="margin-top: 10px; color: #909399; font-size: 12px;">
              此内容将显示在首页"愿景"部分
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="图片设置" name="images">
            <el-form-item label="右侧图片" prop="imageUrl">
              <image-upload v-model="form.imageUrl" :limit="1"/>
              <div style="margin-top: 10px; color: #909399; font-size: 12px;">
                此图片将显示在首页"我们的使命"部分的右侧区域
              </div>
            </el-form-item>
            <el-row>
              <el-col :span="12">
                <el-form-item label="图片描述中文" prop="imageDescZh">
                  <el-input v-model="form.imageDescZh" placeholder="请输入图片描述中文" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="图片描述英文" prop="imageDescEn">
                  <el-input v-model="form.imageDescEn" placeholder="请输入图片描述英文" />
                </el-form-item>
              </el-col>
            </el-row>
            <div style="margin-top: 10px; color: #909399; font-size: 12px;">
              注意：背景图片是固定的，不需要在此处设置
            </div>
          </el-tab-pane>
        </el-tabs>
        

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAboutUs, getAboutUs, delAboutUs, addAboutUs, updateAboutUs } from "@/api/website/aboutUs";
import IconSelect from "@/components/IconSelect";

export default {
  name: "AboutUs",
  components: {
    IconSelect
  },
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 关于我们表格数据
      aboutUsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前选中的标签页
      activeTab: "basic",
      // 统计数据
      statsData: [],
      // 使命卡片数据
      missionCardsData: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        titleZh: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        titleZh: [
          { required: true, message: "标题中文不能为空", trigger: "blur" }
        ],
        titleEn: [
          { required: true, message: "标题英文不能为空", trigger: "blur" }
        ],
        companyNameZh: [
          { required: true, message: "公司名称中文不能为空", trigger: "blur" }
        ],
        companyNameEn: [
          { required: true, message: "公司名称英文不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
        sort: [
          { required: true, message: "排序不能为空", trigger: "blur" }
        ],
        imageUrl: [
          { required: true, message: "右侧图片不能为空", trigger: "blur" }
        ],
        visionTitleZh: [
          { required: true, message: "愿景标题中文不能为空", trigger: "blur" }
        ],
        visionTitleEn: [
          { required: true, message: "愿景标题英文不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询关于我们列表 */
    getList() {
      this.loading = true;
      listAboutUs(this.queryParams).then(response => {
        this.aboutUsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        titleZh: null,
        titleEn: null,
        subtitleZh: null,
        subtitleEn: null,
        companyNameZh: null,
        companyNameEn: null,
        statisticsConfig: null,
        missionCardsConfig: null,
        descriptionZh: null,
        descriptionEn: null,
        imageUrl: null,
        imageDescZh: null,
        imageDescEn: null,
        visionTitleZh: null,
        visionTitleEn: null,
        visionContentZh: null,
        visionContentEn: null,
        visionButtonZh: null,
        visionButtonEn: null,
        visionButtonUrl: null,
        status: "0",
        sort: 0
      };
      this.statsData = [];
      this.missionCardsData = [];
      this.resetForm("form");
    },
    // 添加统计数据项
    addStatItem() {
      this.statsData.push({
        number: '',
        unitZh: '',
        unitEn: '',
        labelZh: '',
        labelEn: ''
      });
    },
    // 删除统计数据项
    removeStatItem(index) {
      this.statsData.splice(index, 1);
    },
    // 添加使命卡片
    addMissionCard() {
      this.missionCardsData.push({
        icon: '',
        titleZh: '',
        titleEn: '',
        descriptionZh: '',
        descriptionEn: ''
      });
    },
    // 删除使命卡片
    removeMissionCard(index) {
      this.missionCardsData.splice(index, 1);
    },
    // 解析JSON字符串为数组
    parseJsonToArray(jsonString, defaultValue = []) {
      if (!jsonString || !jsonString.trim()) {
        return defaultValue;
      }
      try {
        const parsed = JSON.parse(jsonString);
        if (Array.isArray(parsed)) {
          // 转换多语言数据结构为表单友好的格式
          return parsed.map(item => {
            const converted = { ...item };
            // 处理多语言字段
            if (item.unit && typeof item.unit === 'object') {
              converted.unitZh = item.unit.zh || '';
              converted.unitEn = item.unit.en || '';
            }
            if (item.label && typeof item.label === 'object') {
              converted.labelZh = item.label.zh || '';
              converted.labelEn = item.label.en || '';
            }
            if (item.title && typeof item.title === 'object') {
              converted.titleZh = item.title.zh || '';
              converted.titleEn = item.title.en || '';
            }
            if (item.description && typeof item.description === 'object') {
              converted.descriptionZh = item.description.zh || '';
              converted.descriptionEn = item.description.en || '';
            }
            return converted;
          });
        }
        return defaultValue;
      } catch (e) {
        console.error('解析JSON失败:', e);
        return defaultValue;
      }
    },
    // 将数组转换为JSON字符串
    arrayToJsonString(array) {
      if (!Array.isArray(array) || array.length === 0) {
        return '';
      }
      // 转换为多语言数据结构
      const converted = array.map(item => {
        const result = { ...item };
        // 处理多语言字段
        if (item.unitZh !== undefined || item.unitEn !== undefined) {
          result.unit = {
            zh: item.unitZh || '',
            en: item.unitEn || ''
          };
          delete result.unitZh;
          delete result.unitEn;
        }
        if (item.labelZh !== undefined || item.labelEn !== undefined) {
          result.label = {
            zh: item.labelZh || '',
            en: item.labelEn || ''
          };
          delete result.labelZh;
          delete result.labelEn;
        }
        if (item.titleZh !== undefined || item.titleEn !== undefined) {
          result.title = {
            zh: item.titleZh || '',
            en: item.titleEn || ''
          };
          delete result.titleZh;
          delete result.titleEn;
        }
        if (item.descriptionZh !== undefined || item.descriptionEn !== undefined) {
          result.description = {
            zh: item.descriptionZh || '',
            en: item.descriptionEn || ''
          };
          delete result.descriptionZh;
          delete result.descriptionEn;
        }
        return result;
      });
      return JSON.stringify(converted);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加关于我们";
      this.activeTab = "basic";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getAboutUs(id).then(response => {
        this.form = response.data;
        // 解析JSON数据为数组
        this.statsData = this.parseJsonToArray(this.form.statisticsConfig);
        this.missionCardsData = this.parseJsonToArray(this.form.missionCardsConfig);
        this.open = true;
        this.title = "修改关于我们";
        this.activeTab = "basic";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 将数组数据转换为JSON字符串
          this.form.statisticsConfig = this.arrayToJsonString(this.statsData);
          this.form.missionCardsConfig = this.arrayToJsonString(this.missionCardsData);
          
          if (this.form.id != null) {
            updateAboutUs(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAboutUs(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除关于我们编号为"' + ids + '"的数据项？').then(function() {
        return delAboutUs(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script> 