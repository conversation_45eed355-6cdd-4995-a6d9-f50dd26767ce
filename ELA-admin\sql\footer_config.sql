-- 页脚配置表
DROP TABLE IF EXISTS `footer_config`;
CREATE TABLE `footer_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `section_type` varchar(50) NOT NULL COMMENT '区域类型（main_links, address, qr_code, copyright）',
  `title` varchar(100) DEFAULT NULL COMMENT '标题',
  `content` text COMMENT '内容（JSON格式）',
  `background_color` varchar(20) DEFAULT '#004a9b' COMMENT '背景颜色',
  `text_color` varchar(20) DEFAULT '#ffffff' COMMENT '文字颜色',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0启用 1禁用）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='页脚配置表';

-- 插入默认配置数据
INSERT INTO `footer_config` (`section_type`, `title`, `content`, `background_color`, `text_color`, `sort_order`, `status`, `create_time`) VALUES
('main_links', '关于我们', '{"links":[{"name":"公司简介","url":"#"},{"name":"新闻中心","url":"#"},{"name":"科技创新","url":"#"}]}', '#004a9b', '#ffffff', 1, '0', NOW()),
('main_links', '加入我们', '{"links":[{"name":"工作机会","url":"#"}]}', '#004a9b', '#ffffff', 2, '0', NOW()),
('main_links', '用户服务', '{"links":[{"name":"联系我们","url":"#"}]}', '#004a9b', '#ffffff', 3, '0', NOW()),
('address', '联系地址', '{"address":"香港电子商务物流协会","show_friendship_links":true}', '#004a9b', '#ffffff', 4, '0', NOW()),
('qr_code', '微信公众号', '{"qr_image":"https://www.sinochemlt.com/portals/290/images/erweima.png","label":"微信公众号"}', '#004a9b', '#ffffff', 5, '0', NOW()),
('copyright', '版权信息', '{"copyright_text":"版权所有©2023香港电子商务物流协会集团有限公司 浙ICP备17038086号-2","tech_support":"技术支持：北京中恒电国际信息技术有限公司"}', '#ffffff', '#666666', 6, '0', NOW());

-- 页脚链接表
DROP TABLE IF EXISTS `footer_links`;
CREATE TABLE `footer_links` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '链接ID',
  `name` varchar(100) NOT NULL COMMENT '链接名称',
  `url` varchar(500) NOT NULL COMMENT '链接地址',
  `link_type` varchar(50) DEFAULT 'bottom' COMMENT '链接类型（bottom底部链接 friendship友情链接）',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0启用 1禁用）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='页脚链接表';

-- 插入默认链接数据
INSERT INTO `footer_links` (`name`, `url`, `link_type`, `sort_order`, `status`, `create_time`) VALUES
('网站地图', '#', 'bottom', 1, '0', NOW()),
('版权隐私', '#', 'bottom', 2, '0', NOW()),
('联系我们', '#', 'bottom', 3, '0', NOW());

-- 添加页脚配置管理菜单
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('页脚配置管理', 2000, 8, 'footerConfig', 'website/footerConfig/index', NULL, 1, 0, 'C', '0', '0', 'website:footerConfig:list', 'setting', 'admin', NOW(), '', NULL, '页脚配置管理菜单');

-- 获取刚插入的菜单ID
SET @footerConfigMenuId = LAST_INSERT_ID();

-- 添加页脚配置管理按钮
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('页脚配置查询', @footerConfigMenuId, 1, '#', '', NULL, 1, 0, 'F', '0', '0', 'website:footerConfig:query', '#', 'admin', NOW(), '', NULL, ''),
('页脚配置新增', @footerConfigMenuId, 2, '#', '', NULL, 1, 0, 'F', '0', '0', 'website:footerConfig:add', '#', 'admin', NOW(), '', NULL, ''),
('页脚配置修改', @footerConfigMenuId, 3, '#', '', NULL, 1, 0, 'F', '0', '0', 'website:footerConfig:edit', '#', 'admin', NOW(), '', NULL, ''),
('页脚配置删除', @footerConfigMenuId, 4, '#', '', NULL, 1, 0, 'F', '0', '0', 'website:footerConfig:remove', '#', 'admin', NOW(), '', NULL, ''),
('页脚配置导出', @footerConfigMenuId, 5, '#', '', NULL, 1, 0, 'F', '0', '0', 'website:footerConfig:export', '#', 'admin', NOW(), '', NULL, '');

-- 添加页脚链接管理菜单
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('页脚链接管理', 2000, 9, 'footerLink', 'website/footerLink/index', NULL, 1, 0, 'C', '0', '0', 'website:footerLink:list', 'link', 'admin', NOW(), '', NULL, '页脚链接管理菜单');

-- 获取刚插入的页脚链接菜单ID
SET @footerLinkMenuId = LAST_INSERT_ID();

-- 添加页脚链接管理按钮
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('页脚链接查询', @footerLinkMenuId, 1, '#', '', NULL, 1, 0, 'F', '0', '0', 'website:footerLink:query', '#', 'admin', NOW(), '', NULL, ''),
('页脚链接新增', @footerLinkMenuId, 2, '#', '', NULL, 1, 0, 'F', '0', '0', 'website:footerLink:add', '#', 'admin', NOW(), '', NULL, ''),
('页脚链接修改', @footerLinkMenuId, 3, '#', '', NULL, 1, 0, 'F', '0', '0', 'website:footerLink:edit', '#', 'admin', NOW(), '', NULL, ''),
('页脚链接删除', @footerLinkMenuId, 4, '#', '', NULL, 1, 0, 'F', '0', '0', 'website:footerLink:remove', '#', 'admin', NOW(), '', NULL, ''),
('页脚链接导出', @footerLinkMenuId, 5, '#', '', NULL, 1, 0, 'F', '0', '0', 'website:footerLink:export', '#', 'admin', NOW(), '', NULL, ''); 