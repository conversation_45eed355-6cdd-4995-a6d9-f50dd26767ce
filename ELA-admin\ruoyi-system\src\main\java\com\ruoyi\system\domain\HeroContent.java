package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 首页Hero内容对象 hero_content
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class HeroContent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 徽章文字中文 */
    @Excel(name = "徽章文字中文")
    private String badgeZh;

    /** 徽章文字英文 */
    @Excel(name = "徽章文字英文")
    private String badgeEn;

    /** 标题中文 */
    @Excel(name = "标题中文")
    private String titleZh;

    /** 标题英文 */
    @Excel(name = "标题英文")
    private String titleEn;

    /** 副标题中文 */
    @Excel(name = "副标题中文")
    private String subtitleZh;

    /** 副标题英文 */
    @Excel(name = "副标题英文")
    private String subtitleEn;

    /** 按钮配置JSON */
    @Excel(name = "按钮配置JSON")
    private String buttonsConfig;

    /** 统计数据配置JSON */
    @Excel(name = "统计数据配置JSON")
    private String statsConfig;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sort;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setBadgeZh(String badgeZh) 
    {
        this.badgeZh = badgeZh;
    }

    public String getBadgeZh() 
    {
        return badgeZh;
    }

    public void setBadgeEn(String badgeEn) 
    {
        this.badgeEn = badgeEn;
    }

    public String getBadgeEn() 
    {
        return badgeEn;
    }

    public void setTitleZh(String titleZh) 
    {
        this.titleZh = titleZh;
    }

    public String getTitleZh() 
    {
        return titleZh;
    }

    public void setTitleEn(String titleEn) 
    {
        this.titleEn = titleEn;
    }

    public String getTitleEn() 
    {
        return titleEn;
    }

    public void setSubtitleZh(String subtitleZh) 
    {
        this.subtitleZh = subtitleZh;
    }

    public String getSubtitleZh() 
    {
        return subtitleZh;
    }

    public void setSubtitleEn(String subtitleEn) 
    {
        this.subtitleEn = subtitleEn;
    }

    public String getSubtitleEn() 
    {
        return subtitleEn;
    }

    public void setButtonsConfig(String buttonsConfig) 
    {
        this.buttonsConfig = buttonsConfig;
    }

    public String getButtonsConfig() 
    {
        return buttonsConfig;
    }

    public void setStatsConfig(String statsConfig) 
    {
        this.statsConfig = statsConfig;
    }

    public String getStatsConfig() 
    {
        return statsConfig;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setSort(Integer sort) 
    {
        this.sort = sort;
    }

    public Integer getSort() 
    {
        return sort;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("badgeZh", getBadgeZh())
            .append("badgeEn", getBadgeEn())
            .append("titleZh", getTitleZh())
            .append("titleEn", getTitleEn())
            .append("subtitleZh", getSubtitleZh())
            .append("subtitleEn", getSubtitleEn())
            .append("buttonsConfig", getButtonsConfig())
            .append("statsConfig", getStatsConfig())
            .append("status", getStatus())
            .append("sort", getSort())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 