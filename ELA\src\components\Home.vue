<template>
  <div class="home">
    <!-- Hero Section - 主视觉区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <!-- Hero Carousel -->
        <div class="hero-carousel" v-if="banners && banners.length > 0 && !loading">
          <div class="hero-carousel-container">
            <div
              v-for="(banner, index) in banners"
              :key="banner.id"
              class="hero-carousel-slide"
              :class="{ active: currentHeroSlide === index }"
              :style="{ backgroundImage: `url(${banner.imageUrl})` }"
            >
              <div class="hero-slide-overlay"></div>
            </div>
          </div>

          <!-- Hero Carousel Navigation -->
          <div class="hero-carousel-nav" v-if="banners.length > 1">
            <button
              class="hero-nav-btn prev-btn"
              @click="prevHeroSlide"
              :disabled="currentHeroSlide === 0"
            >
              <i class="fas fa-chevron-left"></i>
            </button>
            <button
              class="hero-nav-btn next-btn"
              @click="nextHeroSlide"
              :disabled="currentHeroSlide === banners.length - 1"
            >
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>

          <!-- Hero Carousel Indicators -->
          <div class="hero-carousel-indicators" v-if="banners.length > 1">
            <button
              v-for="(banner, index) in banners"
              :key="banner.id"
              class="hero-indicator"
              :class="{ active: currentHeroSlide === index }"
              @click="goToHeroSlide(index)"
            ></button>
          </div>
        </div>

        <!-- Hero Loading State -->
        <!-- 加载时显示默认内容，避免空白 -->
        <div v-if="loading" class="hero-container">
          <div class="hero-content" data-aos="fade-up" data-aos-duration="1000">
            <div class="hero-badge" data-aos="fade-down" data-aos-delay="200">
              <span>{{ currentLang === 'zh' ? '香港電商物流協會' : 'Hong Kong E-commerce Logistics Association' }}</span>
            </div>
            <h1 class="hero-title" data-aos="fade-up" data-aos-delay="300">
              <span v-if="currentLang === 'zh'">
                推動香港成為<br>
                <span class="highlight">全球電商物流樞紐</span>
              </span>
              <span v-else>
                Driving Hong Kong as<br>
                <span class="highlight">Global E-commerce Hub</span>
              </span>
            </h1>
            <p class="hero-subtitle" data-aos="fade-up" data-aos-delay="500">
              <span v-if="currentLang === 'zh'">
                匯聚業界精英，推動行業發展，培育專業人才，建設可持續的電商物流生態系統
              </span>
              <span v-else>
                Uniting industry leaders, driving sector growth, nurturing professionals, building sustainable e-commerce logistics ecosystem
              </span>
            </p>
            <div class="hero-actions" data-aos="fade-up" data-aos-delay="700">
              <router-link to="/join-us" class="btn-primary">
                <span>{{ currentLang === 'zh' ? '加入我們' : 'Join Us' }}</span>
                <i class="fas fa-arrow-right"></i>
              </router-link>
              <router-link to="/about" class="btn-secondary">
                <span>{{ currentLang === 'zh' ? '了解更多' : 'Learn More' }}</span>
              </router-link>
            </div>
          </div>
        </div>

      </div>
      <div class="hero-container">
        <div class="hero-content" data-aos="fade-up" data-aos-duration="1000" v-if="heroContent">
          <div class="hero-badge" data-aos="fade-down" data-aos-delay="200">
            <span>{{ currentLang === 'zh' ? heroContent.badgeZh : heroContent.badgeEn }}</span>
          </div>
          <h1 class="hero-title" data-aos="fade-up" data-aos-delay="300" v-html="currentLang === 'zh' ? heroContent.titleZh : heroContent.titleEn">
          </h1>
          <p class="hero-subtitle" data-aos="fade-up" data-aos-delay="500">
            <span v-if="currentLang === 'zh'">
              {{ heroContent.subtitleZh }}
            </span>
            <span v-else>
              {{ heroContent.subtitleEn }}
            </span>
          </p>
          <div class="hero-actions" data-aos="fade-up" data-aos-delay="700" v-if="heroContent && heroContent.buttonsConfig && isValidJson(heroContent.buttonsConfig)">
            <router-link
              v-for="button in parseJson(heroContent.buttonsConfig)"
              :key="button.url"
              :to="button.url"
              :class="'btn-' + button.type"
            >
              <span>{{ currentLang === 'zh' ? button.text.zh : button.text.en }}</span>
              <i v-if="button.icon" :class="button.icon"></i>
            </router-link>
          </div>
        </div>
        <div class="hero-stats" data-aos="fade-up" data-aos-delay="900" v-if="heroContent && heroContent.statsConfig && isValidJson(heroContent.statsConfig)">
          <div class="stat-item" v-for="stat in parseJson(heroContent.statsConfig)" :key="stat.number">
            <div class="stat-number">{{ stat.number }}</div>
            <div class="stat-label">{{ currentLang === 'zh' ? stat.label.zh : stat.label.en }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- About Section - 我們的使命 -->
    <section class="about-section">
      <!-- Background Image -->
      <div class="mission-background">
        <img :src="aboutUs && aboutUs.backgroundImageUrl ? aboutUs.backgroundImageUrl : require('@/assets/index/Mission-background.png')" alt="Mission Background" class="mission-bg-image">
      </div>
      <div class="container">
        <div class="section-intro" data-aos="fade-up">
          <h2 class="section-title">{{ aboutUs ? (currentLang === 'zh' ? aboutUs.titleZh : aboutUs.titleEn) : (currentLang === 'zh' ? '我們的使命' : 'Our Mission') }}</h2>
          <p class="section-subtitle">
            <span v-if="aboutUs">
              {{ currentLang === 'zh' ? aboutUs.subtitleZh : aboutUs.subtitleEn }}
            </span>
            <!-- <span v-else>
              <span v-if="currentLang === 'zh'">
                專業性強、服務優質，致力於為客戶提供卓越的產品和服務
              </span>
              <span v-else>
                Professional excellence, quality service, committed to providing outstanding products and services
              </span>
            </span> -->
          </p>
        </div>

        <!-- Company Profile Layout -->
        <div class="company-profile">
          <!-- Left Content -->
          <div class="profile-content" data-aos="fade-right" data-aos-delay="200">
            <h3 class="company-name">
              {{ aboutUs ? (currentLang === 'zh' ? aboutUs.companyNameZh : aboutUs.companyNameEn) : (currentLang === 'zh' ? '香港電商物流協會' : 'Hong Kong E-commerce Logistics Association') }}
            </h3>

            <div class="company-description">
              <div v-if="aboutUs">
                <div v-if="currentLang === 'zh'" v-html="aboutUs.descriptionZh ? aboutUs.descriptionZh.replace(/\n/g, '</p><p>') : ''"></div>
                <div v-else v-html="aboutUs.descriptionEn ? aboutUs.descriptionEn.replace(/\n/g, '</p><p>') : ''"></div>
              </div>
              <div v-else>
                <p v-if="currentLang === 'zh'">
                  我們旨在匯聚一群熱心回饋社會和行業的精英，通過評估香港物流及供應鏈各持份者的需求，向政府反映實際狀況，並提供建設性意見以改善基礎設施和科技應用，從而提升整體電子商貿行業的競爭力。
                </p>
                <p v-else>
                  We seek to bring together a group of elite professionals passionate about the industry. By evaluating
                  the needs of various stakeholders in Hong Kong's logistics and supply chain sectors, we communicate the
                  current situation to the government and provide constructive recommendations to enhance infrastructure
                  and technology applications, ultimately boosting the competitiveness of the E-commerce industry.
                </p>

                <p v-if="currentLang === 'zh'">
                  協會致力於推動電子商貿物流業的發展，為會員提供一個互動平台，讓大家分享專業意見、增進合作機會，並隨時交流國際市場趨勢，共同打造更佳的商業環境，提升服務質量。
                </p>
                <p v-else>
                  The Association is committed to advancing the E-commerce sector. We provide our members with an
                  interactive platform to share insights, encourage collaboration, and exchange information on global
                  market trends. Together, we aim to create a better business environment and improve service quality.
                </p>

                <p v-if="currentLang === 'zh'">
                  我們也將推廣香港電子商貿物流行業在國際市場的發展，促進市場繁榮，為會員創造更多商機和成長。
                </p>
                <p v-else>
                  Furthermore, we strive to promote the growth of Hong Kong's E-commerce logistics industry in
                  international markets, fostering market prosperity and generating more opportunities for our members.
                </p>

                <p v-if="currentLang === 'zh'">
                  同時，我們致力於培育年輕專才，鼓勵他們加入電子商貿物流行業。
                </p>
                <p v-else>
                  At the same time, we are dedicated to nurturing young talent and encouraging their entry into the
                  E-commerce logistics field.
                </p>
              </div>
            </div>

            <!-- Statistics -->
            <div class="statistics-grid">
              <div v-if="aboutUs && aboutUs.statisticsConfig && isValidJson(aboutUs.statisticsConfig)" class="stat-item" v-for="stat in parseJson(aboutUs.statisticsConfig)" :key="stat.number">
                <div class="stat-number">{{ stat.number }}<span class="stat-unit">{{ currentLang === 'zh' ? stat.unit.zh : stat.unit.en }}</span></div>
                <div class="stat-label">{{ currentLang === 'zh' ? stat.label.zh : stat.label.en }}</div>
                <div class="stat-wave">
                  <svg viewBox="0 0 100 20" class="wave-svg">
                    <path d="M0,10 Q25,0 50,10 T100,10" stroke="#a0aec0" stroke-width="2" fill="none"/>
                  </svg>
                </div>
              </div>
              <!-- 默认统计数据 -->
              <!--<template v-if="!aboutUs || !aboutUs.statisticsConfig">-->
              <!--  <div class="stat-item">-->
              <!--    <div class="stat-number">{{ currentLang === 'zh' ? '5' : '5' }}<span-->
              <!--      class="stat-unit">{{ currentLang === 'zh' ? '年' : 'Years' }}</span></div>-->
              <!--    <div class="stat-label">{{ currentLang === 'zh' ? '系統開發經驗' : 'Development Experience' }}</div>-->
              <!--    <div class="stat-wave">-->
              <!--      <svg viewBox="0 0 100 20" class="wave-svg">-->
              <!--        <path d="M0,10 Q25,0 50,10 T100,10" stroke="#a0aec0" stroke-width="2" fill="none"/>-->
              <!--      </svg>-->
              <!--    </div>-->
              <!--  </div>-->

              <!--  <div class="stat-item">-->
              <!--    <div class="stat-number">{{ currentLang === 'zh' ? '50' : '50' }}<span-->
              <!--      class="stat-unit">{{ currentLang === 'zh' ? '項' : '+' }}</span></div>-->
              <!--    <div class="stat-label">{{ currentLang === 'zh' ? '行業活動' : 'Industry Events' }}</div>-->
              <!--    <div class="stat-wave">-->
              <!--      <svg viewBox="0 0 100 20" class="wave-svg">-->
              <!--        <path d="M0,10 Q25,0 50,10 T100,10" stroke="#a0aec0" stroke-width="2" fill="none"/>-->
              <!--      </svg>-->
              <!--    </div>-->
              <!--  </div>-->

              <!--  <div class="stat-item">-->
              <!--    <div class="stat-number">{{ currentLang === 'zh' ? '200' : '200' }}<span-->
              <!--      class="stat-unit">{{ currentLang === 'zh' ? '家' : '+' }}</span></div>-->
              <!--    <div class="stat-label">{{ currentLang === 'zh' ? '合作會員企業' : 'Member Companies' }}</div>-->
              <!--    <div class="stat-wave">-->
              <!--      <svg viewBox="0 0 100 20" class="wave-svg">-->
              <!--        <path d="M0,10 Q25,0 50,10 T100,10" stroke="#a0aec0" stroke-width="2" fill="none"/>-->
              <!--      </svg>-->
              <!--    </div>-->
              <!--  </div>-->

              <!--  <div class="stat-item">-->
              <!--    <div class="stat-number">{{ currentLang === 'zh' ? '1000' : '1000' }}<span-->
              <!--      class="stat-unit">{{ currentLang === 'zh' ? '人' : '+' }}</span></div>-->
              <!--    <div class="stat-label">{{ currentLang === 'zh' ? '服務專業人士' : 'Professionals Served' }}</div>-->
              <!--    <div class="stat-wave">-->
              <!--      <svg viewBox="0 0 100 20" class="wave-svg">-->
              <!--        <path d="M0,10 Q25,0 50,10 T100,10" stroke="#a0aec0" stroke-width="2" fill="none"/>-->
              <!--      </svg>-->
              <!--    </div>-->
              <!--  </div>-->
              <!--</template>-->
            </div>
          </div>

          <!-- Right Image -->
          <div class="profile-image" data-aos="fade-left" data-aos-delay="400">
            <div class="image-container">
              <img :src="aboutUs && aboutUs.imageUrl ? aboutUs.imageUrl : require('@/assets/index/company.png')" alt="HKELA Office">
              <!--<div class="image-overlay">-->
              <!--  <div class="location-badge">-->
              <!--    <i class="fas fa-map-marker-alt"></i>-->
              <!--  </div>-->
              <!--</div>-->
            </div>
          </div>
        </div>

        <!-- Mission Cards (Simplified) -->
        <div class="mission-overview" data-aos="fade-up" data-aos-delay="600">
          <div class="mission-cards">
            <div v-if="aboutUs && aboutUs.missionCardsConfig && isValidJson(aboutUs.missionCardsConfig)" class="mission-card-simple" v-for="card in parseJson(aboutUs.missionCardsConfig)" :key="card.title.zh">
              <div class="card-icon">
                <i :class="card.icon"></i>
              </div>
              <h4>{{ currentLang === 'zh' ? card.title.zh : card.title.en }}</h4>
              <p>{{ currentLang === 'zh' ? card.description.zh : card.description.en }}</p>
            </div>
            <!-- 默认使命卡片 -->
            <template v-if="!aboutUs || !aboutUs.missionCardsConfig">
              <div class="mission-card-simple">
                <div class="card-icon">
                  <i class="fas fa-users"></i>
                </div>
                <h4>{{ currentLang === 'zh' ? '匯聚精英' : 'Elite Network' }}</h4>
                <p>{{
                    currentLang === 'zh' ? '匯聚業界精英，推動行業發展' : 'Uniting industry elites to drive development'
                  }}</p>
              </div>

              <div class="mission-card-simple">
                <div class="card-icon">
                  <i class="fas fa-rocket"></i>
                </div>
                <h4>{{ currentLang === 'zh' ? '推動創新' : 'Drive Innovation' }}</h4>
                <p>{{
                    currentLang === 'zh' ? '提供專業平台，促進合作交流' : 'Professional platform for collaboration'
                  }}</p>
              </div>

              <div class="mission-card-simple">
                <div class="card-icon">
                  <i class="fas fa-globe-asia"></i>
                </div>
                <h4>{{ currentLang === 'zh' ? '國際視野' : 'Global Vision' }}</h4>
                <p>{{ currentLang === 'zh' ? '推廣國際市場，創造更多機會' : 'Promoting international markets' }}</p>
              </div>

              <div class="mission-card-simple">
                <div class="card-icon">
                  <i class="fas fa-graduation-cap"></i>
                </div>
                <h4>{{ currentLang === 'zh' ? '人才培育' : 'Talent Development' }}</h4>
                <p>{{ currentLang === 'zh' ? '培育年輕人才，傳承行業知識' : 'Nurturing young talents for the future' }}</p>
              </div>
            </template>
          </div>
        </div>
      </div>
    </section>

    <!-- Vision Section - 关于我们 -->
    <section class="vision-section">
      <div class="container">
        <div class="vision-layout">
          <!-- Left Side - Visual Elements -->
          <div class="vision-visual" data-aos="fade-right">
            <div class="visual-container">
              <!-- Background Geometric Shapes -->
              <div class="geometric-bg">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
              </div>

              <!-- Main Visual Content -->
              <div class="visual-content">
                <!-- Orange Labels -->
                <div class="feature-label label-top" data-aos="fade-up" data-aos-delay="200">
                  <div class="label-icon">
                    <i class="fas fa-shipping-fast"></i>
                  </div>
                  <div class="label-text">
                    <span class="label-category">HKELA</span>
                    <span class="label-title">{{ currentLang === 'zh' ? '电商物流' : 'E-commerce Logistics' }}</span>
                  </div>
                </div>

                <!-- Central Icons/Elements -->
                <div class="central-elements">
                  <div class="element-item" data-aos="zoom-in" data-aos-delay="300">
                    <i class="fas fa-globe-asia"></i>
                  </div>
                  <div class="element-item" data-aos="zoom-in" data-aos-delay="400">
                    <i class="fas fa-network-wired"></i>
                  </div>
                  <div class="element-item" data-aos="zoom-in" data-aos-delay="500">
                    <i class="fas fa-rocket"></i>
                  </div>
                </div>

                <!-- Bottom Label -->
                <div class="feature-label label-bottom" data-aos="fade-up" data-aos-delay="600">
                  <div class="label-icon">
                    <i class="fas fa-users"></i>
                  </div>
                  <div class="label-text">
                    <span class="label-category">HKELA</span>
                    <span class="label-title">{{ currentLang === 'zh' ? '人才培育' : 'Talent Development' }}</span>
                  </div>
                </div>
              </div>

              <!-- Connecting Lines -->
              <svg class="connection-lines" viewBox="0 0 400 400">
                <line x1="100" y1="80" x2="200" y2="180" class="connection-line"/>
                <line x1="200" y1="300" x2="300" y2="200" class="connection-line"/>
              </svg>
            </div>
          </div>

          <!-- Right Side - Content -->
          <div class="vision-content" data-aos="fade-left">
            <div class="content-header">
              <h2 class="section-title">
                <span v-if="aboutUs && aboutUs.visionTitleZh && aboutUs.visionTitleEn">
                  {{ currentLang === 'zh' ? aboutUs.visionTitleZh : aboutUs.visionTitleEn }}
                </span>
                <span v-else>
                  {{ currentLang === 'zh' ? '願景' : 'Vision' }}
                </span>
              </h2>
            </div>

            <div class="content-body">
              <div class="vision-text">
                <div v-if="aboutUs && aboutUs.visionContentZh && aboutUs.visionContentEn">
                  <div v-if="currentLang === 'zh'" v-html="aboutUs.visionContentZh ? '<p>' + aboutUs.visionContentZh.replace(/\n/g, '</p><p>') + '</p>' : ''"></div>
                  <div v-else v-html="aboutUs.visionContentEn ? '<p>' + aboutUs.visionContentEn.replace(/\n/g, '</p><p>') + '</p>' : ''"></div>
                </div>
                <div v-else>
                  <p v-if="currentLang === 'zh'">
                    香港電商物流協會 (HKELA) 致力於成為本港物流業的領先電子商貿協會，並採取積極務實和有效的措施，推動香港成為全球及可持續發展的電子商貿中心。
                  </p>
                  <p v-else>
                    The Hong Kong E-commerce Logistics Association (HKELA) is committed to becoming the leading E-commerce
                    association in the local logistics sector. We adopt proactive, practical, and effective measures to
                    position Hong Kong as a global and sustainable E-commerce hub.
                  </p>

                  <p v-if="currentLang === 'zh'">
                    電子商貿物流擁有巨大的發展潛力，因此必須促進本地電子商貿物流人才的培育及科技應用，以滿足市場發展的需求。
                  </p>
                  <p v-else>
                    E-commerce logistics has significant growth potential, so it is essential to promote the development
                    of local talent and technology applications to meet the evolving needs of the market.
                  </p>
                </div>
              </div>

              <div class="content-cta">
                <a :href="aboutUs && aboutUs.visionButtonUrl ? aboutUs.visionButtonUrl : '#'" class="vision-btn">
                  <span v-if="aboutUs && aboutUs.visionButtonZh && aboutUs.visionButtonEn">
                    {{ currentLang === 'zh' ? aboutUs.visionButtonZh : aboutUs.visionButtonEn }}
                  </span>
                  <span v-else>
                    {{ currentLang === 'zh' ? '阅读更多' : 'Read More' }}
                  </span>
                  <i class="fas fa-arrow-right"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Leadership Messages Sections - 动态渲染领导力消息 -->
    <div v-if="leadershipMessages && leadershipMessages.length > 0" class="leadership-messages-sections">
      <section
        v-for="(message, index) in leadershipMessages"
        :key="message.id"
        class="leadership-message-section"
        :class="{ 'alternate-bg': index % 2 === 1 }"
        v-show="message.status === '0'"
      >
        <!-- Background Image -->
        <div v-if="message.backgroundImageUrl" class="leadership-background">
          <img :src="message.backgroundImageUrl" alt="Leadership Background" class="leadership-bg-image">
        </div>
        <div class="container">
          <!-- Section Header -->
          <div class="leadership-header" data-aos="fade-up">
            <h2 class="leadership-main-title">
              {{ currentLang === 'zh' ? message.titleZh : message.titleEn }}
            </h2>
          </div>

          <!-- Leadership Content -->
          <div class="leadership-layout" data-aos="fade-up" data-aos-delay="200">
            <!-- Modern Card Layout -->
            <div class="modern-profile-card">
              <!-- Left Side - Info -->
              <div class="profile-info">
                <div class="welcome-text">
                  {{ currentLang === 'zh' ? message.titleZh : message.titleEn }}
                </div>

                <div class="profile-name">
                  <h3>{{ currentLang === 'zh' ? message.nameZh : message.nameEn }}</h3>
                </div>

                <div class="profile-title">
                  {{ currentLang === 'zh' ? message.positionZh : message.positionEn }}
                </div>

                <div class="profile-company">
                  {{ currentLang === 'zh' ? message.companyZh : message.companyEn }}
                </div>

                <div class="profile-section">
                  <h4 class="section-title">
                    <span v-if="currentLang === 'zh'">寄語</span>
                    <span v-else>Message</span>
                  </h4>

                  <!-- Message Content -->
                  <div class="message-content">
                    <div v-if="currentLang === 'zh'" v-html="message.messageZh ? '<p>' + message.messageZh.replace(/\n/g, '</p><p>') + '</p>' : ''"></div>
                    <div v-else v-html="message.messageEn ? '<p>' + message.messageEn.replace(/\n/g, '</p><p>') + '</p>' : ''"></div>
                  </div>
                </div>
              </div>

              <!-- Right Side - Portrait -->
              <div class="profile-portrait">
                <div class="portrait-image-container">
                  <img :src="message.imageUrl" :alt="currentLang === 'zh' ? message.nameZh : message.nameEn" class="portrait-image">
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- 如果没有配置数据，显示默认的领导力消息 -->
    <div v-if="!leadershipMessages || leadershipMessages.length === 0">
      <!-- Leadership Section - 名譽顧問的話 -->
      <section class="leadership-section">
        <!-- Background Image -->
        <div class="leadership-background">
          <img src="@/assets/index/leadership-section.png" alt="Leadership Background" class="leadership-bg-image">
        </div>
        <div class="container">
          <!-- Section Header -->
          <div class="leadership-header" data-aos="fade-up">
                          <h2 class="leadership-main-title">
                <span v-if="currentLang === 'zh'">名譽顧問的話</span>
                <span v-else>Message from Honorary Advisor</span>
              </h2>
          </div>

          <!-- Leadership Content -->
          <div class="leadership-layout" data-aos="fade-up" data-aos-delay="200">
            <!-- Modern Card Layout -->
            <div class="modern-profile-card">
              <!-- Left Side - Info -->
              <div class="profile-info">
                                  <div class="welcome-text">
                    <span v-if="currentLang === 'zh'">名譽顧問的話</span>
                    <span v-else>Message from Honorary Advisor</span>
                  </div>

                <div class="profile-name">
                  <h3 v-if="currentLang === 'zh'">易志明</h3>
                  <h3 v-else>Frankie Yick</h3>
                </div>

                <div class="profile-title">
                  <span v-if="currentLang === 'zh'">首席顧問 • GBS, JP</span>
                  <span v-else>Chief Advisor • GBS, JP</span>
                </div>

                <div class="profile-company">
                  <span v-if="currentLang === 'zh'">九龍倉集團</span>
                  <span v-else>Wharf Group</span>
                </div>

                <div class="profile-section">
                  <h4 class="section-title">
                    <span v-if="currentLang === 'zh'">寄語</span>
                    <span v-else>Message</span>
                  </h4>

                  <!-- Chinese Message -->
                  <div class="message-content" v-if="currentLang === 'zh'">
                    <p>
                      由衷地祝賀一群年輕企業家與本地物流業電子商貿領域的業界人士成立香港電商物流協會（HKELA），致力於推動香港電子商貿物流及為行業的可持續發展培育青年人才。
                    </p>
                    <p>
                      香港地理位置優越，是打入中國市場的門檻，而且數十年來一直是主要的貿易樞紐之一。隨著中國電子商貿市場的進一步發展，將為有志於在未來於電子商貿行業大展拳腳的年輕人提供龐大的機會！
                    </p>
                  </div>

                  <!-- English Message -->
                  <div class="message-content" v-else>
                    <p>
                      My heartfelt congratulations on the establishment of the Hong Kong E-commerce Logistics Association
                      (HKELA) by a group of young entrepreneurs and participants in the E-commerce segment of the local
                      logistics industry with the goal to promote the Hong Kong E-commerce logistics and nurture young
                      talents for the sustainable development of the industry.
                    </p>
                    <p>
                      Hong Kong is well positioned as the gateway of China and has been one of the major trading hubs for
                      decades. The further development of the E-commerce market in China will provide great opportunities
                      for the youngsters who are interested to develop themselves in the E-commerce business in the years
                      to come!
                    </p>
                  </div>
                </div>
              </div>

              <!-- Right Side - Portrait -->
              <div class="profile-portrait">
                <div class="portrait-image-container">
                  <img src="@/assets/index/Chief-Advisor.jpg" alt="Chief Advisor" class="portrait-image">
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- President Message Section - 會長的話 -->
      <section class="president-section">
        <div class="container">
          <!-- Section Header -->
          <div class="president-header" data-aos="fade-up">
            <h2 class="president-main-title">
              <span v-if="currentLang === 'zh'">會長的話</span>
              <span v-else>Message from President</span>
            </h2>
          </div>

          <!-- President Content -->
          <div class="president-layout" data-aos="fade-up" data-aos-delay="200">
            <!-- Modern Card Layout -->
            <div class="modern-profile-card">
              <!-- Left Side - Info -->
              <div class="profile-info">
                <div class="welcome-text">
                  <span v-if="currentLang === 'zh'">會長的話</span>
                  <span v-else>Message from President</span>
                </div>

                <div class="profile-name">
                  <h3 v-if="currentLang === 'zh'">張淑明</h3>
                  <h3 v-else>Suki Cheung</h3>
                </div>

                <div class="profile-title">
                  <span v-if="currentLang === 'zh'">會長</span>
                  <span v-else>President</span>
                </div>

                <div class="profile-company">
                  <span v-if="currentLang === 'zh'">香港電商物流協會</span>
                  <span v-else>Hong Kong E-commerce Logistics Association</span>
                </div>

                <div class="profile-section">
                  <h4 class="section-title">
                    <span v-if="currentLang === 'zh'">寄語</span>
                    <span v-else>Message</span>
                  </h4>

                  <!-- Chinese Message -->
                  <div class="message-content" v-if="currentLang === 'zh'">
                    <p>
                      隨著電子商務的快速發展，全球貿易生態系統正經歷顯著變化。這一增長不僅重新定義了價值鏈，還強化了香港作為全球超級連繫人的角色。
                    </p>
                    <p>
                      作為推動香港電子商務樞紐的主要機構，香港電商物流協會（HKELA）深知物流對於傳遞價值和連接買賣雙方的重要性。此外，香港作為全球金融中心和研發樞紐，更能為全球電子商務生態系統提供關鍵支持和增值。
                    </p>
                    <p>
                      因此，我們匯聚了來自物流、供應鏈、科技和金融領域的專家與專業人士，共同促進電子商務和全球貿易新生態系統的建立。
                    </p>
                    <p>
                      同時，我們致力於培養行業的新一代領袖，確保行業的可持續發展與傳承。如果您也懷有相同的目標及熱情，歡迎加入我們！
                    </p>
                  </div>

                  <!-- English Message -->
                  <div class="message-content" v-else>
                    <p>
                      The rapid growth of e-commerce is transforming the global trade ecosystem. This surge not only
                      redefines the value chain but also enhances Hong Kong's role as a super connector in the world.
                    </p>
                    <p>
                      As a leading advocate for Hong Kong's e-commerce hub, HKELA recognizes that logistics is fundamental
                      to delivering value and connecting buyers and sellers. Additionally, as a global financial center
                      and a hub for research and development, Hong Kong can significantly contribute to and support the
                      global e-commerce landscape.
                    </p>
                    <p>
                      To this end, we bring together experts and professionals from the logistics, supply chain,
                      technology, and finance sectors to foster the creation of a new ecosystem for e-commerce and global
                      trade.
                    </p>
                    <p>
                      We are also deeply committed to nurturing the next generation of industry leaders for sustainable
                      development. Join us if you share our passion!
                    </p>
                  </div>
                </div>
              </div>

              <!-- Right Side - Portrait -->
              <div class="profile-portrait">
                <div class="portrait-image-container">
                  <img src="@/assets/index/President.jpg" alt="President" class="portrait-image">
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Events Section - 精彩時刻 -->
    <section class="events-section">
      <div class="container">
        <!-- 标题区域 -->
        <div class="events-header" data-aos="fade-up">
          <h2 class="events-main-title">
            {{ currentLang === 'zh' ? 'HKELA精彩活動' : 'HKELA Events' }}
          </h2>
          <p class="events-subtitle">
            <span v-if="currentLang === 'zh'">
              每一次突破與創新都是我們與合作伙伴共同創造的精彩時刻
            </span>
            <span v-else>
              Every breakthrough and innovation is a wonderful moment we create together with our partners
            </span>
          </p>
        </div>

        <!-- 活动列表 -->
        <div class="activities-list" v-loading="eventsLoading">
          <div
            v-for="(event, index) in events"
            :key="event.id"
            class="activity-card"
            :style="{ backgroundImage: `url(${event.image})` }"
            :data-aos="index % 2 === 0 ? 'fade-right' : 'fade-left'"
            :data-aos-delay="index * 100"
          >
            <div class="activity-overlay">
              <div class="activity-content">
                <h3 class="activity-title">
                  {{ currentLang === 'zh' ? event.titleZh : event.titleEn }}
                </h3>
                <div class="activity-divider"></div>
                <p class="activity-description">
                  {{ currentLang === 'zh' ? event.descriptionZh : event.descriptionEn }}
                </p>
              </div>
            </div>
          </div>

          <!-- 当没有活动数据时显示提示 -->
          <div v-if="!eventsLoading && events.length === 0" class="empty-state">
            <div class="empty-state-content">
              <i class="fas fa-calendar-alt"></i>
              <p>{{ currentLang === 'zh' ? '暫無精彩活動' : 'No events available' }}</p>
            </div>
          </div>
        </div>

        <!-- 查看更多按钮 -->
        <div class="events-footer" data-aos="fade-up" data-aos-delay="400">
          <router-link to="/pr-events" class="view-more-btn">
            {{ currentLang === 'zh' ? '查看更多活動' : 'View More Events' }}
            <i class="fas fa-arrow-right"></i>
          </router-link>
        </div>
      </div>
    </section>

  </div>
</template>

<script>
import {getHomeData} from '@/api/home'
import {getHomepageActivities} from '@/api/website/activities'

export default {
  name: 'Home',
  props: {
    currentLang: {
      type: String,
      default: 'zh'
    }
  },
  data() {
    return {
      banners: [],
      heroContent: {},
      aboutUs: {},
      leadershipMessages: [],
      loading: false,
      currentHeroSlide: 0,
      heroCarouselInterval: null,
      events: [],
      eventsLoading: false
    }
  },
  created() {
    console.log('组件创建，开始获取数据...') // 调试日志
    // 延迟获取数据，让页面先渲染默认内容
    this.$nextTick(() => {
      this.getHomeData()
      this.getHomepageActivities()
    })
  },
  mounted() {
    console.log('组件挂载...') // 调试日志

    // 初始化AOS動畫
    if (typeof window !== 'undefined' && window.AOS) {
      window.AOS.init({
        duration: 1000,
        easing: 'ease-out-cubic',
        once: true,
        offset: 120,
        delay: 0,
        anchorPlacement: 'top-bottom'
      })
    }

    // 添加滾動視差效果
    this.initParallaxEffect()

    // 添加數字計數動畫
    this.initCountingAnimation()
  },

  beforeDestroy() {
    // 清理轮播图定时器
    this.stopHeroCarousel()

    // 清理滚动事件监听器
    if (typeof window !== 'undefined') {
      window.removeEventListener('scroll', this.handleScroll)
    }
  },
  methods: {
    // 获取首页数据
    async getHomeData() {
      try {
        // 设置一个较短的加载时间，避免用户看到加载状态
        const loadingPromise = new Promise(resolve => setTimeout(resolve, 500))

        const response = await getHomeData()
        console.log('API响应:', response) // 调试日志

        if (response && response.code === 200) {
          this.banners = response.banners || []
          this.heroContent = response.heroContent || {}
          this.aboutUs = response.aboutUs || {}
          this.leadershipMessages = response.leadershipMessages || []
          console.log('Banner数据:', this.banners) // 调试日志
          console.log('Hero内容数据:', this.heroContent) // 调试日志
          console.log('关于我们数据:', this.aboutUs) // 调试日志
          console.log('领导力消息数据:', this.leadershipMessages) // 调试日志

          // 等待最短加载时间，然后启动轮播图
          await loadingPromise
          this.$nextTick(() => {
            this.startHeroCarousel()
          })
        }
      } catch (error) {
        console.error('获取首页数据失败:', error)
        // 即使API失败，也要确保页面正常显示
        this.banners = []
        this.heroContent = {}
        this.aboutUs = {}
        this.leadershipMessages = []
        // 静默处理错误，不显示加载状态
      } finally {
        // 延迟设置loading为false，避免闪烁
        setTimeout(() => {
          this.loading = false
        }, 100)
      }
    },

    // 获取首页活动数据
    async getHomepageActivities() {
      try {
        this.eventsLoading = true
        const response = await getHomepageActivities()
        console.log('首页活动API响应:', response) // 调试日志

        if (response && response.code === 200) {
          // 将后端数据转换为前端需要的格式
          this.events = response.data.map(activity => ({
            id: activity.id,
            image: activity.coverImageUrl || require('@/assets/index/active.jpg'),
            titleZh: activity.title,
            titleEn: activity.titleEn || activity.title, // 使用英文标题，如果没有则回退到中文标题
            descriptionZh: activity.content ? activity.content.replace(/<[^>]*>/g, '').substring(0, 200) + '...' : '',
            descriptionEn: (activity.contentEn || activity.content) ? (activity.contentEn || activity.content).replace(/<[^>]*>/g, '').substring(0, 200) + '...' : ''
          }))
          console.log('首页活动数据:', this.events) // 调试日志
        }
      } catch (error) {
        console.error('获取首页活动数据失败:', error)
        // 静默处理错误，保持默认活动数据
      } finally {
        this.eventsLoading = false
      }
    },

    // 視差滾動效果
    initParallaxEffect() {
      this.handleScroll = () => {
        const scrolled = window.pageYOffset
        const rate = scrolled * -0.5

        const heroParticles = document.querySelector('.hero-particles')
        if (heroParticles) {
          heroParticles.style.transform = `translateY(${rate}px)`
        }
      }

      window.addEventListener('scroll', this.handleScroll)
    },

    // 數字計數動畫
    initCountingAnimation() {
      const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
      }

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const statNumbers = entry.target.querySelectorAll('.stat-number')
            statNumbers.forEach(number => {
              this.animateNumber(number)
            })
            observer.unobserve(entry.target)
          }
        })
      }, observerOptions)

      const statsSection = document.querySelector('.hero-stats')
      if (statsSection) {
        observer.observe(statsSection)
      }
    },

    // 數字動畫函數
    animateNumber(element) {
      const finalNumber = parseInt(element.textContent)
      const duration = 2000
      const startTime = performance.now()

      const updateNumber = (currentTime) => {
        const elapsed = currentTime - startTime
        const progress = Math.min(elapsed / duration, 1)

        // 使用緩動函數
        const easeOutQuart = 1 - Math.pow(1 - progress, 4)
        const currentNumber = Math.floor(easeOutQuart * finalNumber)

        element.textContent = currentNumber

        if (progress < 1) {
          requestAnimationFrame(updateNumber)
        } else {
          element.textContent = finalNumber
        }
      }

      requestAnimationFrame(updateNumber)
    },

    // Hero轮播图相关方法
    startHeroCarousel() {
      if (this.banners && this.banners.length > 1) {
        this.heroCarouselInterval = setInterval(() => {
          this.nextHeroSlide()
        }, 4000) // 4秒自动切换
      }
    },

    stopHeroCarousel() {
      if (this.heroCarouselInterval) {
        clearInterval(this.heroCarouselInterval)
        this.heroCarouselInterval = null
      }
    },

    nextHeroSlide() {
      if (this.banners && this.banners.length > 1) {
        this.currentHeroSlide = (this.currentHeroSlide + 1) % this.banners.length
      }
    },

    prevHeroSlide() {
      if (this.banners && this.banners.length > 1) {
        this.currentHeroSlide = this.currentHeroSlide === 0
          ? this.banners.length - 1
          : this.currentHeroSlide - 1
      }
    },

    goToHeroSlide(index) {
      if (index >= 0 && index < this.banners.length) {
        this.currentHeroSlide = index
      }
    },
    
    // 检查字符串是否为有效的JSON
    isValidJson(str) {
      // 检查输入是否为有效字符串
      if (!str || typeof str !== 'string' || str.trim() === '') {
        return false
      }
      
      try {
        JSON.parse(str)
        return true
      } catch (e) {
        console.warn('JSON验证失败:', e.message, '字符串:', str)
        return false
      }
    },
    
    // 安全地解析JSON字符串
    parseJson(str) {
      // 检查输入是否为有效字符串
      if (!str || typeof str !== 'string' || str.trim() === '') {
        console.warn('parseJson: 输入为空或无效，返回空数组')
        return []
      }
      
      try {
        const result = JSON.parse(str)
        return result || []
      } catch (e) {
        console.error('JSON解析错误:', e, '无效的JSON字符串:', str)
        return []
      }
    }
  }
}
</script>

<style scoped>
:root {
  --primary-color: #1a365d;
  --primary-light: #2d5a87;
  --primary-dark: #0f2942;
  --secondary-color: #e2e8f0;
  --accent-color: #d86337;
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-light: #718096;
  --background-light: #f7fafc;
  --background-white: #ffffff;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.home {
  width: 100%;
  overflow-x: hidden;
}

/* 移动端全局优化 */
@media (max-width: 768px) {
  .home {
    overflow-x: hidden; /* 防止水平滚动 */
  }
  
  .home * {
    -webkit-tap-highlight-color: transparent; /* 移除点击高亮 */
  }
  
  /* 确保所有图片响应式 */
  .home img {
    max-width: 100%;
    height: auto;
  }
  
  /* 优化触摸滚动 */
  .home {
    -webkit-overflow-scrolling: touch;
  }
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* Hero Carousel */
.hero-carousel {
  position: relative;
  width: 100%;
  height: 100%;
}

.hero-carousel-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.hero-carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

.hero-carousel-slide.active {
  opacity: 1;
}

.hero-slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
}

/* Hero Carousel Navigation */
.hero-carousel-nav {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 2rem;
  z-index: 10;
}

.hero-nav-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.hero-nav-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.hero-nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Hero Carousel Indicators */
.hero-carousel-indicators {
  position: absolute;
  bottom: 3rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.75rem;
  z-index: 10;
}

.hero-indicator {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.hero-indicator.active {
  background: white;
  transform: scale(1.2);
}

.hero-indicator:hover {
  background: rgba(255, 255, 255, 0.8);
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2); /* 20%的黑色遮罩 */
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2; /* 确保内容在遮罩层上方 */
}

.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.1), transparent),
  radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.1), transparent),
  radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.1), transparent);
  background-size: 100px 100px;
  animation: float 20s linear infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-100px);
  }
}

/* 新增動畫效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(216, 99, 55, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(216, 99, 55, 0.6), 0 0 30px rgba(216, 99, 55, 0.4);
  }
  100% {
    box-shadow: 0 0 5px rgba(216, 99, 55, 0.3);
  }
}

.hero-container {
  position: relative;
  z-index: 2;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.hero-content {
  max-width: 800px;
  color: white;
  margin-bottom: 4rem;
}

.hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* 移动端优化 */
  @media (max-width: 768px) {
    .hero-badge {
      padding: 0.5rem 1rem;
      font-size: 0.8rem;
      margin-bottom: 1.5rem;
    }
  }

  @media (max-width: 480px) {
    .hero-badge {
      padding: 0.5rem 1rem;
      font-size: 0.75rem;
      margin-bottom: 1rem;
    }
  }

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
}

.hero-title .highlight {
  background: linear-gradient(135deg, #d86337, #f0a04b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 2.5rem;
  font-weight: 300;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.btn-primary, .btn-secondary, .btn-outline {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.btn-primary {
  background: linear-gradient(135deg, #d86337, #c55a30);
  color: white;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 25px 50px rgba(216, 99, 55, 0.4);
  background: linear-gradient(135deg, #c55a30, #b45229);
  animation: glow 2s infinite;
}

.btn-primary:active {
  transform: translateY(-1px);
  transition: transform 0.1s;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: white;
}

.btn-large {
  padding: 1.25rem 2.5rem;
  font-size: 1.1rem;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 2rem;
  max-width: 600px;
}

.stat-item {
  text-align: center;
  color: white;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #d86337, #f0a04b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: pulse 2s infinite;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
  font-weight: 500;
}

/* Container */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Section Styles */
.about-section, .services-section, .leadership-section, .events-section {
  padding: 8rem 0;
}

.about-section {
  /* background: var(--background-white); */
  position: relative;
  overflow: hidden;
}

/* Mission Background Image */
.mission-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.mission-bg-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  opacity: 0.3;
}

@media (max-width: 768px) {
  /* Hero轮播图响应式 */
  .hero-carousel-nav {
    padding: 0 1rem;
  }

  .hero-nav-btn {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
    min-height: 48px; /* 触摸友好 */
    min-width: 48px;
  }

  .hero-carousel-indicators {
    bottom: 2rem;
    padding: 0 1rem;
  }

  .hero-indicator {
    width: 12px;
    height: 12px;
    min-height: 44px; /* 触摸友好的点击区域 */
    min-width: 44px;
    border-radius: 50%;
  }

  /* 轮播图容器优化 */
  .hero-carousel {
    touch-action: pan-y; /* 允许垂直滚动，禁用水平滚动冲突 */
  }

  .hero-carousel-slide {
    background-size: cover;
    background-position: center;
  }
}

@media (max-width: 480px) {
  /* Hero轮播图响应式 */
  .hero-nav-btn {
    width: 45px;
    height: 45px;
    font-size: 1rem;
    min-height: 48px;
    min-width: 48px;
  }

  .hero-carousel-indicators {
    bottom: 1.5rem;
    padding: 0 0.5rem;
  }

  .hero-indicator {
    width: 10px;
    height: 10px;
    min-height: 44px;
    min-width: 44px;
    margin: 0 2px;
  }

  /* 超小屏幕的轮播图优化 */
  .hero-carousel-nav {
    padding: 0 0.5rem;
  }

  .hero-slide-overlay {
    background: linear-gradient(180deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.7) 100%);
  }
}

.about-section .container {
  position: relative;
  z-index: 1;
}

.leadership-section .container {
  position: relative;
  z-index: 1;
}

.services-section {
  background: var(--background-light);
}

.leadership-section {
  background: var(--background-white);
}

.events-section {
  background: var(--background-light);
}

.section-intro {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.section-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* About Section */
.company-profile {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6rem;
  align-items: center;
  margin-bottom: 6rem;
}

.profile-content {
  padding-right: 2rem;
}

.company-name {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 2rem;
  line-height: 1.3;
}

.company-description {
  margin-bottom: 3rem;
}

.company-description p {
  font-size: 1rem;
  line-height: 1.8;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  text-align: justify;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.stat-item {
  position: relative;
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 900;
  color: #2d3748;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-unit {
  font-size: 1.2rem;
  font-weight: 600;
  margin-left: 2px;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
  font-weight: 500;
}

.stat-wave {
  width: 100%;
  height: 20px;
  margin-top: 10px;
}

.wave-svg {
  width: 100%;
  height: 100%;
}

.wave-svg path {
  stroke-dasharray: 200;
  stroke-dashoffset: 200;
  animation: wave-draw 2s ease-in-out forwards;
}

@keyframes wave-draw {
  to {
    stroke-dashoffset: 0;
  }
}

.profile-image {
  position: relative;
}

.profile-image .image-container {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  height: 500px;
}

.profile-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.profile-image:hover img {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  padding: 2rem;
}

.location-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-size: 1rem;
  font-weight: 500;
}

.location-badge i {
  font-size: 1.2rem;
  color: #a0aec0;
}

/* Mission Overview */
.mission-overview {
  margin-top: 4rem;
  padding-top: 4rem;
  border-top: 1px solid var(--border-color);
}

.mission-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.mission-card-simple {
  text-align: center;
  padding: 3rem 2rem;
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transform: perspective(1000px) rotateX(0deg);
  overflow: hidden;
}

.mission-card-simple::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.mission-card-simple:hover::before {
  left: 100%;
}

.mission-card-simple:hover {
  transform: perspective(1000px) rotateX(-5deg) translateY(-15px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  transition: all 0.4s ease;
  position: relative;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.mission-card-simple:hover .card-icon {
  transform: scale(1.1) rotateY(5deg);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.card-icon i {
  font-size: 2rem;
  color: #495057;
  transition: color 0.3s ease;
}

.mission-card-simple:hover .card-icon i {
  color: #2d3748;
}

.mission-card-simple h4 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 1.5rem;
  letter-spacing: -0.01em;
}

.mission-card-simple p {
  font-size: 1rem;
  color: #718096;
  line-height: 1.7;
  font-weight: 400;
}

/* Vision Section - New Modern Design */
.vision-section {
  background: #f8f9fa;
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

.vision-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6rem;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

/* Left Side - Visual Elements */
.vision-visual {
  position: relative;
  height: 600px;
}

.visual-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Background Geometric Shapes */
.geometric-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.shape {
  position: absolute;
  border-radius: 20px;
  opacity: 0.1;
}

.shape-1 {
  width: 120px;
  height: 120px;
  background: #6c5ce7;
  top: 10%;
  left: 20%;
  transform: rotate(15deg);
}

.shape-2 {
  width: 80px;
  height: 80px;
  background: #a29bfe;
  top: 60%;
  right: 30%;
  transform: rotate(-20deg);
}

.shape-3 {
  width: 60px;
  height: 60px;
  background: #fd79a8;
  bottom: 20%;
  left: 10%;
  border-radius: 50%;
}

.shape-4 {
  width: 100px;
  height: 100px;
  background: #fdcb6e;
  top: 30%;
  right: 10%;
  transform: rotate(45deg);
}

/* Feature Labels (Orange Labels) */
.feature-label {
  position: absolute;
  background: #ff6b35;
  color: white;
  border-radius: 20px;
  padding: 1.2rem 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 15px 30px rgba(255, 107, 53, 0.3);
  z-index: 10;
  min-width: 250px;
}

.label-top {
  top: 15%;
  left: 10%;
}

.label-bottom {
  bottom: 15%;
  right: 10%;
}

.label-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.label-icon i {
  font-size: 1.3rem;
  color: white;
}

.label-text {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.label-category {
  font-size: 0.8rem;
  opacity: 0.9;
  font-weight: 500;
}

.label-title {
  font-size: 1.1rem;
  font-weight: 700;
}

/* Central Elements */
.central-elements {
  display: flex;
  gap: 2rem;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.element-item {
  width: 80px;
  height: 80px;
  background: #ffffff;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.element-item:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.element-item i {
  font-size: 2rem;
  color: #495057;
}

/* Connection Lines */
.connection-lines {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  opacity: 0.3;
}

.connection-line {
  stroke: #ff6b35;
  stroke-width: 2;
  stroke-dasharray: 10, 5;
  animation: dash 3s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: -15;
  }
}

/* Right Side - Content */
.vision-content {
  padding-left: 2rem;
}

.content-header .section-title {
  font-size: 3rem;
  font-weight: 800;
  color: #2d3748;
  margin-bottom: 2rem;
  letter-spacing: -0.02em;
}

.content-body {
  max-width: 500px;
}

.vision-text p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #718096;
  margin-bottom: 1.5rem;
  text-align: justify;
}

.vision-text p:last-child {
  margin-bottom: 2.5rem;
}

.content-cta {
  margin-top: 2.5rem;
}

.vision-btn {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  background: #ff6b35;
  color: white;
  padding: 1rem 2.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px rgba(255, 107, 53, 0.3);
}

.vision-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(255, 107, 53, 0.4);
  color: white;
  text-decoration: none;
}

.vision-btn i {
  transition: transform 0.3s ease;
}

.vision-btn:hover i {
  transform: translateX(5px);
}

/* Responsive Design for Vision Section */
@media (max-width: 1024px) {
  .vision-layout {
    grid-template-columns: 1fr;
    gap: 4rem;
    text-align: center;
  }

  .vision-visual {
    height: 400px;
    order: 2;
  }

  .vision-content {
    padding-left: 0;
    order: 1;
  }

  .content-header .section-title {
    font-size: 2.5rem;
  }

  .feature-label {
    min-width: 200px;
    padding: 1rem 1.5rem;
  }

  .label-top {
    top: 10%;
    left: 5%;
  }

  .label-bottom {
    bottom: 10%;
    right: 5%;
  }
}

@media (max-width: 768px) {
  .vision-section {
    padding: 4rem 0;
  }

  .vision-visual {
    height: 300px;
  }

  .content-header .section-title {
    font-size: 2rem;
  }

  .vision-text p {
    font-size: 1rem;
  }

  .feature-label {
    position: relative !important;
    margin: 1rem auto;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
  }

  .central-elements {
    gap: 1rem;
    margin: 2rem 0;
  }

  .element-item {
    width: 60px;
    height: 60px;
  }

  .element-item i {
    font-size: 1.5rem;
  }

  .connection-lines {
    display: none;
  }

  .geometric-bg {
    opacity: 0.5;
  }
}

/* Leadership Messages Sections - 动态领导力消息 */
.leadership-messages-sections {
  width: 100%;
}

.leadership-message-section {
  background: #ffffff;
  padding: 8rem 0;
  position: relative;
  overflow: hidden;
}

.leadership-message-section.alternate-bg {
  background: linear-gradient(135deg, #f7fafc 0%, #e2e8f0 100%);
}

/* Leadership Section - Modern Profile Card Design */
.leadership-section {
  background: #ffffff;
  padding: 8rem 0;
  position: relative;
  overflow: hidden;
}

/* Leadership Background Image */
.leadership-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.leadership-bg-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  opacity: 0.3;
}

.leadership-header {
  text-align: center;
  margin-bottom: 6rem;
  position: relative;
  z-index: 2;
}

.leadership-main-title {
  font-size: 3.5rem;
  font-weight: 900;
  color: #2d3748;
  margin-bottom: 1rem;
  letter-spacing: -0.03em;
}

.leadership-layout {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

/* Modern Profile Card */
.modern-profile-card {
  background: #2d3748;
  display: grid;
  grid-template-columns: 1fr 400px;
  max-width: 1000px;
  min-height: 600px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

/* Left Side - Profile Info */
.profile-info {
  padding: 4rem;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.welcome-text {
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: 1rem;
  opacity: 0.8;
  position: relative;
}

.welcome-text::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 50px;
  height: 3px;
  background: #d86337;
}

.profile-name {
  margin-bottom: 0.5rem;
}

.profile-name h3 {
  font-size: 3rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.1;
  color: #d86337;
}

.profile-title {
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.profile-company {
  font-size: 1rem;
  margin-bottom: 2.5rem;
  opacity: 0.7;
}

.profile-section {
  flex: 1;
}

.profile-section .section-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: white;
}

.message-content {
  line-height: 1.7;
  font-size: 1rem;
  opacity: 0.9;
}

.message-content p {
  margin-bottom: 1.5rem;
  text-align: justify;
}

.message-content p:last-child {
  margin-bottom: 0;
}

/* Right Side - Portrait */
.profile-portrait {
  position: relative;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.portrait-image-container {
  width: 300px;
  height: 350px;
  border-radius: 0;
  overflow: hidden;
  position: relative;
  margin-bottom: 2rem;
}

.portrait-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  filter: contrast(1.1) brightness(1.05);
}

/* Responsive Design for Modern Profile Cards */
@media (max-width: 1200px) {
  .modern-profile-card {
    grid-template-columns: 1fr 350px;
    max-width: 900px;
  }

  .profile-info {
    padding: 3rem;
  }

  .portrait-image-container {
    width: 250px;
    height: 300px;
    border-radius: 0;
  }
}

@media (max-width: 1024px) {
  .leadership-section, .president-section {
    padding: 6rem 0;
  }

  .modern-profile-card {
    grid-template-columns: 1fr;
    max-width: 600px;
    min-height: auto;
  }

  .profile-info {
    padding: 3rem;
    text-align: center;
  }

  .profile-portrait {
    padding: 2rem;
    background: #f8f9fa;
  }

  .portrait-image-container {
    width: 200px;
    height: 250px;
    border-radius: 0;
    margin: 0 auto 0;
  }

  .profile-name h3 {
    font-size: 2.5rem;
  }

  .leadership-main-title, .president-main-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .leadership-section, .president-section {
    padding: 4rem 0;
  }

  .leadership-header, .president-header {
    margin-bottom: 4rem;
  }

  .leadership-main-title, .president-main-title {
    font-size: 2.5rem;
    line-height: 1.1;
  }

  .modern-profile-card {
    max-width: 100%;
    margin: 0 1rem;
  }

  .profile-info {
    padding: 2rem;
  }

  .profile-name h3 {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.3rem;
  }

  .message-content {
    font-size: 0.95rem;
  }

  .portrait-image-container {
    width: 180px;
    height: 220px;
    border-radius: 0;
  }
}

@media (max-width: 480px) {
  .leadership-section, .president-section {
    padding: 3rem 0;
  }

  .leadership-main-title, .president-main-title {
    font-size: 2rem;
  }

  .modern-profile-card {
    margin: 0 0.5rem;
  }

  .profile-info {
    padding: 1.5rem;
  }

  .profile-name h3 {
    font-size: 1.8rem;
  }

  .section-title {
    font-size: 1.2rem;
  }

  .message-content {
    font-size: 0.9rem;
  }

  .portrait-image-container {
    width: 160px;
    height: 200px;
    border-radius: 0;
  }
}

/* President Section */
.president-section {
  padding: 8rem 0;
  background: linear-gradient(135deg, #f7fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.president-header {
  text-align: center;
  margin-bottom: 6rem;
  position: relative;
  z-index: 2;
}

.president-main-title {
  font-size: 3.5rem;
  font-weight: 900;
  color: #2d3748;
  margin-bottom: 1rem;
  letter-spacing: -0.03em;
}

.president-layout {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

/* Events Section - HKELA精彩時刻 */
.events-section {
  padding: 8rem 0;
  background: #f8f9fa;
  position: relative;
}

.events-section .container {
  position: relative;
  z-index: 2;
}

/* 标题区域 */
.events-header {
  text-align: center;
  margin-bottom: 5rem;
}

.events-main-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
}

.events-subtitle {
  font-size: 1.1rem;
  color: #718096;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
  font-weight: 400;
}

/* Activities List */
.activities-list {
  display: flex;
  flex-direction: column;
  gap: 3rem;
  margin-top: 2rem;
  margin-bottom: 4rem;
}

.activity-card {
  width: 100%;
  height: 500px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.activity-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.activity-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.1) 30%,
    rgba(0, 0, 0, 0.7) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 3rem;
}

.activity-content {
  color: white;
  text-align: left;
}

.activity-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1.5rem 0;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.activity-divider {
  width: 80px;
  height: 4px;
  background: #ffffff;
  margin: 1.5rem 0;
  border-radius: 2px;
}

.activity-description {
  font-size: 1.3rem;
  font-weight: 500;
  margin: 0;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.6;
}

/* 查看更多按钮 */
.events-footer {
  text-align: center;
}

.view-more-btn {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #d86337, #c55a30);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(216, 99, 55, 0.3);
}

.view-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(216, 99, 55, 0.4);
  color: white;
  text-decoration: none;
}

.view-more-btn i {
  transition: transform 0.3s ease;
}

.view-more-btn:hover i {
  transform: translateX(3px);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 5rem;
  color: #64748b;
  font-size: 1.3rem;
}

.empty-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.empty-state-content i {
  font-size: 3rem;
  color: #cbd5e0;
  margin-bottom: 1rem;
}

.empty-state-content p {
  font-size: 1.1rem;
  color: #718096;
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .company-profile {
    grid-template-columns: 1fr;
    gap: 4rem;
  }

  .profile-content {
    padding-right: 0;
  }

  .profile-image {
    order: -1;
  }

  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .statistics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .mission-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  /* 修复领导力部分在平板上的显示 */
  .modern-profile-card {
    grid-template-columns: 1fr;
    max-width: 600px;
  }

  .profile-portrait {
    order: -1;
    height: 300px;
  }

  .profile-info {
    padding: 3rem 2rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 80vh;
  }

  .hero-container {
    padding: 0 1rem;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .about-section, .services-section, .leadership-section, .events-section {
    padding: 5rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    margin-bottom: 3rem;
  }

  .btn-primary, .btn-secondary, .btn-outline {
    text-align: center;
    justify-content: center;
    padding: 1rem 1.5rem;
    min-height: 48px; /* 触摸友好的最小高度 */
    font-size: 1rem;
  }

  /* 修复领导力部分在手机上的显示 */
  .modern-profile-card {
    grid-template-columns: 1fr;
    max-width: 100%;
    margin: 0 1rem;
  }

  .profile-portrait {
    height: 250px;
  }

  .profile-info {
    padding: 2rem 1.5rem;
  }

  .profile-name h3 {
    font-size: 2rem;
  }

  .leadership-main-title {
    font-size: 2.5rem;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .activities-list {
    gap: 2rem;
  }

  .activity-card {
    height: 350px;
  }

  .activity-overlay {
    padding: 2rem;
  }

  .activity-title {
    font-size: 1.8rem;
  }

  .activity-description {
    font-size: 1rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .service-card {
    padding: 2rem;
  }

  .events-main-title {
    font-size: 2.2rem;
  }

  .company-profile {
    gap: 3rem;
  }

  .company-name {
    font-size: 1.75rem;
  }

  .statistics-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .profile-image .image-container {
    height: 400px;
  }

  .mission-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .mission-card-simple {
    padding: 1.5rem 1rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .service-card {
    padding: 2rem;
  }

  .vision-goals {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .vision-text-container {
    padding: 2rem;
    gap: 1.5rem;
  }

  .vision-icon-large {
    width: 80px;
    height: 80px;
  }

  .vision-icon-large i {
    font-size: 2rem;
  }

  .vision-text p {
    font-size: 1.1rem;
  }

  .vision-goal-item {
    padding: 2rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-container {
    padding: 0 0.75rem;
  }

  .hero-title {
    font-size: 1.75rem;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1rem;
    line-height: 1.5;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .mv-item, .service-card {
    padding: 1.25rem;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .events-main-title {
    font-size: 1.75rem;
  }

  .events-subtitle {
    font-size: 0.9rem;
  }

  /* 超小屏幕的领导力部分优化 */
  .modern-profile-card {
    margin: 0 0.5rem;
  }

  .profile-info {
    padding: 1.5rem 1rem;
  }

  .profile-name h3 {
    font-size: 1.5rem;
  }

  .leadership-main-title {
    font-size: 2rem;
  }

  .profile-title {
    font-size: 1rem;
  }

  .profile-company {
    font-size: 0.9rem;
  }

  .message-content {
    font-size: 0.9rem;
  }

  .activities-list {
    gap: 1.5rem;
  }

  .activity-card {
    height: 300px;
  }

  .activity-overlay {
    padding: 1.5rem;
  }

  .activity-title {
    font-size: 1.5rem;
  }

  .activity-description {
    font-size: 0.9rem;
  }

  .activity-divider {
    width: 60px;
    height: 3px;
    margin: 1rem 0;
  }

  .view-more-btn {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }

  .events-header {
    margin-bottom: 3rem;
  }
}

.services-grid {
  grid-template-columns: 1fr;
}

.vision-goals {
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.vision-text-container {
  flex-direction: column;
  gap: 2rem;
  text-align: center;
}

.vision-icon-large {
  margin: 0 auto;
}
</style>
