-- 详细会员申请表
DROP TABLE IF EXISTS `membership_applications_detailed`;
CREATE TABLE `membership_applications_detailed` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `membership_type` varchar(50) NOT NULL COMMENT '会员类型',
  
  -- 个人资料 (Personal Particulars)
  `title` varchar(10) DEFAULT NULL COMMENT '称谓 (Dr./Mr./Mrs./Ms.)',
  `surname_en` varchar(100) DEFAULT NULL COMMENT '英文姓氏',
  `other_name_en` varchar(100) DEFAULT NULL COMMENT '英文名字',
  `name_zh` varchar(100) DEFAULT NULL COMMENT '中文姓名',
  `passport_id` varchar(50) DEFAULT NULL COMMENT '护照/身分证号码',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  
  -- 联系方式 (Contact Information)
  `correspondence_address` text DEFAULT NULL COMMENT '通讯地址',
  `email` varchar(100) NOT NULL COMMENT '电子邮箱',
  `mobile` varchar(20) NOT NULL COMMENT '手提电话号码',
  `workplace_phone` varchar(20) DEFAULT NULL COMMENT '办公室电话号码',
  `residence_phone` varchar(20) DEFAULT NULL COMMENT '家居电话号码',
  
  -- 学历信息 (Academic Qualifications)
  `education_1_school` varchar(200) DEFAULT NULL COMMENT '学历1-院校',
  `education_1_award_date` date DEFAULT NULL COMMENT '学历1-授予日期',
  `education_1_qualification` varchar(200) DEFAULT NULL COMMENT '学历1-资历',
  `education_2_school` varchar(200) DEFAULT NULL COMMENT '学历2-院校',
  `education_2_award_date` date DEFAULT NULL COMMENT '学历2-授予日期',
  `education_2_qualification` varchar(200) DEFAULT NULL COMMENT '学历2-资历',
  
  -- 专业资历 (Professional Qualifications)
  `professional_1_qualification` varchar(200) DEFAULT NULL COMMENT '专业资历1-资格名称',
  `professional_1_institution` varchar(200) DEFAULT NULL COMMENT '专业资历1-颁授机构',
  `professional_1_award_date` date DEFAULT NULL COMMENT '专业资历1-授予日期',
  `professional_2_qualification` varchar(200) DEFAULT NULL COMMENT '专业资历2-资格名称',
  `professional_2_institution` varchar(200) DEFAULT NULL COMMENT '专业资历2-颁授机构',
  `professional_2_award_date` date DEFAULT NULL COMMENT '专业资历2-授予日期',
  
  -- 工作经验 (Employment Record)
  `employment_1_company` varchar(200) DEFAULT NULL COMMENT '工作经验1-公司',
  `employment_1_from_date` date DEFAULT NULL COMMENT '工作经验1-开始日期',
  `employment_1_to_date` date DEFAULT NULL COMMENT '工作经验1-结束日期',
  `employment_1_is_current` tinyint(1) DEFAULT 0 COMMENT '工作经验1-是否在职',
  `employment_1_job_title` varchar(100) DEFAULT NULL COMMENT '工作经验1-职位',
  `employment_1_main_duties` text DEFAULT NULL COMMENT '工作经验1-主要职责',
  `employment_2_company` varchar(200) DEFAULT NULL COMMENT '工作经验2-公司',
  `employment_2_from_date` date DEFAULT NULL COMMENT '工作经验2-开始日期',
  `employment_2_to_date` date DEFAULT NULL COMMENT '工作经验2-结束日期',
  `employment_2_is_current` tinyint(1) DEFAULT 0 COMMENT '工作经验2-是否在职',
  `employment_2_job_title` varchar(100) DEFAULT NULL COMMENT '工作经验2-职位',
  `employment_2_main_duties` text DEFAULT NULL COMMENT '工作经验2-主要职责',
  
  -- 申请者须知确认
  `document_requirements_agreed` tinyint(1) DEFAULT 0 COMMENT '文件提交要求确认',
  `privacy_policy_agreed` tinyint(1) DEFAULT 0 COMMENT '保密条款确认',
  
  -- 声明确认
  `declaration_agreed` tinyint(1) DEFAULT 0 COMMENT '声明确认',
  `rules_agreement` tinyint(1) DEFAULT 0 COMMENT '章程遵守确认',
  
  -- 申请状态和处理信息
  `current_step` int(2) DEFAULT 1 COMMENT '当前步骤 (1-6)',
  `is_completed` tinyint(1) DEFAULT 0 COMMENT '是否完成申请',
  `status` char(1) DEFAULT '0' COMMENT '处理状态（0待处理 1已处理 2已拒绝）',
  `process_notes` text DEFAULT NULL COMMENT '处理备注',
  `processed_by` varchar(64) DEFAULT NULL COMMENT '处理人',
  `processed_time` datetime DEFAULT NULL COMMENT '处理时间',
  
  -- 系统字段
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='详细会员申请表';

-- 插入测试数据
INSERT INTO `membership_applications_detailed` (
  `membership_type`, `title`, `surname_en`, `other_name_en`, `name_zh`, `passport_id`, `birth_date`,
  `correspondence_address`, `email`, `mobile`, `workplace_phone`,
  `education_1_school`, `education_1_award_date`, `education_1_qualification`,
  `employment_1_company`, `employment_1_from_date`, `employment_1_to_date`, `employment_1_is_current`, `employment_1_job_title`, `employment_1_main_duties`,
  `document_requirements_agreed`, `privacy_policy_agreed`, `declaration_agreed`, `rules_agreement`,
  `current_step`, `is_completed`, `status`
) VALUES (
  'individual', 'Mr.', 'Chan', 'Chi Keung', '陈志强', 'A123456789', '1985-03-15',
  '香港湾仔轩尼诗道123号', '<EMAIL>', '+852 9876 5432', '+852 2345 6789',
  'The University of Hong Kong', '2008-07-01', 'Bachelor of Business Administration',
  'DHL Supply Chain (Hong Kong) Limited', '2018-06-01', NULL, 1, 'Logistics Manager', '管理跨境电商物流团队；优化仓储配送流程；协调供应商与客户需求',
  1, 1, 1, 1,
  6, 1, '0'
);