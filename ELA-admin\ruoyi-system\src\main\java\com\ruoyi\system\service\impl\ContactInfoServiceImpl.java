package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ContactInfoMapper;
import com.ruoyi.system.domain.ContactInfo;
import com.ruoyi.system.service.IContactInfoService;

/**
 * 联系信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ContactInfoServiceImpl implements IContactInfoService 
{
    @Autowired
    private ContactInfoMapper contactInfoMapper;

    /**
     * 查询联系信息
     * 
     * @param id 联系信息主键
     * @return 联系信息
     */
    @Override
    public ContactInfo selectContactInfoById(Long id)
    {
        return contactInfoMapper.selectContactInfoById(id);
    }

    /**
     * 查询联系信息列表
     * 
     * @param contactInfo 联系信息
     * @return 联系信息
     */
    @Override
    public List<ContactInfo> selectContactInfoList(ContactInfo contactInfo)
    {
        return contactInfoMapper.selectContactInfoList(contactInfo);
    }

    /**
     * 新增联系信息
     * 
     * @param contactInfo 联系信息
     * @return 结果
     */
    @Override
    public int insertContactInfo(ContactInfo contactInfo)
    {
        return contactInfoMapper.insertContactInfo(contactInfo);
    }

    /**
     * 修改联系信息
     * 
     * @param contactInfo 联系信息
     * @return 结果
     */
    @Override
    public int updateContactInfo(ContactInfo contactInfo)
    {
        return contactInfoMapper.updateContactInfo(contactInfo);
    }

    /**
     * 批量删除联系信息
     * 
     * @param ids 需要删除的联系信息主键
     * @return 结果
     */
    @Override
    public int deleteContactInfoByIds(Long[] ids)
    {
        return contactInfoMapper.deleteContactInfoByIds(ids);
    }

    /**
     * 删除联系信息信息
     * 
     * @param id 联系信息主键
     * @return 结果
     */
    @Override
    public int deleteContactInfoById(Long id)
    {
        return contactInfoMapper.deleteContactInfoById(id);
    }
} 