import request from '@/utils/request'

// 查询会员类型列表
export function listMembershipTypes(query) {
  return request({
    url: '/website/membership-types/list',
    method: 'get',
    params: query
  })
}

// 查询会员类型详细
export function getMembershipType(id) {
  return request({
    url: '/website/membership-types/' + id,
    method: 'get'
  })
}

// 新增会员类型
export function addMembershipType(data) {
  return request({
    url: '/website/membership-types',
    method: 'post',
    data: data
  })
}

// 修改会员类型
export function updateMembershipType(data) {
  return request({
    url: '/website/membership-types',
    method: 'put',
    data: data
  })
}

// 删除会员类型
export function delMembershipType(id) {
  return request({
    url: '/website/membership-types/' + id,
    method: 'delete'
  })
}

// 获取启用的会员类型列表(供前端使用)
export function getPublicMembershipTypes() {
  return request({
    url: '/website/membership-types/public',
    method: 'get'
  })
} 