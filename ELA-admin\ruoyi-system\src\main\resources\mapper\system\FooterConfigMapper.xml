<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.FooterConfigMapper">
    
    <resultMap type="FooterConfig" id="FooterConfigResult">
        <result property="id"    column="id"    />
        <result property="sectionType"    column="section_type"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="backgroundColor"    column="background_color"    />
        <result property="textColor"    column="text_color"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectFooterConfigVo">
        select id, section_type, title, content, background_color, text_color, sort_order, status, create_time, update_time, create_by, update_by, remark from footer_config
    </sql>

    <select id="selectFooterConfigList" parameterType="FooterConfig" resultMap="FooterConfigResult">
        <include refid="selectFooterConfigVo"/>
        <where>  
            <if test="sectionType != null  and sectionType != ''"> and section_type = #{sectionType}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectFooterConfigById" parameterType="Long" resultMap="FooterConfigResult">
        <include refid="selectFooterConfigVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertFooterConfig" parameterType="FooterConfig" useGeneratedKeys="true" keyProperty="id">
        insert into footer_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sectionType != null and sectionType != ''">section_type,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="backgroundColor != null">background_color,</if>
            <if test="textColor != null">text_color,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sectionType != null and sectionType != ''">#{sectionType},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="backgroundColor != null">#{backgroundColor},</if>
            <if test="textColor != null">#{textColor},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateFooterConfig" parameterType="FooterConfig">
        update footer_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="sectionType != null and sectionType != ''">section_type = #{sectionType},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="backgroundColor != null">background_color = #{backgroundColor},</if>
            <if test="textColor != null">text_color = #{textColor},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFooterConfigById" parameterType="Long">
        delete from footer_config where id = #{id}
    </delete>

    <delete id="deleteFooterConfigByIds" parameterType="String">
        delete from footer_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 