import request from '@/utils/request'

// 查询申请流程列表
export function listApplicationProcess(query) {
  return request({
    url: '/website/application-process/list',
    method: 'get',
    params: query
  })
}

// 查询申请流程详细
export function getApplicationProcess(id) {
  return request({
    url: '/website/application-process/' + id,
    method: 'get'
  })
}

// 新增申请流程
export function addApplicationProcess(data) {
  return request({
    url: '/website/application-process',
    method: 'post',
    data: data
  })
}

// 修改申请流程
export function updateApplicationProcess(data) {
  return request({
    url: '/website/application-process',
    method: 'put',
    data: data
  })
}

// 删除申请流程
export function delApplicationProcess(id) {
  return request({
    url: '/website/application-process/' + id,
    method: 'delete'
  })
}

// 导出申请流程
export function exportApplicationProcess(query) {
  return request({
    url: '/website/application-process/export',
    method: 'post',
    data: query
  })
}

// 获取启用的申请流程列表(供前端使用)
export function getPublicApplicationProcess() {
  return request({
    url: '/website/application-process/public',
    method: 'get'
  })
} 