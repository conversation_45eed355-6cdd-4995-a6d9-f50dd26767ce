<template>
  <section class="timeline-section section">
    <div class="container">
      <div class="section-title">
        <h2>{{ section.config.title }}</h2>
        <p v-if="section.config.subtitle">{{ section.config.subtitle }}</p>
      </div>
      
      <div class="timeline-container">
        <div 
          v-for="(event, index) in section.config.events" 
          :key="index" 
          class="timeline-item"
        >
          <div class="timeline-marker"></div>
          <div class="timeline-content" :class="{ 'right': index % 2 !== 0 }">
            <div class="timeline-year">{{ event.year }}</div>
            <h3 class="timeline-title">{{ event.title }}</h3>
            <p class="timeline-description">{{ event.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'Timeline',
  props: {
    section: {
      type: Object,
      required: true
    }
  }
}
</script>

<style scoped>
.timeline-section {
  /* Uses global section padding */
}

.timeline-container {
  position: relative;
  max-width: 900px;
  margin: 0 auto;
}

.timeline-container::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 10px;
  bottom: 10px;
  width: 3px;
  background: linear-gradient(to bottom, transparent, var(--primary-color), transparent);
  transform: translateX(-50%);
  border-radius: 3px;
  box-shadow: 0 0 10px var(--primary-color);
}

.timeline-item {
  position: relative;
  margin-bottom: 50px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

/* 时间线标记 */
.timeline-marker {
  position: absolute;
  top: 10px;
  left: 50%;
  width: 18px;
  height: 18px;
  background: var(--bg-dark-secondary);
  border: 3px solid var(--primary-color);
  border-radius: 50%;
  transform: translateX(-50%);
  z-index: 2;
  box-shadow: 0 0 15px var(--primary-color);
}

/* 时间线内容 */
.timeline-content {
  position: relative;
  width: calc(50% - 40px);
  background: rgba(18, 18, 36, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 24px;
  border-radius: var(--radius-lg);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease-out;
}

.timeline-content:hover {
  transform: translateY(-5px);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.timeline-content.right {
  margin-left: calc(50% + 40px);
}
.timeline-item:not(:has(.right)) .timeline-content {
  margin-left: 0;
}

/* 内容箭头 */
.timeline-content::before {
  content: '';
  position: absolute;
  top: 12px;
  width: 0;
  height: 0;
  border: 10px solid transparent;
}

.timeline-content:not(.right)::before {
  right: -20px;
  border-left-color: rgba(255, 255, 255, 0.1);
}

.timeline-content.right::before {
  left: -20px;
  border-right-color: rgba(255, 255, 255, 0.1);
}

.timeline-content:hover::before {
    border-left-color: var(--primary-color);
    border-right-color: var(--primary-color);
}
.timeline-content.right:hover::before {
    border-left-color: transparent;
}
.timeline-content:not(.right):hover::before {
    border-right-color: transparent;
}

.timeline-year {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.timeline-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-heading);
  margin-bottom: 12px;
}

.timeline-description {
  color: var(--text-dark);
  line-height: 1.7;
  font-size: 15px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline-container::before {
    left: 9px;
    transform: translateX(0);
  }

  .timeline-marker {
    left: 0;
    transform: translateX(0);
  }

  .timeline-content,
  .timeline-content.right {
    width: calc(100% - 50px);
    margin-left: 50px;
  }
  
  .timeline-content.right {
    margin-left: 50px;
  }
  
  .timeline-item:not(:has(.right)) .timeline-content {
    margin-left: 50px;
  }
  
  .timeline-content::before,
  .timeline-content.right::before {
    left: -20px;
    border-right-color: rgba(255, 255, 255, 0.1);
    border-left-color: transparent;
  }
  .timeline-content:hover::before,
  .timeline-content.right:hover::before {
      border-right-color: var(--primary-color);
      border-left-color: transparent;
  }

}
</style> 