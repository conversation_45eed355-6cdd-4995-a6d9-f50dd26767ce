import request from '@/utils/request'

// 查询联系信息列表
export function listContactInfo(query) {
  return request({
    url: '/website/contactInfo/list',
    method: 'get',
    params: query
  })
}

// 查询联系信息详细
export function getContactInfo(id) {
  return request({
    url: '/website/contactInfo/' + id,
    method: 'get'
  })
}

// 新增联系信息
export function addContactInfo(data) {
  return request({
    url: '/website/contactInfo',
    method: 'post',
    data: data
  })
}

// 修改联系信息
export function updateContactInfo(data) {
  return request({
    url: '/website/contactInfo',
    method: 'put',
    data: data
  })
}

// 删除联系信息
export function delContactInfo(id) {
  return request({
    url: '/website/contactInfo/' + id,
    method: 'delete'
  })
} 