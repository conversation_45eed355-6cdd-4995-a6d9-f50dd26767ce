-- 首页Hero内容管理菜单 SQL
-- 注意：此SQL需要在网站管理和首页管理菜单已存在的情况下执行

-- 获取首页管理菜单ID（假设已存在）
SELECT @homeMenuId := menu_id FROM sys_menu WHERE menu_name = '首页管理' AND parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '网站管理') LIMIT 1;

-- 如果首页管理菜单不存在，则创建
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '首页管理', @websiteMenuId, '1', 'home', '', 1, 0, 'M', '0', '0', '', 'home', 'admin', sysdate(), '', null, '首页管理菜单'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE menu_name = '首页管理' AND parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '网站管理'));

-- 重新获取首页管理菜单ID
SELECT @homeMenuId := menu_id FROM sys_menu WHERE menu_name = '首页管理' AND parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '网站管理') LIMIT 1;

-- 添加首页Hero内容管理菜单（首页管理的子菜单）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('首页Hero内容', @homeMenuId, '2', 'hero-content', 'website/heroContent/index', 1, 0, 'C', '0', '0', 'website:hero-content:list', 'edit', 'admin', sysdate(), '', null, '首页Hero内容菜单');

-- 获取首页Hero内容菜单ID
SELECT @heroContentMenuId := LAST_INSERT_ID();

-- 添加首页Hero内容管理的按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('首页Hero内容查询', @heroContentMenuId, '1', '#', '', 1, 0, 'F', '0', '0', 'website:hero-content:query', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('首页Hero内容新增', @heroContentMenuId, '2', '#', '', 1, 0, 'F', '0', '0', 'website:hero-content:add', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('首页Hero内容修改', @heroContentMenuId, '3', '#', '', 1, 0, 'F', '0', '0', 'website:hero-content:edit', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('首页Hero内容删除', @heroContentMenuId, '4', '#', '', 1, 0, 'F', '0', '0', 'website:hero-content:remove', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('首页Hero内容导出', @heroContentMenuId, '5', '#', '', 1, 0, 'F', '0', '0', 'website:hero-content:export', '#', 'admin', sysdate(), '', null, '');

-- 说明：
-- 1. 此SQL会创建菜单结构：网站管理 > 首页管理 > 首页Hero内容
-- 2. 包含了所有必要的按钮权限：查询、新增、修改、删除、导出
-- 3. 权限标识符为：website:hero-content:*
-- 4. 组件路径为：website/heroContent/index
-- 5. 执行此SQL后，管理员用户就可以在后台看到并访问首页Hero内容管理页面 