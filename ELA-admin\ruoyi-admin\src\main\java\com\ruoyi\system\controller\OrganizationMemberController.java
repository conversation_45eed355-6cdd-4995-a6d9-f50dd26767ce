package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.OrganizationMember;
import com.ruoyi.system.service.IOrganizationMemberService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 组织架构成员Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/system/organization/member")
public class OrganizationMemberController extends BaseController
{
    @Autowired
    private IOrganizationMemberService organizationMemberService;

    /**
     * 查询组织架构成员列表
     */
    @PreAuthorize("@ss.hasPermi('website:organization:member:list')")
    @GetMapping("/list")
    public TableDataInfo list(OrganizationMember organizationMember)
    {
        startPage();
        List<OrganizationMember> list = organizationMemberService.selectOrganizationMemberList(organizationMember);
        return getDataTable(list);
    }

    /**
     * 导出组织架构成员列表
     */
    @PreAuthorize("@ss.hasPermi('website:organization:member:export')")
    @Log(title = "组织架构成员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrganizationMember organizationMember)
    {
        List<OrganizationMember> list = organizationMemberService.selectOrganizationMemberList(organizationMember);
        ExcelUtil<OrganizationMember> util = new ExcelUtil<OrganizationMember>(OrganizationMember.class);
        util.exportExcel(response, list, "组织架构成员数据");
    }

    /**
     * 获取组织架构成员详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:organization:member:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(organizationMemberService.selectOrganizationMemberById(id));
    }

    /**
     * 新增组织架构成员
     */
    @PreAuthorize("@ss.hasPermi('website:organization:member:add')")
    @Log(title = "组织架构成员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OrganizationMember organizationMember)
    {
        return toAjax(organizationMemberService.insertOrganizationMember(organizationMember));
    }

    /**
     * 修改组织架构成员
     */
    @PreAuthorize("@ss.hasPermi('website:organization:member:edit')")
    @Log(title = "组织架构成员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrganizationMember organizationMember)
    {
        return toAjax(organizationMemberService.updateOrganizationMember(organizationMember));
    }

    /**
     * 删除组织架构成员
     */
    @PreAuthorize("@ss.hasPermi('website:organization:member:remove')")
    @Log(title = "组织架构成员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(organizationMemberService.deleteOrganizationMemberByIds(ids));
    }
} 