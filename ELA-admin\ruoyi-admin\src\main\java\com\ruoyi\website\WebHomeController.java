package com.ruoyi.website;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.HomeBanner;
import com.ruoyi.system.domain.HeroContent;
import com.ruoyi.system.domain.AboutUs;
import com.ruoyi.system.domain.LeadershipMessage;
import com.ruoyi.system.service.IHomeBannerService;
import com.ruoyi.system.service.IHeroContentService;
import com.ruoyi.system.service.IAboutUsService;
import com.ruoyi.system.service.ILeadershipMessageService;

/**
 * 官网首页Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/web/home")
@CrossOrigin(origins = "*", maxAge = 3600)
public class WebHomeController extends BaseController {

    @Autowired
    private IHomeBannerService homeBannerService;

    @Autowired
    private IHeroContentService heroContentService;

    @Autowired
    private IAboutUsService aboutUsService;

    @Autowired
    private ILeadershipMessageService leadershipMessageService;

    /**
     * 获取首页Banner列表（官网专用）
     * 
     * @return Banner列表数据
     */
    @GetMapping("/banners")
    public AjaxResult getBanners()
    {
        try {
            List<HomeBanner> list = homeBannerService.selectEnabledHomeBannerList();
            return AjaxResult.success(list);
        } catch (Exception e) {
            return AjaxResult.error("获取Banner数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取首页Hero内容（官网专用）
     * 
     * @return Hero内容数据
     */
    @GetMapping("/hero")
    public AjaxResult getHeroContent()
    {
        try {
            HeroContent heroContent = heroContentService.selectEnabledHeroContent();
            return AjaxResult.success(heroContent);
        } catch (Exception e) {
            return AjaxResult.error("获取Hero内容失败：" + e.getMessage());
        }
    }

    /**
     * 获取关于我们内容（官网专用）
     * 
     * @return 关于我们数据
     */
    @GetMapping("/about")
    public AjaxResult getAboutUs()
    {
        try {
            AboutUs aboutUs = aboutUsService.selectAboutUsActive();
            return AjaxResult.success(aboutUs);
        } catch (Exception e) {
            return AjaxResult.error("获取关于我们内容失败：" + e.getMessage());
        }
    }

    /**
     * 获取首页所有数据（官网专用）
     * 
     * @return 首页数据
     */
    @GetMapping("/data")
    public AjaxResult getHomeData()
    {
        try {
            // 获取Banner数据
            List<HomeBanner> banners = homeBannerService.selectEnabledHomeBannerList();
            
            // 获取Hero内容数据
            HeroContent heroContent = heroContentService.selectEnabledHeroContent();
            
            // 获取关于我们数据
            AboutUs aboutUs = aboutUsService.selectAboutUsActive();
            
            // 获取领导力消息数据
            List<LeadershipMessage> leadershipMessages = leadershipMessageService.selectEnabledLeadershipMessageList();
            
            // 构建返回数据
            AjaxResult result = AjaxResult.success();
            result.put("banners", banners);
            result.put("heroContent", heroContent);
            result.put("aboutUs", aboutUs);
            result.put("leadershipMessages", leadershipMessages);
            
            return result;
        } catch (Exception e) {
            return AjaxResult.error("获取首页数据失败：" + e.getMessage());
        }
    }
}
