<template>
  <section class="grid-cards section" :style="section.config.style">
    <div class="container">
      <div class="section-title" v-if="section.config.title">
        <h2>{{ section.config.title }}</h2>
        <p v-if="section.config.subtitle">{{ section.config.subtitle }}</p>
      </div>
      
      <div 
        class="cards-grid" 
        :class="[`layout-${section.config.layout}`, `style-${section.config.cardStyle}`]"
        :style="{ '--columns': section.config.columns || 4 }"
      >
        <div 
          v-for="item in section.config.items" 
          :key="item.id"
          class="card-item"
          :style="{ '--item-color': item.color }"
        >
          <div class="card-icon" v-if="item.icon">{{ item.icon }}</div>
          <div class="card-content">
            <h3 class="card-title">{{ item.title }}</h3>
            <p class="card-description">{{ item.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'GridCards',
  props: {
    section: {
      type: Object,
      required: true
    }
  }
}
</script>

<style scoped>
.grid-cards {
  padding: 80px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.5rem;
}

.section-title p {
  font-size: 1.125rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
}

/* 网格布局 */
.cards-grid {
  display: grid;
  gap: 2rem;
}

.layout-grid {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.layout-masonry {
  grid-template-columns: repeat(var(--columns), 1fr);
  grid-auto-rows: min-content;
}

.layout-carousel {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  padding-bottom: 1rem;
}

/* 卡片样式 */
.card-item {
  background: white;
  border-radius: var(--radius-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.style-flat .card-item {
  border: 1px solid var(--gray-200);
  padding: 2rem;
}

.style-elevated .card-item {
  box-shadow: var(--shadow);
  padding: 2.5rem 2rem;
  border: 1px solid var(--gray-200);
}

.style-elevated .card-item:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.style-outlined .card-item {
  border: 2px solid var(--gray-200);
  padding: 2rem;
}

.style-outlined .card-item:hover {
  border-color: var(--item-color, var(--primary-color));
}

/* 卡片内容 */
.card-item {
  text-align: center;
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  line-height: 1;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 1rem;
}

.card-description {
  color: var(--gray-600);
  line-height: 1.6;
  margin: 0;
}

/* 顶部装饰条 */
.style-elevated .card-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--item-color, var(--primary-color));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.style-elevated .card-item:hover::before {
  transform: scaleX(1);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .layout-masonry {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .grid-cards {
    padding: 60px 0;
  }
  
  .section-title h2 {
    font-size: 2rem;
  }
  
  .cards-grid {
    gap: 1.5rem;
  }
  
  .layout-grid,
  .layout-masonry,
  .layout-carousel {
    grid-template-columns: 1fr;
  }
  
  .card-item {
    padding: 1.5rem !important;
  }
}

@media (max-width: 480px) {
  .section-title h2 {
    font-size: 1.75rem;
  }
  
  .card-icon {
    font-size: 2.5rem;
  }
  
  .card-title {
    font-size: 1.125rem;
  }
}

/* 自定义滚动条（用于carousel布局） */
.layout-carousel::-webkit-scrollbar {
  height: 8px;
}

.layout-carousel::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 4px;
}

.layout-carousel::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

.layout-carousel::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}
</style> 