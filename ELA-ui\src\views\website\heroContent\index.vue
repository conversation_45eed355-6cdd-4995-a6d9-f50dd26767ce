<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="徽章中文" prop="badgeZh">
        <el-input
          v-model="queryParams.badgeZh"
          placeholder="请输入徽章文字中文"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="内容状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['website:hero-content:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['website:hero-content:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['website:hero-content:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="heroContentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键ID" align="center" prop="id" />
      <el-table-column label="徽章中文" align="center" prop="badgeZh" />
      <el-table-column label="徽章英文" align="center" prop="badgeEn" />
      <el-table-column label="标题中文" align="center" prop="titleZh" :show-overflow-tooltip="true" />
      <el-table-column label="标题英文" align="center" prop="titleEn" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['website:hero-content:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['website:hero-content:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改首页Hero内容对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="徽章文字中文" prop="badgeZh">
              <el-input v-model="form.badgeZh" placeholder="请输入徽章文字中文" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="徽章文字英文" prop="badgeEn">
              <el-input v-model="form.badgeEn" placeholder="请输入徽章文字英文" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="标题中文" prop="titleZh">
              <el-input v-model="form.titleZh" type="textarea" placeholder="请输入标题中文" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标题英文" prop="titleEn">
              <el-input v-model="form.titleEn" type="textarea" placeholder="请输入标题英文" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="副标题中文" prop="subtitleZh">
              <el-input v-model="form.subtitleZh" type="textarea" placeholder="请输入副标题中文" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="副标题英文" prop="subtitleEn">
              <el-input v-model="form.subtitleEn" type="textarea" placeholder="请输入副标题英文" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="按钮配置" prop="buttonsConfig">
          <el-input v-model="form.buttonsConfig" type="textarea" placeholder="请输入按钮配置JSON" />
        </el-form-item>
        <el-form-item label="统计数据配置" prop="statsConfig">
          <el-input v-model="form.statsConfig" type="textarea" placeholder="请输入统计数据配置JSON" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="form.sort" controls-position="right" placeholder="请输入排序" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listHeroContent, getHeroContent, delHeroContent, addHeroContent, updateHeroContent } from "@/api/website/heroContent";

export default {
  name: "HeroContent",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 首页Hero内容表格数据
      heroContentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        badgeZh: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        badgeZh: [
          { required: true, message: "徽章文字中文不能为空", trigger: "blur" }
        ],
        badgeEn: [
          { required: true, message: "徽章文字英文不能为空", trigger: "blur" }
        ],
        titleZh: [
          { required: true, message: "标题中文不能为空", trigger: "blur" }
        ],
        titleEn: [
          { required: true, message: "标题英文不能为空", trigger: "blur" }
        ],
        subtitleZh: [
          { required: true, message: "副标题中文不能为空", trigger: "blur" }
        ],
        subtitleEn: [
          { required: true, message: "副标题英文不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询首页Hero内容列表 */
    getList() {
      this.loading = true;
      listHeroContent(this.queryParams).then(response => {
        this.heroContentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        badgeZh: null,
        badgeEn: null,
        titleZh: null,
        titleEn: null,
        subtitleZh: null,
        subtitleEn: null,
        buttonsConfig: null,
        statsConfig: null,
        status: "0",
        sort: 0,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加首页Hero内容";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getHeroContent(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改首页Hero内容";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateHeroContent(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHeroContent(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除首页Hero内容编号为"' + ids + '"的数据项？').then(function() {
        return delHeroContent(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script> 