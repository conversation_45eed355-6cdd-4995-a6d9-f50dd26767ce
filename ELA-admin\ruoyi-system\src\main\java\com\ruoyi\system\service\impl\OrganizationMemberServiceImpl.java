package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.OrganizationMemberMapper;
import com.ruoyi.system.domain.OrganizationMember;
import com.ruoyi.system.service.IOrganizationMemberService;

/**
 * 组织架构成员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class OrganizationMemberServiceImpl implements IOrganizationMemberService 
{
    @Autowired
    private OrganizationMemberMapper organizationMemberMapper;

    /**
     * 查询组织架构成员
     * 
     * @param id 组织架构成员主键
     * @return 组织架构成员
     */
    @Override
    public OrganizationMember selectOrganizationMemberById(Long id)
    {
        return organizationMemberMapper.selectOrganizationMemberById(id);
    }

    /**
     * 查询组织架构成员列表
     * 
     * @param organizationMember 组织架构成员
     * @return 组织架构成员
     */
    @Override
    public List<OrganizationMember> selectOrganizationMemberList(OrganizationMember organizationMember)
    {
        return organizationMemberMapper.selectOrganizationMemberList(organizationMember);
    }

    /**
     * 新增组织架构成员
     * 
     * @param organizationMember 组织架构成员
     * @return 结果
     */
    @Override
    public int insertOrganizationMember(OrganizationMember organizationMember)
    {
        organizationMember.setCreateTime(DateUtils.getNowDate());
        return organizationMemberMapper.insertOrganizationMember(organizationMember);
    }

    /**
     * 修改组织架构成员
     * 
     * @param organizationMember 组织架构成员
     * @return 结果
     */
    @Override
    public int updateOrganizationMember(OrganizationMember organizationMember)
    {
        organizationMember.setUpdateTime(DateUtils.getNowDate());
        return organizationMemberMapper.updateOrganizationMember(organizationMember);
    }

    /**
     * 批量删除组织架构成员
     * 
     * @param ids 需要删除的组织架构成员主键
     * @return 结果
     */
    @Override
    public int deleteOrganizationMemberByIds(Long[] ids)
    {
        return organizationMemberMapper.deleteOrganizationMemberByIds(ids);
    }

    /**
     * 删除组织架构成员信息
     * 
     * @param id 组织架构成员主键
     * @return 结果
     */
    @Override
    public int deleteOrganizationMemberById(Long id)
    {
        return organizationMemberMapper.deleteOrganizationMemberById(id);
    }
} 