package com.ruoyi.system.domain;

import java.util.Date;
import java.util.List;
import java.util.Arrays;
import java.util.ArrayList;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonSetter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 资讯管理对象 publications
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
public class Publication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 资讯ID */
    private Long id;

    /** 资讯标题 */
    @Excel(name = "资讯标题")
    private String title;

    /** 封面图URL */
    @Excel(name = "封面图URL")
    private String coverImageUrl;

    /** 内容图片URL */
    @Excel(name = "内容图片URL")
    private String contentImageUrl;

    /** 资讯内容(富文本) */
    @Excel(name = "资讯内容")
    private String content;

    /** 内容摘要 */
    @Excel(name = "内容摘要")
    private String summary;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /** 排序号 */
    @Excel(name = "排序号")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    
    public void setCoverImageUrl(String coverImageUrl) 
    {
        this.coverImageUrl = coverImageUrl;
    }

    public String getCoverImageUrl() 
    {
        return coverImageUrl;
    }
    
    public void setContentImageUrl(String contentImageUrl) 
    {
        this.contentImageUrl = contentImageUrl;
    }

    public String getContentImageUrl() 
    {
        return contentImageUrl;
    }
    
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    
    public void setSummary(String summary) 
    {
        this.summary = summary;
    }

    public String getSummary() 
    {
        return summary;
    }
    
    public void setPublishTime(Date publishTime) 
    {
        this.publishTime = publishTime;
    }

    public Date getPublishTime() 
    {
        return publishTime;
    }
    
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("coverImageUrl", getCoverImageUrl())
            .append("contentImageUrl", getContentImageUrl())
            .append("content", getContent())
            .append("summary", getSummary())
            .append("publishTime", getPublishTime())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}