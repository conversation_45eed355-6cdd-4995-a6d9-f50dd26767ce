package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.WebsiteBanner;

/**
 * 网站BannerService接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IWebsiteBannerService 
{
    /**
     * 查询网站Banner
     * 
     * @param id 网站Banner主键
     * @return 网站Banner
     */
    public WebsiteBanner selectWebsiteBannerById(Long id);

    /**
     * 查询网站Banner列表
     * 
     * @param websiteBanner 网站Banner
     * @return 网站Banner集合
     */
    public List<WebsiteBanner> selectWebsiteBannerList(WebsiteBanner websiteBanner);

    /**
     * 查询启用的网站Banner列表
     * 
     * @return 网站Banner集合
     */
    public List<WebsiteBanner> selectEnabledWebsiteBannerList();

    /**
     * 新增网站Banner
     * 
     * @param websiteBanner 网站Banner
     * @return 结果
     */
    public int insertWebsiteBanner(WebsiteBanner websiteBanner);

    /**
     * 修改网站Banner
     * 
     * @param websiteBanner 网站Banner
     * @return 结果
     */
    public int updateWebsiteBanner(WebsiteBanner websiteBanner);

    /**
     * 批量删除网站Banner
     * 
     * @param ids 需要删除的网站Banner主键集合
     * @return 结果
     */
    public int deleteWebsiteBannerByIds(Long[] ids);

    /**
     * 删除网站Banner信息
     * 
     * @param id 网站Banner主键
     * @return 结果
     */
    public int deleteWebsiteBannerById(Long id);
} 