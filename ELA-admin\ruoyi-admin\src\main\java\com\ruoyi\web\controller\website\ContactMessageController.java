package com.ruoyi.web.controller.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.ContactMessage;
import com.ruoyi.system.service.IContactMessageService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 联系消息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/website/contactMessage")
public class ContactMessageController extends BaseController
{
    @Autowired
    private IContactMessageService contactMessageService;

    /**
     * 查询联系消息列表
     */
    @PreAuthorize("@ss.hasPermi('website:contactMessage:list')")
    @GetMapping("/list")
    public TableDataInfo list(ContactMessage contactMessage)
    {
        startPage();
        List<ContactMessage> list = contactMessageService.selectContactMessageList(contactMessage);
        return getDataTable(list);
    }

    /**
     * 导出联系消息列表
     */
    @PreAuthorize("@ss.hasPermi('website:contactMessage:export')")
    @Log(title = "联系消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ContactMessage contactMessage)
    {
        List<ContactMessage> list = contactMessageService.selectContactMessageList(contactMessage);
        ExcelUtil<ContactMessage> util = new ExcelUtil<ContactMessage>(ContactMessage.class);
        util.exportExcel(response, list, "联系消息数据");
    }

    /**
     * 获取联系消息详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:contactMessage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(contactMessageService.selectContactMessageById(id));
    }

    /**
     * 新增联系消息
     */
    @PreAuthorize("@ss.hasPermi('website:contactMessage:add')")
    @Log(title = "联系消息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContactMessage contactMessage)
    {
        return toAjax(contactMessageService.insertContactMessage(contactMessage));
    }

    /**
     * 修改联系消息
     */
    @PreAuthorize("@ss.hasPermi('website:contactMessage:edit')")
    @Log(title = "联系消息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContactMessage contactMessage)
    {
        return toAjax(contactMessageService.updateContactMessage(contactMessage));
    }

    /**
     * 删除联系消息
     */
    @PreAuthorize("@ss.hasPermi('website:contactMessage:remove')")
    @Log(title = "联系消息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(contactMessageService.deleteContactMessageByIds(ids));
    }

    /**
     * 公开接口：新增联系消息（无需认证）
     */
    @PostMapping("/public/add")
    public AjaxResult addPublic(@RequestBody ContactMessage contactMessage)
    {
        return toAjax(contactMessageService.insertContactMessage(contactMessage));
    }
} 