<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="会员类型" prop="membershipType">
        <el-select v-model="queryParams.membershipType" placeholder="请选择会员类型" clearable>
          <el-option label="企业会员" value="corporate" />
          <el-option label="个人会员" value="individual" />
          <el-option label="准会员" value="associate" />
        </el-select>
      </el-form-item>
      <el-form-item label="申请人" prop="applicantName">
        <el-input
          v-model="queryParams.applicantName"
          placeholder="请输入申请人姓名/企业名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="处理状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择处理状态" clearable>
          <el-option label="待处理" value="0" />
          <el-option label="已处理" value="1" />
          <el-option label="已拒绝" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['website:membership-application:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['website:membership-application:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['website:membership-application:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleBatchProcess('1')"
          v-hasPermi="['website:membership-application:edit']"
        >批量处理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-close"
          size="mini"
          :disabled="multiple"
          @click="handleBatchProcess('2')"
          v-hasPermi="['website:membership-application:edit']"
        >批量拒绝</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['website:membership-application:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="membershipApplicationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请ID" align="center" prop="id" />
      <el-table-column label="会员类型" align="center" prop="membershipType">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.membershipType === 'corporate'" type="primary">企业会员</el-tag>
          <el-tag v-else-if="scope.row.membershipType === 'individual'" type="success">个人会员</el-tag>
          <el-tag v-else-if="scope.row.membershipType === 'associate'" type="warning">准会员</el-tag>
          <span v-else>{{ scope.row.membershipType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请人姓名/企业名称" align="center" prop="applicantName" :show-overflow-tooltip="true" />
      <el-table-column label="联络人" align="center" prop="contactPerson" />
      <el-table-column label="电话号码" align="center" prop="phone" />
      <el-table-column label="电子邮箱" align="center" prop="email" :show-overflow-tooltip="true" />
      <el-table-column label="处理状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === '0'" type="info">待处理</el-tag>
          <el-tag v-else-if="scope.row.status === '1'" type="success">已处理</el-tag>
          <el-tag v-else-if="scope.row.status === '2'" type="danger">已拒绝</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['website:membership-application:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['website:membership-application:edit']"
          >修改</el-button>
          <el-button
            v-if="scope.row.status === '0'"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleProcess(scope.row, '1')"
            v-hasPermi="['website:membership-application:edit']"
          >已处理</el-button>
          <el-button
            v-if="scope.row.status === '0'"
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleProcess(scope.row, '2')"
            v-hasPermi="['website:membership-application:edit']"
          >已拒绝</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['website:membership-application:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会员申请表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="会员类型" prop="membershipType">
              <el-select v-model="form.membershipType" placeholder="请选择会员类型">
                <el-option label="企业会员" value="corporate" />
                <el-option label="个人会员" value="individual" />
                <el-option label="准会员" value="associate" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择处理状态">
                <el-option label="待处理" value="0" />
                <el-option label="已处理" value="1" />
                <el-option label="已拒绝" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="申请人姓名/企业名称" prop="applicantName">
              <el-input v-model="form.applicantName" placeholder="请输入申请人姓名/企业名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联络人" prop="contactPerson">
              <el-input v-model="form.contactPerson" placeholder="请输入联络人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="电话号码" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入电话号码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电子邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入电子邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="地址" prop="address">
          <el-input v-model="form.address" type="textarea" placeholder="请输入地址" />
        </el-form-item>
        <el-form-item label="业务描述" prop="businessDescription">
          <el-input v-model="form.businessDescription" type="textarea" placeholder="请输入业务描述" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="推荐人一" prop="recommender1">
              <el-input v-model="form.recommender1" placeholder="请输入推荐人一" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="推荐人二" prop="recommender2">
              <el-input v-model="form.recommender2" placeholder="请输入推荐人二" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="处理备注" prop="processNotes">
          <el-input v-model="form.processNotes" type="textarea" placeholder="请输入处理备注" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="处理人" prop="processedBy">
              <el-input v-model="form.processedBy" placeholder="请输入处理人" />
            </el-form-item>
          </el-col>
                     <el-col :span="12">
             <el-form-item label="处理时间" prop="processedTime">
               <el-date-picker clearable
                 v-model="form.processedTime"
                 type="datetime"
                 value-format="yyyy-MM-dd HH:mm:ss"
                 format="yyyy-MM-dd HH:mm:ss"
                 placeholder="请选择处理时间">
               </el-date-picker>
             </el-form-item>
           </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看会员申请表对话框 -->
    <el-dialog title="查看申请详情" :visible.sync="viewOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="申请ID">{{ viewForm.id }}</el-descriptions-item>
        <el-descriptions-item label="会员类型">
          <el-tag v-if="viewForm.membershipType === 'corporate'" type="primary">企业会员</el-tag>
          <el-tag v-else-if="viewForm.membershipType === 'individual'" type="success">个人会员</el-tag>
          <el-tag v-else-if="viewForm.membershipType === 'associate'" type="warning">准会员</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="申请人姓名/企业名称">{{ viewForm.applicantName }}</el-descriptions-item>
        <el-descriptions-item label="联络人">{{ viewForm.contactPerson || '-' }}</el-descriptions-item>
        <el-descriptions-item label="电话号码">{{ viewForm.phone }}</el-descriptions-item>
        <el-descriptions-item label="电子邮箱">{{ viewForm.email }}</el-descriptions-item>
        <el-descriptions-item label="地址" :span="2">{{ viewForm.address }}</el-descriptions-item>
        <el-descriptions-item label="业务描述" :span="2">{{ viewForm.businessDescription }}</el-descriptions-item>
        <el-descriptions-item label="推荐人一">{{ viewForm.recommender1 || '-' }}</el-descriptions-item>
        <el-descriptions-item label="推荐人二">{{ viewForm.recommender2 || '-' }}</el-descriptions-item>
        <el-descriptions-item label="处理状态">
          <el-tag v-if="viewForm.status === '0'" type="info">待处理</el-tag>
          <el-tag v-else-if="viewForm.status === '1'" type="success">已处理</el-tag>
          <el-tag v-else-if="viewForm.status === '2'" type="danger">已拒绝</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ parseTime(viewForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="处理人">{{ viewForm.processedBy || '-' }}</el-descriptions-item>
        <el-descriptions-item label="处理时间">{{ viewForm.processedTime ? parseTime(viewForm.processedTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}</el-descriptions-item>
        <el-descriptions-item label="处理备注" :span="2">{{ viewForm.processNotes || '-' }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 处理备注对话框 -->
    <el-dialog title="添加处理备注" :visible.sync="processOpen" width="500px" append-to-body>
      <el-form ref="processForm" :model="processForm" :rules="processRules" label-width="80px">
        <el-form-item label="处理状态" prop="status">
          <el-select v-model="processForm.status" placeholder="请选择处理状态">
            <el-option label="已处理" value="1" />
            <el-option label="已拒绝" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理备注" prop="processNotes">
          <el-input
            v-model="processForm.processNotes"
            type="textarea"
            :rows="4"
            placeholder="请输入处理备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitProcess">确 定</el-button>
        <el-button @click="cancelProcess">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMembershipApplication, getMembershipApplication, delMembershipApplication, addMembershipApplication, updateMembershipApplication } from "@/api/website/membership-application";

export default {
  name: "MembershipApplication",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会员申请表表格数据
      membershipApplicationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 是否显示处理弹出层
      processOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        membershipType: null,
        applicantName: null,
        status: null,
        processedBy: null
      },
      // 表单参数
      form: {},
      // 查看表单参数
      viewForm: {},
      // 处理表单参数
      processForm: {
        id: null,
        status: '',
        processNotes: ''
      },
      // 处理表单校验
      processRules: {
        status: [
          { required: true, message: "处理状态不能为空", trigger: "change" }
        ]
      },
      // 表单校验
      rules: {
        membershipType: [
          { required: true, message: "会员类型不能为空", trigger: "change" }
        ],
        applicantName: [
          { required: true, message: "申请人姓名/企业名称不能为空", trigger: "blur" }
        ],
        phone: [
          { required: true, message: "电话号码不能为空", trigger: "blur" }
        ],
        email: [
          { required: true, message: "电子邮箱不能为空", trigger: "blur" }
        ],
        address: [
          { required: true, message: "地址不能为空", trigger: "blur" }
        ],
        businessDescription: [
          { required: true, message: "业务描述不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询会员申请表列表 */
    getList() {
      this.loading = true;
      listMembershipApplication(this.queryParams).then(response => {
        this.membershipApplicationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        membershipType: null,
        applicantName: null,
        contactPerson: null,
        phone: null,
        email: null,
        address: null,
        businessDescription: null,
        recommender1: null,
        recommender2: null,
        status: "0",
        processNotes: null,
        processedBy: null,
        processedTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加会员申请表";
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.viewForm = row;
      this.viewOpen = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMembershipApplication(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改会员申请表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 如果状态变为已处理或已拒绝，且处理时间为空，则设置当前时间
          if ((this.form.status === "1" || this.form.status === "2") && !this.form.processedTime) {
            this.form.processedTime = this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
          }
          
          if (this.form.id != null) {
            updateMembershipApplication(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMembershipApplication(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除会员申请表编号为"' + ids + '"的数据项？').then(function() {
        return delMembershipApplication(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 快捷处理按钮操作 */
    handleProcess(row, status) {
      this.processForm = {
        id: row.id,
        status: status,
        processNotes: ''
      };
      this.processOpen = true;
    },
    /** 批量处理按钮操作 */
    handleBatchProcess(status) {
      const statusText = status === '1' ? '已处理' : '已拒绝';
      this.$modal.confirm(`确认将选中的 ${this.ids.length} 个申请标记为${statusText}？`).then(function() {
        const promises = this.ids.map(id => {
          const data = {
            id: id,
            status: status,
            processedTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
            processedBy: this.$store.getters.name
          };
          return updateMembershipApplication(data);
        });
        return Promise.all(promises);
      }.bind(this)).then(() => {
        this.getList();
        this.$modal.msgSuccess(`已成功批量标记为${statusText}`);
      }).catch(() => {});
    },
    /** 提交处理表单 */
    submitProcess() {
      this.$refs["processForm"].validate(valid => {
        if (valid) {
          const statusText = this.processForm.status === '1' ? '已处理' : '已拒绝';
          const data = {
            id: this.processForm.id,
            status: this.processForm.status,
            processNotes: this.processForm.processNotes,
            processedTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
            processedBy: this.$store.getters.name
          };
          updateMembershipApplication(data).then(response => {
            this.$modal.msgSuccess(`已成功标记为${statusText}`);
            this.processOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消处理表单 */
    cancelProcess() {
      this.processOpen = false;
      this.processForm = {
        id: null,
        status: '',
        processNotes: ''
      };
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('website/membership-application/export', {
        ...this.queryParams
      }, `membership_application_${new Date().getTime()}.xlsx`)
    }
  }
};
</script> 