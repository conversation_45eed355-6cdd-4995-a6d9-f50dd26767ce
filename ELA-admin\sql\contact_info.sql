-- 联系信息表
CREATE TABLE `contact_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `company_name` varchar(200) NOT NULL COMMENT '公司名称',
  `address` text COMMENT '公司地址',
  `phone` varchar(50) COMMENT '联系电话',
  `email` varchar(100) COMMENT '邮箱地址',
  `website` varchar(200) COMMENT '官网地址',
  `business_hours` varchar(200) COMMENT '营业时间',
  `description` text COMMENT '公司描述',
  `icon` varchar(100) COMMENT '图标',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联系信息表';

-- 插入默认数据
INSERT INTO `contact_info` (`id`, `company_name`, `address`, `phone`, `email`, `website`, `business_hours`, `description`, `icon`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1, '香港电商物流协会 (HKELA)', '香港中环金融街8号国际金融中心二期', '+852 1234 5678', '<EMAIL>', 'https://www.hkela.org', '周一至周五 9:00-18:00', '香港电商物流协会致力于推动香港电商物流行业的发展，为会员提供专业的服务和支持。', 'fas fa-building', '0', 'admin', NOW(), 'admin', NOW(), '默认联系信息'); 