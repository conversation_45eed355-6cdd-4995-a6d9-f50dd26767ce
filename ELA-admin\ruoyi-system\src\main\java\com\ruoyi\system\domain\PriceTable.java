package com.ruoyi.system.domain;

// PriceTable.java
import java.util.List;

/**
 * 代表一个完整的价格表，包含表名和多行数据。
 */
public class PriceTable {
    private String tableName;
    private List<PriceTableRow> rows;

    public PriceTable() {}

    public PriceTable(String tableName, List<PriceTableRow> rows) {
        this.tableName = tableName;
        this.rows = rows;
    }

    // Getters and Setters
    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<PriceTableRow> getRows() {
        return rows;
    }

    public void setRows(List<PriceTableRow> rows) {
        this.rows = rows;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== ").append(tableName).append(" ===\n");
        for (PriceTableRow row : rows) {
            sb.append(row.toString()).append("\n");
        }
        return sb.toString();
    }
}
