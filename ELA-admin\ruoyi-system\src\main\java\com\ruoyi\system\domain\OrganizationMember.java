package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 组织架构成员对象 organization_members
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class OrganizationMember extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 分类ID */
    @Excel(name = "分类ID")
    private Long categoryId;

    /** 姓名（中文） */
    @Excel(name = "姓名（中文）")
    private String nameZh;

    /** 姓名（英文） */
    @Excel(name = "姓名（英文）")
    private String nameEn;

    /** 职位（中文） */
    @Excel(name = "职位（中文）")
    private String positionZh;

    /** 职位（英文） */
    @Excel(name = "职位（英文）")
    private String positionEn;

    /** 部门（中文） */
    @Excel(name = "部门（中文）")
    private String departmentZh;

    /** 部门（英文） */
    @Excel(name = "部门（英文）")
    private String departmentEn;

    /** 公司（中文） */
    @Excel(name = "公司（中文）")
    private String companyZh;

    /** 公司（英文） */
    @Excel(name = "公司（英文）")
    private String companyEn;

    /** 个人简介（中文） */
    @Excel(name = "个人简介（中文）")
    private String bioZh;

    /** 个人简介（英文） */
    @Excel(name = "个人简介（英文）")
    private String bioEn;

    /** 头像URL */
    @Excel(name = "头像URL")
    private String avatarUrl;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;

    /** 电话 */
    @Excel(name = "电话")
    private String phone;

    /** LinkedIn链接 */
    @Excel(name = "LinkedIn链接")
    private String linkedinUrl;

    /** Facebook链接 */
    @Excel(name = "Facebook链接")
    private String facebookUrl;

    /** LinkedIn账号 */
    @Excel(name = "LinkedIn账号")
    private String linkedinAccount;

    /** Facebook账号 */
    @Excel(name = "Facebook账号")
    private String facebookAccount;

    /** 成员类型ID */
    @Excel(name = "成员类型ID")
    private Long memberType;

    /** 是否是领导层（0否 1是） */
    @Excel(name = "是否是领导层", readConverterExp = "0=否,1=是")
    private Integer isLeadership;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 关联的分类信息 */
    private OrganizationCategory category;

    /** 关联的成员类型信息 */
    private MemberType memberTypeInfo;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }

    public void setNameZh(String nameZh) 
    {
        this.nameZh = nameZh;
    }

    public String getNameZh() 
    {
        return nameZh;
    }

    public void setNameEn(String nameEn) 
    {
        this.nameEn = nameEn;
    }

    public String getNameEn() 
    {
        return nameEn;
    }

    public void setPositionZh(String positionZh) 
    {
        this.positionZh = positionZh;
    }

    public String getPositionZh() 
    {
        return positionZh;
    }

    public void setPositionEn(String positionEn) 
    {
        this.positionEn = positionEn;
    }

    public String getPositionEn() 
    {
        return positionEn;
    }

    public void setDepartmentZh(String departmentZh) 
    {
        this.departmentZh = departmentZh;
    }

    public String getDepartmentZh() 
    {
        return departmentZh;
    }

    public void setDepartmentEn(String departmentEn) 
    {
        this.departmentEn = departmentEn;
    }

    public String getDepartmentEn() 
    {
        return departmentEn;
    }

    public void setCompanyZh(String companyZh) 
    {
        this.companyZh = companyZh;
    }

    public String getCompanyZh() 
    {
        return companyZh;
    }

    public void setCompanyEn(String companyEn) 
    {
        this.companyEn = companyEn;
    }

    public String getCompanyEn() 
    {
        return companyEn;
    }

    public void setBioZh(String bioZh) 
    {
        this.bioZh = bioZh;
    }

    public String getBioZh() 
    {
        return bioZh;
    }

    public void setBioEn(String bioEn) 
    {
        this.bioEn = bioEn;
    }

    public String getBioEn() 
    {
        return bioEn;
    }

    public void setAvatarUrl(String avatarUrl) 
    {
        this.avatarUrl = avatarUrl;
    }

    public String getAvatarUrl() 
    {
        return avatarUrl;
    }

    public void setEmail(String email) 
    {
        this.email = email;
    }

    public String getEmail() 
    {
        return email;
    }

    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }

    public void setLinkedinUrl(String linkedinUrl) 
    {
        this.linkedinUrl = linkedinUrl;
    }

    public String getLinkedinUrl() 
    {
        return linkedinUrl;
    }

    public void setFacebookUrl(String facebookUrl) 
    {
        this.facebookUrl = facebookUrl;
    }

    public String getFacebookUrl() 
    {
        return facebookUrl;
    }

    public void setLinkedinAccount(String linkedinAccount) 
    {
        this.linkedinAccount = linkedinAccount;
    }

    public String getLinkedinAccount() 
    {
        return linkedinAccount;
    }

    public void setFacebookAccount(String facebookAccount) 
    {
        this.facebookAccount = facebookAccount;
    }

    public String getFacebookAccount() 
    {
        return facebookAccount;
    }

    public void setMemberType(Long memberType) 
    {
        this.memberType = memberType;
    }

    public Long getMemberType() 
    {
        return memberType;
    }

    public void setIsLeadership(Integer isLeadership) 
    {
        this.isLeadership = isLeadership;
    }

    public Integer getIsLeadership() 
    {
        return isLeadership;
    }

    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public OrganizationCategory getCategory() 
    {
        return category;
    }

    public void setCategory(OrganizationCategory category) 
    {
        this.category = category;
    }

    public MemberType getMemberTypeInfo() 
    {
        return memberTypeInfo;
    }

    public void setMemberTypeInfo(MemberType memberTypeInfo) 
    {
        this.memberTypeInfo = memberTypeInfo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("categoryId", getCategoryId())
            .append("nameZh", getNameZh())
            .append("nameEn", getNameEn())
            .append("positionZh", getPositionZh())
            .append("positionEn", getPositionEn())
            .append("departmentZh", getDepartmentZh())
            .append("departmentEn", getDepartmentEn())
            .append("companyZh", getCompanyZh())
            .append("companyEn", getCompanyEn())
            .append("bioZh", getBioZh())
            .append("bioEn", getBioEn())
            .append("avatarUrl", getAvatarUrl())
            .append("email", getEmail())
            .append("phone", getPhone())
            .append("linkedinUrl", getLinkedinUrl())
            .append("facebookUrl", getFacebookUrl())
            .append("linkedinAccount", getLinkedinAccount())
            .append("facebookAccount", getFacebookAccount())
            .append("memberType", getMemberType())
            .append("isLeadership", getIsLeadership())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}