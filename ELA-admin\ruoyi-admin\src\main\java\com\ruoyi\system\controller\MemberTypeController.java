package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.MemberType;
import com.ruoyi.system.service.IMemberTypeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 成员类型Controller
 * 
 * <AUTHOR>
 * @date 2023-12-01
 */
@RestController
@RequestMapping("/system/organization/membertype")
public class MemberTypeController extends BaseController
{
    @Autowired
    private IMemberTypeService memberTypeService;

    /**
     * 查询成员类型列表
     */
    @PreAuthorize("@ss.hasPermi('website:organization:membertype:list')")
    @GetMapping("/list")
    public TableDataInfo list(MemberType memberType)
    {
        startPage();
        List<MemberType> list = memberTypeService.selectMemberTypeList(memberType);
        return getDataTable(list);
    }

    /**
     * 导出成员类型列表
     */
    @PreAuthorize("@ss.hasPermi('website:organization:membertype:export')")
    @Log(title = "成员类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MemberType memberType)
    {
        List<MemberType> list = memberTypeService.selectMemberTypeList(memberType);
        ExcelUtil<MemberType> util = new ExcelUtil<MemberType>(MemberType.class);
        util.exportExcel(response, list, "成员类型数据");
    }

    /**
     * 获取成员类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:organization:membertype:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(memberTypeService.selectMemberTypeById(id));
    }

    /**
     * 新增成员类型
     */
    @PreAuthorize("@ss.hasPermi('website:organization:membertype:add')")
    @Log(title = "成员类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MemberType memberType)
    {
        // 检查类型代码是否唯一
        if ("1".equals(memberTypeService.checkTypeCodeUnique(memberType.getTypeCode())))
        {
            return error("新增成员类型'" + memberType.getNameZh() + "'失败，类型代码已存在");
        }
        memberType.setCreateBy(getUsername());
        return toAjax(memberTypeService.insertMemberType(memberType));
    }

    /**
     * 修改成员类型
     */
    @PreAuthorize("@ss.hasPermi('website:organization:membertype:edit')")
    @Log(title = "成员类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MemberType memberType)
    {
        // 检查类型代码是否唯一（排除当前记录）
        MemberType existingType = memberTypeService.selectMemberTypeByCode(memberType.getTypeCode());
        if (existingType != null && !existingType.getId().equals(memberType.getId()))
        {
            return error("修改成员类型'" + memberType.getNameZh() + "'失败，类型代码已存在");
        }
        memberType.setUpdateBy(getUsername());
        return toAjax(memberTypeService.updateMemberType(memberType));
    }

    /**
     * 删除成员类型
     */
    @PreAuthorize("@ss.hasPermi('website:organization:membertype:remove')")
    @Log(title = "成员类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(memberTypeService.deleteMemberTypeByIds(ids));
    }

    /**
     * 获取成员类型选项列表
     */
    @GetMapping("/options")
    public AjaxResult getOptions()
    {
        List<MemberType> list = memberTypeService.selectMemberTypeOptions();
        return success(list);
    }

    /**
     * 根据组织架构分类ID获取成员类型选项列表
     */
    @GetMapping("/options/{categoryId}")
    public AjaxResult getOptionsByCategoryId(@PathVariable("categoryId") Long categoryId)
    {
        List<MemberType> list = memberTypeService.selectMemberTypeOptionsByCategoryId(categoryId);
        return success(list);
    }
} 