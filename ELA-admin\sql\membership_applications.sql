-- 会员申请表
CREATE TABLE `membership_applications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `membership_type` varchar(50) NOT NULL COMMENT '会员类型',
  `applicant_name` varchar(200) NOT NULL COMMENT '申请人姓名/企业名称',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '联络人',
  `phone` varchar(20) NOT NULL COMMENT '电话号码',
  `email` varchar(100) NOT NULL COMMENT '电子邮箱',
  `address` text NOT NULL COMMENT '地址',
  `business_description` text NOT NULL COMMENT '业务描述',
  `recommender1` varchar(100) DEFAULT NULL COMMENT '推荐人一',
  `recommender2` varchar(100) DEFAULT NULL COMMENT '推荐人二',
  `status` char(1) DEFAULT '0' COMMENT '处理状态（0待处理 1已处理 2已拒绝）',
  `process_notes` text DEFAULT NULL COMMENT '处理备注',
  `processed_by` varchar(64) DEFAULT NULL COMMENT '处理人',
  `processed_time` datetime DEFAULT NULL COMMENT '处理时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员申请表';

-- 插入测试数据
INSERT INTO `membership_applications` (`membership_type`, `applicant_name`, `contact_person`, `phone`, `email`, `address`, `business_description`, `recommender1`, `recommender2`, `status`, `create_time`) VALUES
('corporate', '香港物流科技有限公司', '张经理', '+852 2123 4567', '<EMAIL>', '香港中环金融街88号', '专注于跨境电商物流解决方案，提供仓储、配送、通关等一站式服务', '李顾问', '王总监', '0', NOW()),
('individual', '陈志强', NULL, '+852 9876 5432', '<EMAIL>', '香港湾仔轩尼诗道123号', '物流行业资深专家，拥有15年供应链管理经验', '刘教授', NULL, '1', NOW()),
('associate', '亚太物流联盟', '林秘书', '+852 3456 7890', '<EMAIL>', '香港铜锣湾时代广场456号', '区域性物流行业协会，致力于推动亚太地区物流业发展', '赵会长', '孙秘书长', '0', NOW()); 