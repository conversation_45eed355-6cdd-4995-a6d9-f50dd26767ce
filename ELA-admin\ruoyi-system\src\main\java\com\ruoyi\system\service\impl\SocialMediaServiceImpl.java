package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SocialMediaMapper;
import com.ruoyi.system.domain.SocialMedia;
import com.ruoyi.system.service.ISocialMediaService;

/**
 * 社交媒体信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class SocialMediaServiceImpl implements ISocialMediaService 
{
    @Autowired
    private SocialMediaMapper socialMediaMapper;

    /**
     * 查询社交媒体信息
     * 
     * @param id 社交媒体信息主键
     * @return 社交媒体信息
     */
    @Override
    public SocialMedia selectSocialMediaById(Long id)
    {
        return socialMediaMapper.selectSocialMediaById(id);
    }

    /**
     * 查询社交媒体信息列表
     * 
     * @param socialMedia 社交媒体信息
     * @return 社交媒体信息
     */
    @Override
    public List<SocialMedia> selectSocialMediaList(SocialMedia socialMedia)
    {
        return socialMediaMapper.selectSocialMediaList(socialMedia);
    }

    /**
     * 新增社交媒体信息
     * 
     * @param socialMedia 社交媒体信息
     * @return 结果
     */
    @Override
    public int insertSocialMedia(SocialMedia socialMedia)
    {
        return socialMediaMapper.insertSocialMedia(socialMedia);
    }

    /**
     * 修改社交媒体信息
     * 
     * @param socialMedia 社交媒体信息
     * @return 结果
     */
    @Override
    public int updateSocialMedia(SocialMedia socialMedia)
    {
        return socialMediaMapper.updateSocialMedia(socialMedia);
    }

    /**
     * 批量删除社交媒体信息
     * 
     * @param ids 需要删除的社交媒体信息主键
     * @return 结果
     */
    @Override
    public int deleteSocialMediaByIds(Long[] ids)
    {
        return socialMediaMapper.deleteSocialMediaByIds(ids);
    }

    /**
     * 删除社交媒体信息信息
     * 
     * @param id 社交媒体信息主键
     * @return 结果
     */
    @Override
    public int deleteSocialMediaById(Long id)
    {
        return socialMediaMapper.deleteSocialMediaById(id);
    }
} 