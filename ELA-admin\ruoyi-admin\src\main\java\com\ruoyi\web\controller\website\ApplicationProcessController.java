package com.ruoyi.web.controller.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.ApplicationProcess;
import com.ruoyi.system.service.IApplicationProcessService;

/**
 * 申请流程Controller
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
@RestController
@RequestMapping("/website/application-process")
public class ApplicationProcessController extends BaseController
{
    @Autowired
    private IApplicationProcessService applicationProcessService;

    /**
     * 查询申请流程列表
     */
    @PreAuthorize("@ss.hasPermi('website:application-process:list')")
    @GetMapping("/list")
    public TableDataInfo list(ApplicationProcess applicationProcess)
    {
        startPage();
        List<ApplicationProcess> list = applicationProcessService.selectApplicationProcessList(applicationProcess);
        return getDataTable(list);
    }

    /**
     * 导出申请流程列表
     */
    @PreAuthorize("@ss.hasPermi('website:application-process:export')")
    @Log(title = "申请流程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ApplicationProcess applicationProcess)
    {
        List<ApplicationProcess> list = applicationProcessService.selectApplicationProcessList(applicationProcess);
        ExcelUtil<ApplicationProcess> util = new ExcelUtil<ApplicationProcess>(ApplicationProcess.class);
        util.exportExcel(response, list, "申请流程数据");
    }

    /**
     * 获取申请流程详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:application-process:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(applicationProcessService.selectApplicationProcessById(id));
    }

    /**
     * 新增申请流程
     */
    @PreAuthorize("@ss.hasPermi('website:application-process:add')")
    @Log(title = "申请流程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ApplicationProcess applicationProcess)
    {
        applicationProcess.setCreateBy(getUsername());
        return toAjax(applicationProcessService.insertApplicationProcess(applicationProcess));
    }

    /**
     * 修改申请流程
     */
    @PreAuthorize("@ss.hasPermi('website:application-process:edit')")
    @Log(title = "申请流程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ApplicationProcess applicationProcess)
    {
        applicationProcess.setUpdateBy(getUsername());
        return toAjax(applicationProcessService.updateApplicationProcess(applicationProcess));
    }

    /**
     * 删除申请流程
     */
    @PreAuthorize("@ss.hasPermi('website:application-process:remove')")
    @Log(title = "申请流程", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(applicationProcessService.deleteApplicationProcessByIds(ids));
    }

    /**
     * 获取启用的申请流程列表(供前端使用)
     */
    @GetMapping("/public")
    public AjaxResult getPublicApplicationProcess()
    {
        ApplicationProcess applicationProcess = new ApplicationProcess();
        applicationProcess.setStatus("0"); // 只获取启用的申请流程
        List<ApplicationProcess> list = applicationProcessService.selectApplicationProcessList(applicationProcess);
        return AjaxResult.success(list);
    }
} 