-- 添加成员类型管理菜单和权限
-- 获取组织架构管理菜单ID
SET @org_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '组织架构管理' AND parent_id = 2000);

-- 添加成员类型管理菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('成员类型管理', @org_menu_id, 3, 'membertype', 'website/organization/memberType/index', '', 1, 0, 'C', '0', '0', 'website:organization:membertype:list', 'component', 'admin', sysdate(), '', null, '成员类型管理菜单');

-- 获取成员类型管理菜单ID
SET @member_type_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '成员类型管理' AND parent_id = @org_menu_id);

-- 添加成员类型管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
('成员类型查询', @member_type_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'website:organization:membertype:query', '#', 'admin', sysdate(), '', null, ''),
('成员类型新增', @member_type_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'website:organization:membertype:add', '#', 'admin', sysdate(), '', null, ''),
('成员类型修改', @member_type_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'website:organization:membertype:edit', '#', 'admin', sysdate(), '', null, ''),
('成员类型删除', @member_type_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'website:organization:membertype:remove', '#', 'admin', sysdate(), '', null, ''),
('成员类型导出', @member_type_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'website:organization:membertype:export', '#', 'admin', sysdate(), '', null, '');

-- 为admin角色分配成员类型管理权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE menu_name IN ('成员类型管理', '成员类型查询', '成员类型新增', '成员类型修改', '成员类型删除', '成员类型导出')
AND parent_id = @member_type_menu_id OR menu_id = @member_type_menu_id; 