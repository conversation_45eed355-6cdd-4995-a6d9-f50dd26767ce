-- 迁移现有组织成员数据的成员类型关联
-- 创建时间: 2024-12-13 14:48:25
-- 执行前提：已执行 fix_member_type_category_relation_20241213_143027.sql

-- 注意：执行此脚本前请先备份数据！

-- 第一步：检查数据状态
SELECT '=== 执行前数据检查 ===' as info;
SELECT '组织架构分类数量：' as info, COUNT(*) as count FROM organization_categories;
SELECT '成员类型数量：' as info, COUNT(*) as count FROM member_types;
SELECT '组织成员数量：' as info, COUNT(*) as count FROM organization_members;

-- 第二步：显示当前需要迁移的数据
SELECT '=== 需要迁移的成员数据 ===' as info;
SELECT 
  om.id,
  om.name_zh,
  oc.name_zh as category_name,
  om.member_type as old_member_type,
  mt.id as new_member_type_id,
  mt.name_zh as new_member_type_name
FROM organization_members om
LEFT JOIN organization_categories oc ON om.category_id = oc.id
LEFT JOIN member_types mt ON mt.category_id = om.category_id
WHERE om.member_type IS NOT NULL
ORDER BY om.category_id, om.id;

-- 第三步：执行数据迁移
-- 更新理事会成员（假设分类ID为1）
UPDATE organization_members om
SET member_type = (
  SELECT mt.id 
  FROM member_types mt 
  WHERE mt.category_id = om.category_id 
  AND (
    (om.member_type = 'president' AND mt.type_code = 'president') OR
    (om.member_type = 'vice_president' AND mt.type_code = 'vice_president') OR
    (om.member_type = 'director' AND mt.type_code = 'director') OR
    (om.member_type = 'secretary' AND mt.type_code = 'secretary_general') OR
    (om.member_type = 'treasurer' AND mt.type_code = 'treasurer')
  )
  LIMIT 1
)
WHERE om.category_id IN (
  SELECT id FROM organization_categories WHERE name_zh LIKE '%理事%'
)
AND om.member_type IN ('president', 'vice_president', 'director', 'secretary', 'treasurer');

-- 更新顾问团成员（假设分类ID为2）
UPDATE organization_members om
SET member_type = (
  SELECT mt.id 
  FROM member_types mt 
  WHERE mt.category_id = om.category_id 
  AND (
    (om.member_type = 'advisor' AND mt.type_code = 'advisor') OR
    (om.member_type = 'honorary_advisor' AND mt.type_code = 'honorary_advisor') OR
    (om.member_type = 'member' AND mt.type_code = 'founding_member')
  )
  LIMIT 1
)
WHERE om.category_id IN (
  SELECT id FROM organization_categories WHERE name_zh LIKE '%顾问%' OR name_zh LIKE '%創會%'
)
AND om.member_type IN ('advisor', 'honorary_advisor', 'member');

-- 更新青年委员会成员（假设分类ID为3）
UPDATE organization_members om
SET member_type = (
  SELECT mt.id 
  FROM member_types mt 
  WHERE mt.category_id = om.category_id 
  AND (
    (om.member_type = 'chair' AND mt.type_code = 'chairman') OR
    (om.member_type = 'vice_chair' AND mt.type_code = 'vice_chairman') OR
    (om.member_type = 'secretary' AND mt.type_code = 'secretary') OR
    (om.member_type = 'member' AND mt.type_code = 'committee_member')
  )
  LIMIT 1
)
WHERE om.category_id IN (
  SELECT id FROM organization_categories WHERE name_zh LIKE '%青年%'
)
AND om.member_type IN ('chair', 'vice_chair', 'secretary', 'member');

-- 第四步：验证迁移结果
SELECT '=== 迁移后数据检查 ===' as info;
SELECT 
  om.id,
  om.name_zh,
  oc.name_zh as category_name,
  om.member_type as member_type_id,
  mt.name_zh as member_type_name,
  mt.type_code
FROM organization_members om
LEFT JOIN organization_categories oc ON om.category_id = oc.id
LEFT JOIN member_types mt ON om.member_type = mt.id
ORDER BY om.category_id, om.id;

-- 第五步：检查是否有未迁移的数据
SELECT '=== 未迁移的数据（需要手动处理）===' as info;
SELECT 
  om.id,
  om.name_zh,
  oc.name_zh as category_name,
  om.member_type
FROM organization_members om
LEFT JOIN organization_categories oc ON om.category_id = oc.id
LEFT JOIN member_types mt ON om.member_type = mt.id
WHERE mt.id IS NULL AND om.member_type IS NOT NULL;

-- 说明：
-- 1. 请根据实际的分类ID和成员类型代码调整上述SQL
-- 2. 执行前请先备份数据
-- 3. 建议在测试环境先验证
-- 4. 如有未迁移的数据，请手动处理 