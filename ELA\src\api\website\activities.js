import request from '@/utils/request'

// 查询公开的活动列表
export function getPublicActivities() {
  return request({
    url: '/website/activities/public',
    method: 'get'
  })
}

// 查询即将开始的活动列表
export function getUpcomingActivities() {
  return request({
    url: '/website/activities/upcoming',
    method: 'get'
  })
}

// 查询最近的活动列表
export function getRecentActivities() {
  return request({
    url: '/website/activities/recent',
    method: 'get'
  })
}

// 查询首页显示的活动列表
export function getHomepageActivities() {
  return request({
    url: '/website/activities/homepage',
    method: 'get'
  })
}

// 根据ID查询活动详情
export function getActivityDetail(id) {
  return request({
    url: `/website/activities/detail/${id}`,
    method: 'get'
  })
}