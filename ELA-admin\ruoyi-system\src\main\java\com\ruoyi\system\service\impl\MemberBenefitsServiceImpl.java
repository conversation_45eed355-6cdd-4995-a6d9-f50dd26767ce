package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.MemberBenefitsMapper;
import com.ruoyi.system.domain.MemberBenefits;
import com.ruoyi.system.service.IMemberBenefitsService;

/**
 * 会员权益Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
@Service
public class MemberBenefitsServiceImpl implements IMemberBenefitsService 
{
    @Autowired
    private MemberBenefitsMapper memberBenefitsMapper;

    /**
     * 查询会员权益
     * 
     * @param id 会员权益主键
     * @return 会员权益
     */
    @Override
    public MemberBenefits selectMemberBenefitsById(Long id)
    {
        return memberBenefitsMapper.selectMemberBenefitsById(id);
    }

    /**
     * 查询会员权益列表
     * 
     * @param memberBenefits 会员权益
     * @return 会员权益
     */
    @Override
    public List<MemberBenefits> selectMemberBenefitsList(MemberBenefits memberBenefits)
    {
        return memberBenefitsMapper.selectMemberBenefitsList(memberBenefits);
    }

    /**
     * 新增会员权益
     * 
     * @param memberBenefits 会员权益
     * @return 结果
     */
    @Override
    public int insertMemberBenefits(MemberBenefits memberBenefits)
    {
        return memberBenefitsMapper.insertMemberBenefits(memberBenefits);
    }

    /**
     * 修改会员权益
     * 
     * @param memberBenefits 会员权益
     * @return 结果
     */
    @Override
    public int updateMemberBenefits(MemberBenefits memberBenefits)
    {
        return memberBenefitsMapper.updateMemberBenefits(memberBenefits);
    }

    /**
     * 批量删除会员权益
     * 
     * @param ids 需要删除的会员权益主键
     * @return 结果
     */
    @Override
    public int deleteMemberBenefitsByIds(Long[] ids)
    {
        return memberBenefitsMapper.deleteMemberBenefitsByIds(ids);
    }

    /**
     * 删除会员权益信息
     * 
     * @param id 会员权益主键
     * @return 结果
     */
    @Override
    public int deleteMemberBenefitsById(Long id)
    {
        return memberBenefitsMapper.deleteMemberBenefitsById(id);
    }
} 