-- ----------------------------
-- Table structure for about_us
-- ----------------------------
DROP TABLE IF EXISTS `about_us`;
CREATE TABLE `about_us` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title_zh` varchar(200) DEFAULT NULL COMMENT '标题（中文）',
  `title_en` varchar(200) DEFAULT NULL COMMENT '标题（英文）',
  `subtitle_zh` varchar(500) DEFAULT NULL COMMENT '副标题（中文）',
  `subtitle_en` varchar(500) DEFAULT NULL COMMENT '副标题（英文）',
  `company_name_zh` varchar(200) DEFAULT NULL COMMENT '公司名称（中文）',
  `company_name_en` varchar(200) DEFAULT NULL COMMENT '公司名称（英文）',
  `description_zh` text COMMENT '公司描述（中文）',
  `description_en` text COMMENT '公司描述（英文）',
  `statistics_config` text COMMENT '统计数据配置（JSON格式）',
  `mission_cards_config` text COMMENT '使命卡片配置（JSON格式）',
  `image_url` varchar(500) DEFAULT NULL COMMENT '右侧图片URL',
  `image_desc_zh` varchar(200) DEFAULT NULL COMMENT '图片描述（中文）',
  `image_desc_en` varchar(200) DEFAULT NULL COMMENT '图片描述（英文）',
  `background_image_url` varchar(500) DEFAULT NULL COMMENT '背景图片URL',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='关于我们表';

-- ----------------------------
-- Records of about_us
-- ----------------------------
INSERT INTO `about_us` VALUES (1, '我們的使命', 'Our Mission', '專業性強、服務優質，致力於為客戶提供卓越的產品和服務', 'Professional excellence, quality service, committed to providing outstanding products and services', '香港電商物流協會', 'Hong Kong E-commerce Logistics Association', '我們旨在匯聚一群熱心回饋社會和行業的精英，通過評估香港物流及供應鏈各持份者的需求，向政府反映實際狀況，並提供建設性意見以改善基礎設施和科技應用，從而提升整體電子商貿行業的競爭力。\n\n協會致力於推動電子商貿物流業的發展，為會員提供一個互動平台，讓大家分享專業意見、增進合作機會，並隨時交流國際市場趨勢，共同打造更佳的商業環境，提升服務質量。\n\n我們也將推廣香港電子商貿物流行業在國際市場的發展，促進市場繁榮，為會員創造更多商機和成長。\n\n同時，我們致力於培育年輕專才，鼓勵他們加入電子商貿物流行業。', 'We seek to bring together a group of elite professionals passionate about the industry. By evaluating the needs of various stakeholders in Hong Kong\'s logistics and supply chain sectors, we communicate the current situation to the government and provide constructive recommendations to enhance infrastructure and technology applications, ultimately boosting the competitiveness of the E-commerce industry.\n\nThe Association is committed to advancing the E-commerce sector. We provide our members with an interactive platform to share insights, encourage collaboration, and exchange information on global market trends. Together, we aim to create a better business environment and improve service quality.\n\nFurthermore, we strive to promote the growth of Hong Kong\'s E-commerce logistics industry in international markets, fostering market prosperity and generating more opportunities for our members.\n\nAt the same time, we are dedicated to nurturing young talent and encouraging their entry into the E-commerce logistics field.', '[
  {
    "number": "5",
    "unit": {"zh": "年", "en": "Years"},
    "label": {"zh": "系統開發經驗", "en": "Development Experience"}
  },
  {
    "number": "50",
    "unit": {"zh": "項", "en": "+"},
    "label": {"zh": "行業活動", "en": "Industry Events"}
  },
  {
    "number": "200",
    "unit": {"zh": "家", "en": "+"},
    "label": {"zh": "合作會員企業", "en": "Member Companies"}
  },
  {
    "number": "1000",
    "unit": {"zh": "人", "en": "+"},
    "label": {"zh": "服務專業人士", "en": "Professionals Served"}
  }
]', '[
  {
    "icon": "fas fa-users",
    "title": {"zh": "匯聚精英", "en": "Elite Network"},
    "description": {"zh": "匯聚業界精英，推動行業發展", "en": "Uniting industry elites to drive development"}
  },
  {
    "icon": "fas fa-rocket",
    "title": {"zh": "推動創新", "en": "Drive Innovation"},
    "description": {"zh": "提供專業平台，促進合作交流", "en": "Professional platform for collaboration"}
  },
  {
    "icon": "fas fa-globe-asia",
    "title": {"zh": "國際視野", "en": "Global Vision"},
    "description": {"zh": "推廣國際市場，創造更多機會", "en": "Promoting international markets"}
  },
  {
    "icon": "fas fa-graduation-cap",
    "title": {"zh": "人才培育", "en": "Talent Development"},
    "description": {"zh": "培育年輕人才，傳承行業知識", "en": "Nurturing young talents for the future"}
  }
]', '/profile/upload/2024/01/01/company_20240101_001.png', '辦公地址：香港中環商業中心', 'Office Location: Central Business District, Hong Kong', '/profile/upload/2024/01/01/mission_bg_20240101_001.png', 1, '0', NOW(), NOW());

-- ----------------------------
-- Menu SQL for about_us
-- ----------------------------
-- 父菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('关于我们', '2000', '4', 'aboutus', 'website/aboutus/index', 1, 0, 'C', '0', '0', 'website:aboutus:list', 'el-icon-info', 'admin', NOW(), '', NULL, '关于我们菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('关于我们查询', @parentId, '1', '#', '', 1, 0, 'F', '0', '0', 'website:aboutus:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('关于我们新增', @parentId, '2', '#', '', 1, 0, 'F', '0', '0', 'website:aboutus:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('关于我们修改', @parentId, '3', '#', '', 1, 0, 'F', '0', '0', 'website:aboutus:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('关于我们删除', @parentId, '4', '#', '', 1, 0, 'F', '0', '0', 'website:aboutus:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('关于我们导出', @parentId, '5', '#', '', 1, 0, 'F', '0', '0', 'website:aboutus:export', '#', 'admin', NOW(), '', NULL, ''); 