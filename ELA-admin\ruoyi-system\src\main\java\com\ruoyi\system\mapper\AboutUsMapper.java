package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.AboutUs;

/**
 * 关于我们Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface AboutUsMapper
{
    /**
     * 查询关于我们
     *
     * @param id 关于我们主键
     * @return 关于我们
     */
    public AboutUs selectAboutUsById(Long id);

    /**
     * 查询关于我们列表
     *
     * @param aboutUs 关于我们
     * @return 关于我们集合
     */
    public List<AboutUs> selectAboutUsList(AboutUs aboutUs);

    /**
     * 查询启用状态的关于我们信息
     *
     * @return 关于我们
     */
    public AboutUs selectAboutUsActive();

    /**
     * 新增关于我们
     *
     * @param aboutUs 关于我们
     * @return 结果
     */
    public int insertAboutUs(AboutUs aboutUs);

    /**
     * 修改关于我们
     *
     * @param aboutUs 关于我们
     * @return 结果
     */
    public int updateAboutUs(AboutUs aboutUs);

    /**
     * 删除关于我们
     *
     * @param id 关于我们主键
     * @return 结果
     */
    public int deleteAboutUsById(Long id);

    /**
     * 批量删除关于我们
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAboutUsByIds(Long[] ids);
} 