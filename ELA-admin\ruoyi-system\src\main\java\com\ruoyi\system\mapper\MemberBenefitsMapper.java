package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.MemberBenefits;

/**
 * 会员权益Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
public interface MemberBenefitsMapper 
{
    /**
     * 查询会员权益
     * 
     * @param id 会员权益主键
     * @return 会员权益
     */
    public MemberBenefits selectMemberBenefitsById(Long id);

    /**
     * 查询会员权益列表
     * 
     * @param memberBenefits 会员权益
     * @return 会员权益集合
     */
    public List<MemberBenefits> selectMemberBenefitsList(MemberBenefits memberBenefits);

    /**
     * 新增会员权益
     * 
     * @param memberBenefits 会员权益
     * @return 结果
     */
    public int insertMemberBenefits(MemberBenefits memberBenefits);

    /**
     * 修改会员权益
     * 
     * @param memberBenefits 会员权益
     * @return 结果
     */
    public int updateMemberBenefits(MemberBenefits memberBenefits);

    /**
     * 删除会员权益
     * 
     * @param id 会员权益主键
     * @return 结果
     */
    public int deleteMemberBenefitsById(Long id);

    /**
     * 批量删除会员权益
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMemberBenefitsByIds(Long[] ids);
} 