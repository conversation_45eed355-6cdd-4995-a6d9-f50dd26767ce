package com.ruoyi.website;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.Activity;
import com.ruoyi.system.service.IActivityService;

/**
 * 前端网站活动查询Controller
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
@RestController
@RequestMapping("/website/activities")
public class WebActivityController extends BaseController
{
    @Autowired
    private IActivityService activityService;

    /**
     * 查询公开的活动列表（供前端网站使用）
     */
    @GetMapping("/public")
    public AjaxResult getPublicActivities()
    {
        Activity activity = new Activity();
        activity.setStatus("0"); // 只查询正常状态的活动
        List<Activity> list = activityService.selectActivityList(activity);
        return AjaxResult.success(list);
    }

    /**
     * 查询即将开始的活动列表
     */
    @GetMapping("/upcoming")
    public AjaxResult getUpcomingActivities()
    {
        Activity activity = new Activity();
        activity.setStatus("0"); // 只查询正常状态的活动
        List<Activity> list = activityService.selectActivityList(activity);
        
        // 可以在这里添加过滤逻辑，比如只返回未来的活动
        return AjaxResult.success(list);
    }

    /**
     * 查询最近的活动列表
     */
    @GetMapping("/recent")
    public AjaxResult getRecentActivities()
    {
        Activity activity = new Activity();
        activity.setStatus("0"); // 只查询正常状态的活动
        List<Activity> list = activityService.selectActivityList(activity);
        
        // 可以在这里添加过滤逻辑，比如只返回最近的活动
        return AjaxResult.success(list);
    }

    /**
     * 查询首页显示的活动列表
     */
    @GetMapping("/homepage")
    public AjaxResult getHomepageActivities()
    {
        Activity activity = new Activity();
        activity.setShowOnHomepage("1");
        activity.setStatus("0"); // 只查询正常状态的活动
        List<Activity> list = activityService.selectActivityList(activity);
        return AjaxResult.success(list);
    }

    /**
     * 根据ID获取活动详情
     */
    @GetMapping("/detail/{id}")
    public AjaxResult getActivityDetail(@PathVariable Long id)
    {
        Activity activity = activityService.selectActivityById(id);
        if (activity == null || !"0".equals(activity.getStatus()))
        {
            return AjaxResult.error("活动不存在或已下线");
        }
        return AjaxResult.success(activity);
    }
}