<!-- <AUTHOR> -->
<template>
  <div class="icon-select-container">
    <el-popover
      placement="bottom-start"
      width="800"
      trigger="click"
      v-model="showDialog"
      popper-class="icon-select-popover"
    >
      <div class="icon-dialog-content">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索图标"
          clearable
          style="margin-bottom: 20px;"
        >
          <template slot="prefix">
            <i class="el-icon-search"></i>
          </template>
        </el-input>
        
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="常用" name="common"></el-tab-pane>
          <el-tab-pane label="用户" name="user"></el-tab-pane>
          <el-tab-pane label="商务" name="business"></el-tab-pane>
          <el-tab-pane label="交通" name="transport"></el-tab-pane>
          <el-tab-pane label="科技" name="tech"></el-tab-pane>
          <el-tab-pane label="其他" name="other"></el-tab-pane>
        </el-tabs>
        
        <div class="icon-grid" v-if="filteredIcons.length > 0" style="max-height: 300px; overflow-y: auto;">
          <div
            v-for="icon in filteredIcons"
            :key="icon.class"
            class="icon-item"
            :class="{ active: selectedIcon === icon.class }"
            @click.stop="selectIcon(icon.class)"
            @mousedown.stop="selectIcon(icon.class)"
            :title="icon.name"
          >
            <i :class="icon.class" style="pointer-events: none;"></i>
            <span class="icon-name" style="pointer-events: none;">{{ icon.name }}</span>
          </div>
        </div>
        
        <div v-else class="no-icons">
          <p>未找到匹配的图标</p>
        </div>
        
        <div style="margin-top: 15px; text-align: right; border-top: 1px solid #eee; padding-top: 15px;">
          <el-button size="small" @click="cancelSelect">取消</el-button>
          <el-button type="primary" size="small" @click="confirmSelect">确定</el-button>
        </div>
      </div>
      
      <el-input
        slot="reference"
        v-model="selectedIcon"
        placeholder="请选择图标"
        readonly
        style="cursor: pointer;"
      >
        <template slot="prefix">
          <i v-if="selectedIcon" :class="selectedIcon" style="margin-right: 8px;"></i>
        </template>
        <template slot="suffix">
          <i class="el-icon-arrow-down" style="cursor: pointer;"></i>
        </template>
      </el-input>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'IconSelect',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showDialog: false,
      searchKeyword: '',
      activeTab: 'common',
      selectedIcon: this.value || '',
      iconCategories: {
        common: [
          { class: 'fas fa-home', name: '首页' },
          { class: 'fas fa-user', name: '用户' },
          { class: 'fas fa-users', name: '用户组' },
          { class: 'fas fa-cog', name: '设置' },
          { class: 'fas fa-heart', name: '收藏' },
          { class: 'fas fa-star', name: '星标' },
          { class: 'fas fa-search', name: '搜索' },
          { class: 'fas fa-plus', name: '添加' },
          { class: 'fas fa-minus', name: '减少' },
          { class: 'fas fa-edit', name: '编辑' },
          { class: 'fas fa-trash', name: '删除' },
          { class: 'fas fa-save', name: '保存' },
          { class: 'fas fa-download', name: '下载' },
          { class: 'fas fa-upload', name: '上传' },
          { class: 'fas fa-print', name: '打印' },
          { class: 'fas fa-share', name: '分享' },
          { class: 'fas fa-link', name: '链接' },
          { class: 'fas fa-lock', name: '锁定' },
          { class: 'fas fa-unlock', name: '解锁' },
          { class: 'fas fa-eye', name: '查看' },
          { class: 'fas fa-eye-slash', name: '隐藏' },
          { class: 'fas fa-check', name: '确认' },
          { class: 'fas fa-times', name: '关闭' },
          { class: 'fas fa-arrow-left', name: '左箭头' },
          { class: 'fas fa-arrow-right', name: '右箭头' },
          { class: 'fas fa-arrow-up', name: '上箭头' },
          { class: 'fas fa-arrow-down', name: '下箭头' },
          { class: 'fas fa-info-circle', name: '信息' },
          { class: 'fas fa-exclamation-triangle', name: '警告' },
          { class: 'fas fa-question-circle', name: '问题' }
        ],
        user: [
          { class: 'fas fa-user', name: '用户' },
          { class: 'fas fa-users', name: '用户组' },
          { class: 'fas fa-user-plus', name: '添加用户' },
          { class: 'fas fa-user-minus', name: '删除用户' },
          { class: 'fas fa-user-edit', name: '编辑用户' },
          { class: 'fas fa-user-check', name: '验证用户' },
          { class: 'fas fa-user-times', name: '禁用用户' },
          { class: 'fas fa-user-shield', name: '管理员' },
          { class: 'fas fa-user-tie', name: '商务人员' },
          { class: 'fas fa-user-graduate', name: '学生' },
          { class: 'fas fa-user-md', name: '医生' },
          { class: 'fas fa-user-nurse', name: '护士' },
          { class: 'fas fa-user-secret', name: '特工' },
          { class: 'fas fa-user-astronaut', name: '宇航员' },
          { class: 'fas fa-user-ninja', name: '忍者' },
          { class: 'fas fa-user-friends', name: '朋友' },
          { class: 'fas fa-user-cog', name: '用户设置' },
          { class: 'fas fa-user-lock', name: '锁定用户' },
          { class: 'fas fa-user-clock', name: '用户时间' },
          { class: 'fas fa-user-tag', name: '用户标签' },
          { class: 'fas fa-id-card', name: '身份证' },
          { class: 'fas fa-id-badge', name: '工牌' },
          { class: 'fas fa-address-card', name: '名片' },
          { class: 'fas fa-portrait', name: '头像' }
        ],
        business: [
          { class: 'fas fa-briefcase', name: '公文包' },
          { class: 'fas fa-building', name: '建筑' },
          { class: 'fas fa-industry', name: '工业' },
          { class: 'fas fa-chart-line', name: '折线图' },
          { class: 'fas fa-chart-bar', name: '柱状图' },
          { class: 'fas fa-chart-pie', name: '饼图' },
          { class: 'fas fa-chart-area', name: '面积图' },
          { class: 'fas fa-money-bill', name: '钞票' },
          { class: 'fas fa-dollar-sign', name: '美元' },
          { class: 'fas fa-euro-sign', name: '欧元' },
          { class: 'fas fa-yen-sign', name: '日元' },
          { class: 'fas fa-pound-sign', name: '英镑' },
          { class: 'fas fa-credit-card', name: '信用卡' },
          { class: 'fas fa-wallet', name: '钱包' },
          { class: 'fas fa-coins', name: '硬币' },
          { class: 'fas fa-piggy-bank', name: '存钱罐' },
          { class: 'fas fa-calculator', name: '计算器' },
          { class: 'fas fa-receipt', name: '收据' },
          { class: 'fas fa-invoice', name: '发票' },
          { class: 'fas fa-balance-scale', name: '天平' },
          { class: 'fas fa-handshake', name: '握手' },
          { class: 'fas fa-contract', name: '合同' },
          { class: 'fas fa-signature', name: '签名' },
          { class: 'fas fa-stamp', name: '印章' },
          { class: 'fas fa-award', name: '奖项' },
          { class: 'fas fa-trophy', name: '奖杯' },
          { class: 'fas fa-medal', name: '奖牌' },
          { class: 'fas fa-ribbon', name: '丝带' },
          { class: 'fas fa-certificate', name: '证书' },
          { class: 'fas fa-graduation-cap', name: '毕业帽' }
        ],
        transport: [
          { class: 'fas fa-car', name: '汽车' },
          { class: 'fas fa-bus', name: '公交车' },
          { class: 'fas fa-truck', name: '卡车' },
          { class: 'fas fa-motorcycle', name: '摩托车' },
          { class: 'fas fa-bicycle', name: '自行车' },
          { class: 'fas fa-train', name: '火车' },
          { class: 'fas fa-subway', name: '地铁' },
          { class: 'fas fa-plane', name: '飞机' },
          { class: 'fas fa-helicopter', name: '直升机' },
          { class: 'fas fa-ship', name: '轮船' },
          { class: 'fas fa-anchor', name: '锚' },
          { class: 'fas fa-rocket', name: '火箭' },
          { class: 'fas fa-taxi', name: '出租车' },
          { class: 'fas fa-ambulance', name: '救护车' },
          { class: 'fas fa-fire-truck', name: '消防车' },
          { class: 'fas fa-police-car', name: '警车' },
          { class: 'fas fa-tractor', name: '拖拉机' },
          { class: 'fas fa-caravan', name: '房车' },
          { class: 'fas fa-shuttle-van', name: '班车' },
          { class: 'fas fa-road', name: '道路' },
          { class: 'fas fa-route', name: '路线' },
          { class: 'fas fa-map', name: '地图' },
          { class: 'fas fa-map-marker-alt', name: '地标' },
          { class: 'fas fa-compass', name: '指南针' },
          { class: 'fas fa-location-arrow', name: '定位' },
          { class: 'fas fa-traffic-light', name: '红绿灯' },
          { class: 'fas fa-gas-pump', name: '加油站' },
          { class: 'fas fa-parking', name: '停车场' }
        ],
        tech: [
          { class: 'fas fa-laptop', name: '笔记本' },
          { class: 'fas fa-desktop', name: '台式机' },
          { class: 'fas fa-tablet-alt', name: '平板' },
          { class: 'fas fa-mobile-alt', name: '手机' },
          { class: 'fas fa-keyboard', name: '键盘' },
          { class: 'fas fa-mouse', name: '鼠标' },
          { class: 'fas fa-headphones', name: '耳机' },
          { class: 'fas fa-microphone', name: '麦克风' },
          { class: 'fas fa-camera', name: '相机' },
          { class: 'fas fa-video', name: '视频' },
          { class: 'fas fa-tv', name: '电视' },
          { class: 'fas fa-radio', name: '收音机' },
          { class: 'fas fa-wifi', name: 'WiFi' },
          { class: 'fab fa-bluetooth', name: '蓝牙' },
          { class: 'fab fa-usb', name: 'USB' },
          { class: 'fas fa-plug', name: '插头' },
          { class: 'fas fa-battery-full', name: '满电' },
          { class: 'fas fa-battery-half', name: '半电' },
          { class: 'fas fa-battery-empty', name: '没电' },
          { class: 'fas fa-server', name: '服务器' },
          { class: 'fas fa-database', name: '数据库' },
          { class: 'fas fa-cloud', name: '云' },
          { class: 'fas fa-cloud-upload-alt', name: '云上传' },
          { class: 'fas fa-cloud-download-alt', name: '云下载' },
          { class: 'fas fa-network-wired', name: '网络' },
          { class: 'fas fa-ethernet', name: '以太网' },
          { class: 'fas fa-satellite', name: '卫星' },
          { class: 'fas fa-satellite-dish', name: '卫星天线' },
          { class: 'fas fa-broadcast-tower', name: '信号塔' },
          { class: 'fas fa-signal', name: '信号' },
          { class: 'fas fa-qrcode', name: '二维码' },
          { class: 'fas fa-barcode', name: '条形码' },
          { class: 'fas fa-fingerprint', name: '指纹' },
          { class: 'fas fa-shield-alt', name: '盾牌' },
          { class: 'fas fa-bug', name: '错误' },
          { class: 'fas fa-code', name: '代码' },
          { class: 'fas fa-terminal', name: '终端' }
        ],
        other: [
          { class: 'fas fa-book', name: '书籍' },
          { class: 'fas fa-newspaper', name: '报纸' },
          { class: 'fas fa-file', name: '文件' },
          { class: 'fas fa-folder', name: '文件夹' },
          { class: 'fas fa-envelope', name: '信封' },
          { class: 'fas fa-phone', name: '电话' },
          { class: 'fas fa-fax', name: '传真' },
          { class: 'fas fa-calendar', name: '日历' },
          { class: 'fas fa-clock', name: '时钟' },
          { class: 'fas fa-bell', name: '铃铛' },
          { class: 'fas fa-flag', name: '旗帜' },
          { class: 'fas fa-gift', name: '礼物' },
          { class: 'fas fa-shopping-cart', name: '购物车' },
          { class: 'fas fa-shopping-bag', name: '购物袋' },
          { class: 'fas fa-store', name: '商店' },
          { class: 'fas fa-warehouse', name: '仓库' },
          { class: 'fas fa-box', name: '箱子' },
          { class: 'fas fa-shipping-fast', name: '快递' },
          { class: 'fas fa-dolly', name: '推车' },
          { class: 'fas fa-weight', name: '重量' },
          { class: 'fas fa-balance-scale', name: '平衡' },
          { class: 'fas fa-ruler', name: '尺子' },
          { class: 'fas fa-thermometer', name: '温度计' },
          { class: 'fas fa-tachometer-alt', name: '仪表盘' },
          { class: 'fas fa-stopwatch', name: '秒表' },
          { class: 'fas fa-hourglass', name: '沙漏' },
          { class: 'fas fa-sun', name: '太阳' },
          { class: 'fas fa-moon', name: '月亮' },
          { class: 'fas fa-star', name: '星星' },
          { class: 'fas fa-cloud', name: '云朵' },
          { class: 'fas fa-umbrella', name: '雨伞' },
          { class: 'fas fa-snowflake', name: '雪花' },
          { class: 'fas fa-fire', name: '火焰' },
          { class: 'fas fa-leaf', name: '叶子' },
          { class: 'fas fa-tree', name: '树' },
          { class: 'fas fa-mountain', name: '山' },
          { class: 'fas fa-water', name: '水' },
          { class: 'fas fa-globe', name: '地球' },
          { class: 'fas fa-language', name: '语言' },
          { class: 'fas fa-music', name: '音乐' },
          { class: 'fas fa-volume-up', name: '音量' },
          { class: 'fas fa-play', name: '播放' },
          { class: 'fas fa-pause', name: '暂停' },
          { class: 'fas fa-stop', name: '停止' },
          { class: 'fas fa-forward', name: '快进' },
          { class: 'fas fa-backward', name: '后退' },
          { class: 'fas fa-random', name: '随机' },
          { class: 'fas fa-redo', name: '重复' },
          { class: 'fas fa-gamepad', name: '游戏手柄' },
          { class: 'fas fa-dice', name: '骰子' },
          { class: 'fas fa-chess', name: '国际象棋' },
          { class: 'fas fa-puzzle-piece', name: '拼图' },
          { class: 'fas fa-paint-brush', name: '画笔' },
          { class: 'fas fa-palette', name: '调色板' },
          { class: 'fas fa-camera', name: '复古相机' },
          { class: 'fas fa-film', name: '胶卷' },
          { class: 'fas fa-theater-masks', name: '戏剧面具' },
          { class: 'fas fa-magic', name: '魔法' },
          { class: 'fas fa-key', name: '钥匙' },
          { class: 'fas fa-hammer', name: '锤子' },
          { class: 'fas fa-wrench', name: '扳手' },
          { class: 'fas fa-tools', name: '工具' },
          { class: 'fas fa-hard-hat', name: '安全帽' },
          { class: 'fas fa-glasses', name: '眼镜' },
          { class: 'fas fa-crown', name: '王冠' },
          { class: 'fas fa-gem', name: '宝石' },
          { class: 'fas fa-ring', name: '戒指' },
          { class: 'fas fa-watch', name: '手表' }
        ]
      }
    };
  },
  computed: {
    filteredIcons() {
      let icons = this.iconCategories[this.activeTab] || [];
      
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        icons = icons.filter(icon => 
          icon.name.toLowerCase().includes(keyword) ||
          icon.class.toLowerCase().includes(keyword)
        );
      }
      
      return icons;
    }
  },
  watch: {
    value(newVal) {
      this.selectedIcon = newVal;
    }
  },
  methods: {
    selectIcon(iconClass) {
      this.selectedIcon = iconClass;
    },
    confirmSelect() {
      this.$emit('input', this.selectedIcon);
      this.showDialog = false;
    },
    cancelSelect() {
      this.selectedIcon = this.value;
      this.showDialog = false;
    },
    handleClose() {
      this.selectedIcon = this.value;
      this.showDialog = false;
    },
    handleTabClick() {
      // 切换标签页时重置搜索
      this.searchKeyword = '';
    }
  }
};
</script>

<style scoped>
.icon-select-container {
  width: 100%;
}

.icon-select-container .el-input {
  cursor: pointer;
}

.icon-select-container .el-input__inner {
  cursor: pointer;
}

.icon-dialog-content {
  max-height: 500px;
  overflow-y: auto;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
  padding: 10px 0;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fff;
  min-height: 80px;
}

.icon-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.icon-item.active {
  border-color: #409eff;
  background-color: #e6f7ff;
  color: #409eff;
}

.icon-item i {
  font-size: 24px;
  margin-bottom: 8px;
  color: inherit;
}

.icon-name {
  font-size: 12px;
  text-align: center;
  color: #666;
  line-height: 1.2;
  word-break: break-all;
}

.icon-item.active .icon-name {
  color: #409eff;
}

.no-icons {
  text-align: center;
  padding: 40px;
  color: #999;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 确保图标选择对话框在最上层 */
.icon-select-container .el-dialog__wrapper {
  z-index: 3000 !important;
}

.icon-select-container .el-dialog {
  z-index: 3001 !important;
}

.icon-select-container .el-overlay {
  z-index: 2999 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .icon-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
  }
  
  .icon-item {
    padding: 10px 5px;
    min-height: 60px;
  }
  
  .icon-item i {
    font-size: 20px;
    margin-bottom: 5px;
  }
  
  .icon-name {
    font-size: 10px;
  }
}

/* 图标选择弹出框样式 */
.icon-select-popover {
  max-width: 800px !important;
  padding: 20px !important;
}

.icon-select-popover .el-popover__title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
}
</style>
