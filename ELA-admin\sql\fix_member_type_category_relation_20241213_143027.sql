-- 修复成员类型与组织架构分类的关联关系
-- 创建时间: 2024-12-13 14:30:27

-- 第一步：为 member_types 表添加 category_id 字段
ALTER TABLE member_types ADD COLUMN category_id BIGINT COMMENT '组织架构分类ID';

-- 第二步：添加外键约束
ALTER TABLE member_types ADD CONSTRAINT fk_member_type_category 
FOREIGN KEY (category_id) REFERENCES organization_categories(id);

-- 第三步：添加索引
ALTER TABLE member_types ADD INDEX idx_category_id (category_id);

-- 第四步：删除现有的通用成员类型数据
DELETE FROM member_types;

-- 第五步：为每个组织架构分类插入对应的成员类型
-- 假设分类ID：1=理事会, 2=顾问团, 3=青年委员会（请根据实际数据调整）

-- 理事会成员类型
INSERT INTO member_types (category_id, type_code, name_zh, name_en, description_zh, description_en, sort_order, status, create_by, remark) VALUES
(1, 'president', '会长', 'President', '理事会会长', 'Board President', 1, '0', 'admin', '理事会最高领导职位'),
(1, 'vice_president', '副会长', 'Vice President', '理事会副会长', 'Board Vice President', 2, '0', 'admin', '理事会副领导职位'),
(1, 'secretary_general', '秘书长', 'Secretary General', '理事会秘书长', 'Board Secretary General', 3, '0', 'admin', '负责理事会日常事务'),
(1, 'treasurer', '司库', 'Treasurer', '理事会司库', 'Board Treasurer', 4, '0', 'admin', '负责理事会财务工作'),
(1, 'director', '理事', 'Director', '理事会成员', 'Board Director', 5, '0', 'admin', '理事会普通成员'),
(1, 'executive_director', '执行理事', 'Executive Director', '执行理事', 'Executive Director', 6, '0', 'admin', '执行层理事');

-- 顾问团成员类型  
INSERT INTO member_types (category_id, type_code, name_zh, name_en, description_zh, description_en, sort_order, status, create_by, remark) VALUES
(2, 'senior_advisor', '高级顾问', 'Senior Advisor', '高级顾问', 'Senior Advisor', 1, '0', 'admin', '资深顾问成员'),
(2, 'advisor', '顾问', 'Advisor', '顾问成员', 'Advisor Member', 2, '0', 'admin', '一般顾问成员'),
(2, 'honorary_advisor', '名誉顾问', 'Honorary Advisor', '名誉顾问', 'Honorary Advisor', 3, '0', 'admin', '名誉顾问职位'),
(2, 'founding_member', '创会成员', 'Founding Member', '创会成员', 'Founding Member', 4, '0', 'admin', '协会创始成员'),
(2, 'life_member', '永久会员', 'Life Member', '永久会员', 'Life Member', 5, '0', 'admin', '终身会员');

-- 青年委员会成员类型
INSERT INTO member_types (category_id, type_code, name_zh, name_en, description_zh, description_en, sort_order, status, create_by, remark) VALUES
(3, 'chairman', '主席', 'Chairman', '青年委员会主席', 'Youth Committee Chairman', 1, '0', 'admin', '青年委员会领导'),
(3, 'vice_chairman', '副主席', 'Vice Chairman', '青年委员会副主席', 'Youth Committee Vice Chairman', 2, '0', 'admin', '青年委员会副领导'),
(3, 'secretary', '秘书', 'Secretary', '青年委员会秘书', 'Youth Committee Secretary', 3, '0', 'admin', '负责委员会会务'),
(3, 'committee_member', '委员', 'Committee Member', '青年委员会委员', 'Youth Committee Member', 4, '0', 'admin', '委员会普通成员'),
(3, 'youth_representative', '青年代表', 'Youth Representative', '青年代表', 'Youth Representative', 5, '0', 'admin', '青年代表成员');

-- 第六步：更新现有的组织成员数据，将成员类型ID更新为对应分类下的成员类型ID
-- 注意：这一步需要根据实际数据情况手动调整，因为需要将原来的字符串类型转换为新的ID

-- 更新表注释
ALTER TABLE member_types COMMENT='成员类型表（按组织架构分类）';

-- 添加唯一索引，确保同一分类下类型代码不重复
ALTER TABLE member_types ADD UNIQUE INDEX uk_category_type_code (category_id, type_code); 