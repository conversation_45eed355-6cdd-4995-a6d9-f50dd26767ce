import request from '@/utils/request'

// 查询成员类型列表
export function listMemberType(query) {
  return request({
    url: '/system/organization/membertype/list',
    method: 'get',
    params: query
  })
}

// 查询成员类型详细
export function getMemberType(id) {
  return request({
    url: '/system/organization/membertype/' + id,
    method: 'get'
  })
}

// 新增成员类型
export function addMemberType(data) {
  return request({
    url: '/system/organization/membertype',
    method: 'post',
    data: data
  })
}

// 修改成员类型
export function updateMemberType(data) {
  return request({
    url: '/system/organization/membertype',
    method: 'put',
    data: data
  })
}

// 删除成员类型
export function delMemberType(id) {
  return request({
    url: '/system/organization/membertype/' + id,
    method: 'delete'
  })
}

// 获取成员类型选项列表
export function getMemberTypeOptions() {
  return request({
    url: '/system/organization/membertype/options',
    method: 'get'
  })
} 