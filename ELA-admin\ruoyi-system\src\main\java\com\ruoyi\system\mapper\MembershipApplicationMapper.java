package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.MembershipApplication;

/**
 * 会员申请表Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface MembershipApplicationMapper 
{
    /**
     * 查询会员申请表
     * 
     * @param id 会员申请表主键
     * @return 会员申请表
     */
    public MembershipApplication selectMembershipApplicationById(Long id);

    /**
     * 查询会员申请表列表
     * 
     * @param membershipApplication 会员申请表
     * @return 会员申请表集合
     */
    public List<MembershipApplication> selectMembershipApplicationList(MembershipApplication membershipApplication);

    /**
     * 新增会员申请表
     * 
     * @param membershipApplication 会员申请表
     * @return 结果
     */
    public int insertMembershipApplication(MembershipApplication membershipApplication);

    /**
     * 修改会员申请表
     * 
     * @param membershipApplication 会员申请表
     * @return 结果
     */
    public int updateMembershipApplication(MembershipApplication membershipApplication);

    /**
     * 删除会员申请表
     * 
     * @param id 会员申请表主键
     * @return 结果
     */
    public int deleteMembershipApplicationById(Long id);

    /**
     * 批量删除会员申请表
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMembershipApplicationByIds(Long[] ids);
} 