package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 联系消息对象 contact_messages
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ContactMessage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 消息ID */
    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;

    /** 主题 */
    @Excel(name = "主题")
    private String subject;

    /** 消息内容 */
    @Excel(name = "消息内容")
    private String message;

    /** 处理状态（0未处理 1已处理） */
    @Excel(name = "处理状态", readConverterExp = "0=未处理,1=已处理")
    private String status;

    /** 处理人 */
    @Excel(name = "处理人")
    private String processedBy;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date processedTime;

    /** 处理备注 */
    @Excel(name = "处理备注")
    private String processedNote;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setEmail(String email) 
    {
        this.email = email;
    }

    public String getEmail() 
    {
        return email;
    }
    public void setSubject(String subject) 
    {
        this.subject = subject;
    }

    public String getSubject() 
    {
        return subject;
    }
    public void setMessage(String message) 
    {
        this.message = message;
    }

    public String getMessage() 
    {
        return message;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setProcessedBy(String processedBy) 
    {
        this.processedBy = processedBy;
    }

    public String getProcessedBy() 
    {
        return processedBy;
    }
    public void setProcessedTime(Date processedTime) 
    {
        this.processedTime = processedTime;
    }

    public Date getProcessedTime() 
    {
        return processedTime;
    }
    public void setProcessedNote(String processedNote) 
    {
        this.processedNote = processedNote;
    }

    public String getProcessedNote() 
    {
        return processedNote;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("email", getEmail())
            .append("subject", getSubject())
            .append("message", getMessage())
            .append("status", getStatus())
            .append("processedBy", getProcessedBy())
            .append("processedTime", getProcessedTime())
            .append("processedNote", getProcessedNote())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("remark", getRemark())
            .toString();
    }
} 