package com.ruoyi.web.controller.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.MembershipType;
import com.ruoyi.system.service.IMembershipTypeService;

/**
 * 会员类型Controller
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
@RestController
@RequestMapping("/website/membership-types")
public class MembershipTypeController extends BaseController
{
    @Autowired
    private IMembershipTypeService membershipTypeService;

    /**
     * 查询会员类型列表
     */
    @PreAuthorize("@ss.hasPermi('website:membership-types:list')")
    @GetMapping("/list")
    public TableDataInfo list(MembershipType membershipType)
    {
        startPage();
        List<MembershipType> list = membershipTypeService.selectMembershipTypeList(membershipType);
        return getDataTable(list);
    }

    /**
     * 导出会员类型列表
     */
    @PreAuthorize("@ss.hasPermi('website:membership-types:export')")
    @Log(title = "会员类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MembershipType membershipType)
    {
        List<MembershipType> list = membershipTypeService.selectMembershipTypeList(membershipType);
        ExcelUtil<MembershipType> util = new ExcelUtil<MembershipType>(MembershipType.class);
        util.exportExcel(response, list, "会员类型数据");
    }

    /**
     * 获取会员类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:membership-types:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(membershipTypeService.selectMembershipTypeById(id));
    }

    /**
     * 新增会员类型
     */
    @PreAuthorize("@ss.hasPermi('website:membership-types:add')")
    @Log(title = "会员类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MembershipType membershipType)
    {
        membershipType.setCreateBy(getUsername());
        return toAjax(membershipTypeService.insertMembershipType(membershipType));
    }

    /**
     * 修改会员类型
     */
    @PreAuthorize("@ss.hasPermi('website:membership-types:edit')")
    @Log(title = "会员类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MembershipType membershipType)
    {
        membershipType.setUpdateBy(getUsername());
        return toAjax(membershipTypeService.updateMembershipType(membershipType));
    }

    /**
     * 删除会员类型
     */
    @PreAuthorize("@ss.hasPermi('website:membership-types:remove')")
    @Log(title = "会员类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(membershipTypeService.deleteMembershipTypeByIds(ids));
    }

    /**
     * 获取启用的会员类型列表(供前端使用)
     */
    @GetMapping("/public")
    public AjaxResult getPublicMembershipTypes()
    {
        MembershipType membershipType = new MembershipType();
        membershipType.setStatus("0"); // 只获取启用的会员类型
        List<MembershipType> list = membershipTypeService.selectMembershipTypeList(membershipType);
        return AjaxResult.success(list);
    }
} 