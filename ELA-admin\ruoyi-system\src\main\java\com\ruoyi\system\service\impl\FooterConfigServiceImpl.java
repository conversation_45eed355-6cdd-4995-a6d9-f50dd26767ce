package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.FooterConfigMapper;
import com.ruoyi.system.domain.FooterConfig;
import com.ruoyi.system.service.IFooterConfigService;

/**
 * 页脚配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class FooterConfigServiceImpl implements IFooterConfigService 
{
    @Autowired
    private FooterConfigMapper footerConfigMapper;

    /**
     * 查询页脚配置
     * 
     * @param id 页脚配置主键
     * @return 页脚配置
     */
    @Override
    public FooterConfig selectFooterConfigById(Long id)
    {
        return footerConfigMapper.selectFooterConfigById(id);
    }

    /**
     * 查询页脚配置列表
     * 
     * @param footerConfig 页脚配置
     * @return 页脚配置
     */
    @Override
    public List<FooterConfig> selectFooterConfigList(FooterConfig footerConfig)
    {
        return footerConfigMapper.selectFooterConfigList(footerConfig);
    }

    /**
     * 新增页脚配置
     * 
     * @param footerConfig 页脚配置
     * @return 结果
     */
    @Override
    public int insertFooterConfig(FooterConfig footerConfig)
    {
        return footerConfigMapper.insertFooterConfig(footerConfig);
    }

    /**
     * 修改页脚配置
     * 
     * @param footerConfig 页脚配置
     * @return 结果
     */
    @Override
    public int updateFooterConfig(FooterConfig footerConfig)
    {
        return footerConfigMapper.updateFooterConfig(footerConfig);
    }

    /**
     * 批量删除页脚配置
     * 
     * @param ids 需要删除的页脚配置主键
     * @return 结果
     */
    @Override
    public int deleteFooterConfigByIds(Long[] ids)
    {
        return footerConfigMapper.deleteFooterConfigByIds(ids);
    }

    /**
     * 删除页脚配置信息
     * 
     * @param id 页脚配置主键
     * @return 结果
     */
    @Override
    public int deleteFooterConfigById(Long id)
    {
        return footerConfigMapper.deleteFooterConfigById(id);
    }
} 