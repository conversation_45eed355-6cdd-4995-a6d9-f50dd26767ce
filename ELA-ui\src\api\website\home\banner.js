import request from '@/utils/request'

// 查询首页Banner列表
export function listHomeBanner(query) {
  return request({
    url: '/website/home/<USER>/list',
    method: 'get',
    params: query
  })
}

// 查询首页Banner详细
export function getHomeBanner(id) {
  return request({
    url: '/website/home/<USER>/' + id,
    method: 'get'
  })
}

// 新增首页Banner
export function addHomeBanner(data) {
  return request({
    url: '/website/home/<USER>',
    method: 'post',
    data: data
  })
}

// 修改首页Banner
export function updateHomeBanner(data) {
  return request({
    url: '/website/home/<USER>',
    method: 'put',
    data: data
  })
}

// 删除首页Banner
export function delHomeBanner(id) {
  return request({
    url: '/website/home/<USER>/' + id,
    method: 'delete'
  })
} 