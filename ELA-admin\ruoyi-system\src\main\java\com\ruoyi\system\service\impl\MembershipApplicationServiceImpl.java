package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.MembershipApplicationMapper;
import com.ruoyi.system.domain.MembershipApplication;
import com.ruoyi.system.service.IMembershipApplicationService;

/**
 * 会员申请表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MembershipApplicationServiceImpl implements IMembershipApplicationService 
{
    @Autowired
    private MembershipApplicationMapper membershipApplicationMapper;

    /**
     * 查询会员申请表
     * 
     * @param id 会员申请表主键
     * @return 会员申请表
     */
    @Override
    public MembershipApplication selectMembershipApplicationById(Long id)
    {
        return membershipApplicationMapper.selectMembershipApplicationById(id);
    }

    /**
     * 查询会员申请表列表
     * 
     * @param membershipApplication 会员申请表
     * @return 会员申请表
     */
    @Override
    public List<MembershipApplication> selectMembershipApplicationList(MembershipApplication membershipApplication)
    {
        return membershipApplicationMapper.selectMembershipApplicationList(membershipApplication);
    }

    /**
     * 新增会员申请表
     * 
     * @param membershipApplication 会员申请表
     * @return 结果
     */
    @Override
    public int insertMembershipApplication(MembershipApplication membershipApplication)
    {
        return membershipApplicationMapper.insertMembershipApplication(membershipApplication);
    }

    /**
     * 修改会员申请表
     * 
     * @param membershipApplication 会员申请表
     * @return 结果
     */
    @Override
    public int updateMembershipApplication(MembershipApplication membershipApplication)
    {
        return membershipApplicationMapper.updateMembershipApplication(membershipApplication);
    }

    /**
     * 批量删除会员申请表
     * 
     * @param ids 需要删除的会员申请表主键
     * @return 结果
     */
    @Override
    public int deleteMembershipApplicationByIds(Long[] ids)
    {
        return membershipApplicationMapper.deleteMembershipApplicationByIds(ids);
    }

    /**
     * 删除会员申请表信息
     * 
     * @param id 会员申请表主键
     * @return 结果
     */
    @Override
    public int deleteMembershipApplicationById(Long id)
    {
        return membershipApplicationMapper.deleteMembershipApplicationById(id);
    }
} 