<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="类型" prop="type">
        <el-input
          v-model="queryParams.type"
          placeholder="请输入类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名中文" prop="nameZh">
        <el-input
          v-model="queryParams.nameZh"
          placeholder="请输入姓名中文"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="内容状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:leadershipMessage:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:leadershipMessage:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:leadershipMessage:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="leadershipMessageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="类型" align="center" prop="type" width="100" :show-overflow-tooltip="true" />
      <el-table-column label="姓名中文" align="center" prop="nameZh" :show-overflow-tooltip="true" />
      <el-table-column label="姓名英文" align="center" prop="nameEn" :show-overflow-tooltip="true" />
      <el-table-column label="职位中文" align="center" prop="positionZh" :show-overflow-tooltip="true" />
      <el-table-column label="公司中文" align="center" prop="companyZh" :show-overflow-tooltip="true" />
      <el-table-column label="头像" align="center" prop="imageUrl" width="80">
        <template slot-scope="scope">
          <image-preview v-if="scope.row.imageUrl" :src="scope.row.imageUrl" :width="50" :height="50"/>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" width="80" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:leadershipMessage:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:leadershipMessage:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改领导留言对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane label="基本信息" name="basic">
            <el-row>
              <el-col :span="12">
                <el-form-item label="类型" prop="type">
                  <el-input v-model="form.type" placeholder="请输入类型（如：名誉顾问、会长、副会长等）" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="排序" prop="sortOrder">
                  <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="标题中文" prop="titleZh">
                  <el-input v-model="form.titleZh" placeholder="请输入标题中文" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="标题英文" prop="titleEn">
                  <el-input v-model="form.titleEn" placeholder="请输入标题英文" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="姓名中文" prop="nameZh">
                  <el-input v-model="form.nameZh" placeholder="请输入姓名中文" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="姓名英文" prop="nameEn">
                  <el-input v-model="form.nameEn" placeholder="请输入姓名英文" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="职位中文" prop="positionZh">
                  <el-input v-model="form.positionZh" placeholder="请输入职位中文" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="职位英文" prop="positionEn">
                  <el-input v-model="form.positionEn" placeholder="请输入职位英文" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="公司中文" prop="companyZh">
                  <el-input v-model="form.companyZh" placeholder="请输入公司中文" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="公司英文" prop="companyEn">
                  <el-input v-model="form.companyEn" placeholder="请输入公司英文" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-radio-group v-model="form.status">
                    <el-radio
                      v-for="dict in dict.type.sys_normal_disable"
                      :key="dict.value"
                      :label="dict.value"
                    >{{dict.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          
          <el-tab-pane label="留言内容" name="message">
            <el-form-item label="留言内容中文" prop="messageZh">
              <el-input v-model="form.messageZh" type="textarea" :rows="8" placeholder="请输入留言内容中文" />
            </el-form-item>
            <el-form-item label="留言内容英文" prop="messageEn">
              <el-input v-model="form.messageEn" type="textarea" :rows="8" placeholder="请输入留言内容英文" />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="图片设置" name="images">
            <el-form-item label="头像图片" prop="imageUrl">
              <image-upload v-model="form.imageUrl" :limit="1"/>
              <div style="margin-top: 10px; color: #909399; font-size: 12px;">
                建议上传正方形头像，支持jpg、png格式
              </div>
            </el-form-item>
            <el-form-item label="背景图片" prop="backgroundImageUrl">
              <image-upload v-model="form.backgroundImageUrl" :limit="1"/>
              <div style="margin-top: 10px; color: #909399; font-size: 12px;">
                可选，如不设置将使用默认背景
              </div>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLeadershipMessage, getLeadershipMessage, delLeadershipMessage, addLeadershipMessage, updateLeadershipMessage } from "@/api/website/leadershipMessage";

export default {
  name: "LeadershipMessage",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 领导留言表格数据
      leadershipMessageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前选中的标签页
      activeTab: "basic",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: null,
        nameZh: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        type: [
          { required: true, message: "类型不能为空", trigger: "change" }
        ],
        titleZh: [
          { required: true, message: "标题中文不能为空", trigger: "blur" }
        ],
        titleEn: [
          { required: true, message: "标题英文不能为空", trigger: "blur" }
        ],
        nameZh: [
          { required: true, message: "姓名中文不能为空", trigger: "blur" }
        ],
        nameEn: [
          { required: true, message: "姓名英文不能为空", trigger: "blur" }
        ],
        positionZh: [
          { required: true, message: "职位中文不能为空", trigger: "blur" }
        ],
        positionEn: [
          { required: true, message: "职位英文不能为空", trigger: "blur" }
        ],
        companyZh: [
          { required: true, message: "公司中文不能为空", trigger: "blur" }
        ],
        companyEn: [
          { required: true, message: "公司英文不能为空", trigger: "blur" }
        ],
        messageZh: [
          { required: true, message: "留言内容中文不能为空", trigger: "blur" }
        ],
        messageEn: [
          { required: true, message: "留言内容英文不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
        sortOrder: [
          { required: true, message: "排序不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询领导留言列表 */
    getList() {
      this.loading = true;
      listLeadershipMessage(this.queryParams).then(response => {
        this.leadershipMessageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        type: null,
        titleZh: null,
        titleEn: null,
        nameZh: null,
        nameEn: null,
        positionZh: null,
        positionEn: null,
        companyZh: null,
        companyEn: null,
        messageZh: null,
        messageEn: null,
        imageUrl: null,
        backgroundImageUrl: null,
        sortOrder: 0,
        status: "0"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加领导留言";
      this.activeTab = "basic";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getLeadershipMessage(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改领导留言";
        this.activeTab = "basic";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLeadershipMessage(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLeadershipMessage(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除领导留言编号为"' + ids + '"的数据项？').then(function() {
        return delLeadershipMessage(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script> 