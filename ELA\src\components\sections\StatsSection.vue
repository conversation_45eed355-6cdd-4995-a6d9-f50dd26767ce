<template>
  <section class="stats-section section">
    <div class="container">
      <div class="section-title" v-if="section.config.title || section.config.subtitle">
        <h2 v-if="section.config.title">{{ section.config.title }}</h2>
        <p v-if="section.config.subtitle">{{ section.config.subtitle }}</p>
      </div>
      
      <div class="stats-grid">
        <div 
          v-for="(stat, index) in section.config.stats" 
          :key="stat.id"
          class="stat-item"
        >
          <div class="stat-icon" v-if="stat.icon">
            <i :class="stat.icon"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ animatedNumbers[index] }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'StatsSection',
  props: {
    section: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      animatedNumbers: this.section.config.stats.map(() => 0)
    }
  },
  watch: {
    section: {
      handler(newVal) {
        if (newVal.config.stats) {
          this.animateStats()
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    animateStats() {
      const animationDuration = 2000 // 2 seconds
      this.section.config.stats.forEach((stat, index) => {
        const targetValue = parseFloat(stat.number.replace(/[+,]/g, ''))
        if (isNaN(targetValue)) {
            this.animatedNumbers.splice(index, 1, stat.number)
            return
        }

        let startTimestamp = null
        const step = (timestamp) => {
          if (!startTimestamp) startTimestamp = timestamp
          const progress = Math.min((timestamp - startTimestamp) / animationDuration, 1)
          const currentValue = Math.floor(progress * targetValue)
          
          this.animatedNumbers.splice(index, 1, currentValue)
          
          if (progress < 1) {
            window.requestAnimationFrame(step)
          } else {
            // Ensure the final number includes suffixes like '+'
            this.animatedNumbers.splice(index, 1, stat.number)
          }
        }
        window.requestAnimationFrame(step)
      })
    }
  }
}
</script>

<style scoped>
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 30px;
}

.stat-item {
  background: rgba(18, 18, 36, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease-out;
  backdrop-filter: blur(5px);
}

.stat-item:hover {
  transform: translateY(-8px);
  background: rgba(18, 18, 36, 0.9);
  border-color: var(--primary-color);
  box-shadow: 0 10px 30px rgba(0, 240, 255, 0.15);
}

.stat-icon {
  font-size: 36px;
  color: var(--primary-color);
  text-shadow: 0 0 15px var(--primary-color);
  flex-shrink: 0;
}

.stat-number {
  font-size: 36px;
  font-weight: 700;
  color: var(--text-heading);
  line-height: 1.2;
}

.stat-label {
  font-size: 15px;
  color: var(--text-dark);
  font-weight: 400;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  .stat-item {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>