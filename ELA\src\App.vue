<template>
  <div id="app">
    <Header @language-changed="handleLanguageChange" />
    <main class="main-content">
      <router-view :current-lang="currentLang"/>
    </main>
    <Footer />
  </div>
</template>

<script>
import Header from './components/Header.vue'
import Footer from './components/Footer.vue'

export default {
  name: 'App',
  components: {
    Header,
    Footer
  },
  data() {
    return {
      currentLang: 'zh'
    }
  },
  mounted() {
    // 从localStorage恢复语言设置
    const savedLang = localStorage.getItem('language')
    if (savedLang) {
      this.currentLang = savedLang
    }
  },
  methods: {
    handleLanguageChange(lang) {
      this.currentLang = lang
      // 可以在这里添加其他全局语言切换的逻辑
      // 例如：更新页面标题、meta标签等
      this.updatePageLanguage(lang)
    },
    updatePageLanguage(lang) {
      // 更新HTML lang属性
      document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en'
      
      // 更新页面标题
      if (lang === 'zh') {
        document.title = 'HKELA - 香港电子商务物流协会'
      } else {
        document.title = 'HKELA - Hong Kong E-commerce Logistics Association'
      }
    }
  }
}
</script>

<style>
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #0056b3;
  --text-primary: #333;
  --text-secondary: #666;
  --bg-light-gray: #f8f9fa;
  --border-color: #dee2e6;
  --font-family-base: 'Noto Sans SC', 'Microsoft YaHei', 'PingFang SC', sans-serif;
}

body {
  font-family: var(--font-family-base);
  line-height: 1.7;
  color: var(--text-primary);
  background-color: #fff;
  font-size: 15px;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  width: 100%;
}

a {
  text-decoration: none;
  color: var(--primary-color);
  transition: color 0.3s ease;
}

a:hover {
  color: #00418a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
}
</style>
