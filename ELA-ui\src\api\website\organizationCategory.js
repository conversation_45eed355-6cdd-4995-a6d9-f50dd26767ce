import request from '@/utils/request'

// 查询组织架构分类列表
export function listOrganizationCategory(query) {
  return request({
    url: '/system/organization/category/list',
    method: 'get',
    params: query
  })
}

// 查询组织架构分类详细
export function getOrganizationCategory(id) {
  return request({
    url: '/system/organization/category/' + id,
    method: 'get'
  })
}

// 新增组织架构分类
export function addOrganizationCategory(data) {
  return request({
    url: '/system/organization/category',
    method: 'post',
    data: data
  })
}

// 修改组织架构分类
export function updateOrganizationCategory(data) {
  return request({
    url: '/system/organization/category',
    method: 'put',
    data: data
  })
}

// 删除组织架构分类
export function delOrganizationCategory(id) {
  return request({
    url: '/system/organization/category/' + id,
    method: 'delete'
  })
} 