package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * 申请条件对象 application_requirements
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
public class ApplicationRequirement extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 中文标题 */
    @Excel(name = "中文标题")
    private String titleZh;

    /** 英文标题 */
    @Excel(name = "英文标题")
    private String titleEn;

    /** 中文条件列表 */
    @Excel(name = "中文条件列表")
    private List<String> requirementsZh = new ArrayList<>();

    /** 英文条件列表 */
    @Excel(name = "英文条件列表")
    private List<String> requirementsEn = new ArrayList<>();

    /** 排序字段 */
    @Excel(name = "排序字段")
    private Integer sortOrder;

    /** 状态：0-禁用，1-启用 */
    @Excel(name = "状态：0-禁用，1-启用")
    private Integer status;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setTitleZh(String titleZh) 
    {
        this.titleZh = titleZh;
    }

    public String getTitleZh() 
    {
        return titleZh;
    }

    public void setTitleEn(String titleEn) 
    {
        this.titleEn = titleEn;
    }

    public String getTitleEn() 
    {
        return titleEn;
    }

    public void setRequirementsZh(List<String> requirementsZh) 
    {
        this.requirementsZh = requirementsZh;
    }

    public List<String> getRequirementsZh() 
    {
        return requirementsZh;
    }

    // 处理JSON字符串输入
    @JsonSetter("requirementsZh")
    public void setRequirementsZhFromString(Object requirementsZh) {
        if (requirementsZh instanceof String) {
            try {
                this.requirementsZh = objectMapper.readValue((String) requirementsZh, new TypeReference<List<String>>() {});
            } catch (JsonProcessingException e) {
                this.requirementsZh = new ArrayList<>();
            }
        } else if (requirementsZh instanceof List) {
            this.requirementsZh = (List<String>) requirementsZh;
        }
    }

    public void setRequirementsEn(List<String> requirementsEn) 
    {
        this.requirementsEn = requirementsEn;
    }

    public List<String> getRequirementsEn() 
    {
        return requirementsEn;
    }

    // 处理JSON字符串输入
    @JsonSetter("requirementsEn")
    public void setRequirementsEnFromString(Object requirementsEn) {
        if (requirementsEn instanceof String) {
            try {
                this.requirementsEn = objectMapper.readValue((String) requirementsEn, new TypeReference<List<String>>() {});
            } catch (JsonProcessingException e) {
                this.requirementsEn = new ArrayList<>();
            }
        } else if (requirementsEn instanceof List) {
            this.requirementsEn = (List<String>) requirementsEn;
        }
    }

    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("titleZh", getTitleZh())
            .append("titleEn", getTitleEn())
            .append("requirementsZh", getRequirementsZh())
            .append("requirementsEn", getRequirementsEn())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 