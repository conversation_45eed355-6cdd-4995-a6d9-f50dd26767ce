<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.WebsiteBannerMapper">
    
    <resultMap type="WebsiteBanner" id="WebsiteBannerResult">
        <result property="id"    column="id"    />
        <result property="titleZh"    column="title_zh"    />
        <result property="titleEn"    column="title_en"    />
        <result property="subtitleZh"    column="subtitle_zh"    />
        <result property="subtitleEn"    column="subtitle_en"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="linkUrl"    column="link_url"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="showButton"    column="show_button"    />
        <result property="buttonTextZh"    column="button_text_zh"    />
        <result property="buttonTextEn"    column="button_text_en"    />
        <result property="buttonLink"    column="button_link"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectWebsiteBannerVo">
        select id, title_zh, title_en, subtitle_zh, subtitle_en, image_url, link_url, sort_order, status, show_button, button_text_zh, button_text_en, button_link, create_by, create_time, update_by, update_time, remark from website_banner
    </sql>

    <select id="selectWebsiteBannerList" parameterType="WebsiteBanner" resultMap="WebsiteBannerResult">
        <include refid="selectWebsiteBannerVo"/>
        <where>  
            <if test="titleZh != null  and titleZh != ''"> and title_zh like concat('%', #{titleZh}, '%')</if>
            <if test="titleEn != null  and titleEn != ''"> and title_en like concat('%', #{titleEn}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectEnabledWebsiteBannerList" resultMap="WebsiteBannerResult">
        <include refid="selectWebsiteBannerVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectWebsiteBannerById" parameterType="Long" resultMap="WebsiteBannerResult">
        <include refid="selectWebsiteBannerVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertWebsiteBanner" parameterType="WebsiteBanner" useGeneratedKeys="true" keyProperty="id">
        insert into website_banner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="titleZh != null">title_zh,</if>
            <if test="titleEn != null">title_en,</if>
            <if test="subtitleZh != null">subtitle_zh,</if>
            <if test="subtitleEn != null">subtitle_en,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="linkUrl != null">link_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="showButton != null">show_button,</if>
            <if test="buttonTextZh != null">button_text_zh,</if>
            <if test="buttonTextEn != null">button_text_en,</if>
            <if test="buttonLink != null">button_link,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="titleZh != null">#{titleZh},</if>
            <if test="titleEn != null">#{titleEn},</if>
            <if test="subtitleZh != null">#{subtitleZh},</if>
            <if test="subtitleEn != null">#{subtitleEn},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="linkUrl != null">#{linkUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="showButton != null">#{showButton},</if>
            <if test="buttonTextZh != null">#{buttonTextZh},</if>
            <if test="buttonTextEn != null">#{buttonTextEn},</if>
            <if test="buttonLink != null">#{buttonLink},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateWebsiteBanner" parameterType="WebsiteBanner">
        update website_banner
        <trim prefix="SET" suffixOverrides=",">
            <if test="titleZh != null">title_zh = #{titleZh},</if>
            <if test="titleEn != null">title_en = #{titleEn},</if>
            <if test="subtitleZh != null">subtitle_zh = #{subtitleZh},</if>
            <if test="subtitleEn != null">subtitle_en = #{subtitleEn},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="linkUrl != null">link_url = #{linkUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="showButton != null">show_button = #{showButton},</if>
            <if test="buttonTextZh != null">button_text_zh = #{buttonTextZh},</if>
            <if test="buttonTextEn != null">button_text_en = #{buttonTextEn},</if>
            <if test="buttonLink != null">button_link = #{buttonLink},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWebsiteBannerById" parameterType="Long">
        delete from website_banner where id = #{id}
    </delete>

    <delete id="deleteWebsiteBannerByIds" parameterType="String">
        delete from website_banner where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 