package com.ruoyi.system.domain;

import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 组织架构分类对象 organization_categories
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class OrganizationCategory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 分类名称（中文） */
    @Excel(name = "分类名称（中文）")
    private String nameZh;

    /** 分类名称（英文） */
    @Excel(name = "分类名称（英文）")
    private String nameEn;

    /** 描述（中文） */
    @Excel(name = "描述（中文）")
    private String descriptionZh;

    /** 描述（英文） */
    @Excel(name = "描述（英文）")
    private String descriptionEn;

    /** 页面标题（中文） */
    @Excel(name = "页面标题（中文）")
    private String heroTitleZh;

    /** 页面标题（英文） */
    @Excel(name = "页面标题（英文）")
    private String heroTitleEn;

    /** 页面副标题（中文） */
    @Excel(name = "页面副标题（中文）")
    private String heroSubtitleZh;

    /** 页面副标题（英文） */
    @Excel(name = "页面副标题（英文）")
    private String heroSubtitleEn;

    /** 页面头图URL */
    @Excel(name = "页面头图URL")
    private String heroImageUrl;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 关联的成员列表 */
    private List<OrganizationMember> members;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setNameZh(String nameZh) 
    {
        this.nameZh = nameZh;
    }

    public String getNameZh() 
    {
        return nameZh;
    }

    public void setNameEn(String nameEn) 
    {
        this.nameEn = nameEn;
    }

    public String getNameEn() 
    {
        return nameEn;
    }

    public void setDescriptionZh(String descriptionZh) 
    {
        this.descriptionZh = descriptionZh;
    }

    public String getDescriptionZh() 
    {
        return descriptionZh;
    }

    public void setDescriptionEn(String descriptionEn) 
    {
        this.descriptionEn = descriptionEn;
    }

    public String getDescriptionEn() 
    {
        return descriptionEn;
    }

    public void setHeroTitleZh(String heroTitleZh) 
    {
        this.heroTitleZh = heroTitleZh;
    }

    public String getHeroTitleZh() 
    {
        return heroTitleZh;
    }

    public void setHeroTitleEn(String heroTitleEn) 
    {
        this.heroTitleEn = heroTitleEn;
    }

    public String getHeroTitleEn() 
    {
        return heroTitleEn;
    }

    public void setHeroSubtitleZh(String heroSubtitleZh) 
    {
        this.heroSubtitleZh = heroSubtitleZh;
    }

    public String getHeroSubtitleZh() 
    {
        return heroSubtitleZh;
    }

    public void setHeroSubtitleEn(String heroSubtitleEn) 
    {
        this.heroSubtitleEn = heroSubtitleEn;
    }

    public String getHeroSubtitleEn() 
    {
        return heroSubtitleEn;
    }

    public void setHeroImageUrl(String heroImageUrl) 
    {
        this.heroImageUrl = heroImageUrl;
    }

    public String getHeroImageUrl() 
    {
        return heroImageUrl;
    }

    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public List<OrganizationMember> getMembers() 
    {
        return members;
    }

    public void setMembers(List<OrganizationMember> members) 
    {
        this.members = members;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("nameZh", getNameZh())
            .append("nameEn", getNameEn())
            .append("descriptionZh", getDescriptionZh())
            .append("descriptionEn", getDescriptionEn())
            .append("heroTitleZh", getHeroTitleZh())
            .append("heroTitleEn", getHeroTitleEn())
            .append("heroSubtitleZh", getHeroSubtitleZh())
            .append("heroSubtitleEn", getHeroSubtitleEn())
            .append("heroImageUrl", getHeroImageUrl())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 