package com.ruoyi.web.controller.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.WebsiteBanner;
import com.ruoyi.system.service.IWebsiteBannerService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 网站BannerController
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/website/banner")
public class WebsiteBannerController extends BaseController
{
    @Autowired
    private IWebsiteBannerService websiteBannerService;

    /**
     * 查询网站Banner列表
     */
    @PreAuthorize("@ss.hasPermi('website:banner:list')")
    @GetMapping("/list")
    public TableDataInfo list(WebsiteBanner websiteBanner)
    {
        startPage();
        List<WebsiteBanner> list = websiteBannerService.selectWebsiteBannerList(websiteBanner);
        return getDataTable(list);
    }

    /**
     * 导出网站Banner列表
     */
    @PreAuthorize("@ss.hasPermi('website:banner:export')")
    @Log(title = "网站Banner", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WebsiteBanner websiteBanner)
    {
        List<WebsiteBanner> list = websiteBannerService.selectWebsiteBannerList(websiteBanner);
        ExcelUtil<WebsiteBanner> util = new ExcelUtil<WebsiteBanner>(WebsiteBanner.class);
        util.exportExcel(response, list, "网站Banner数据");
    }

    /**
     * 获取网站Banner详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:banner:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(websiteBannerService.selectWebsiteBannerById(id));
    }

    /**
     * 新增网站Banner
     */
    @PreAuthorize("@ss.hasPermi('website:banner:add')")
    @Log(title = "网站Banner", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WebsiteBanner websiteBanner)
    {
        return toAjax(websiteBannerService.insertWebsiteBanner(websiteBanner));
    }

    /**
     * 修改网站Banner
     */
    @PreAuthorize("@ss.hasPermi('website:banner:edit')")
    @Log(title = "网站Banner", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WebsiteBanner websiteBanner)
    {
        return toAjax(websiteBannerService.updateWebsiteBanner(websiteBanner));
    }

    /**
     * 删除网站Banner
     */
    @PreAuthorize("@ss.hasPermi('website:banner:remove')")
    @Log(title = "网站Banner", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(websiteBannerService.deleteWebsiteBannerByIds(ids));
    }
} 