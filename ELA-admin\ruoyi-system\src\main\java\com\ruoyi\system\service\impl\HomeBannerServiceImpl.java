package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.HomeBannerMapper;
import com.ruoyi.system.domain.HomeBanner;
import com.ruoyi.system.service.IHomeBannerService;

/**
 * 首页BannerService业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class HomeBannerServiceImpl implements IHomeBannerService 
{
    @Autowired
    private HomeBannerMapper homeBannerMapper;

    /**
     * 查询首页Banner
     * 
     * @param id 首页Banner主键
     * @return 首页Banner
     */
    @Override
    public HomeBanner selectHomeBannerById(Long id)
    {
        return homeBannerMapper.selectHomeBannerById(id);
    }

    /**
     * 查询首页Banner列表
     * 
     * @param homeBanner 首页Banner
     * @return 首页Banner
     */
    @Override
    public List<HomeBanner> selectHomeBannerList(HomeBanner homeBanner)
    {
        return homeBannerMapper.selectHomeBannerList(homeBanner);
    }

    /**
     * 查询启用的首页Banner列表（用于前端显示）
     * 
     * @return 首页Banner集合
     */
    @Override
    public List<HomeBanner> selectEnabledHomeBannerList()
    {
        return homeBannerMapper.selectEnabledHomeBannerList();
    }

    /**
     * 新增首页Banner
     * 
     * @param homeBanner 首页Banner
     * @return 结果
     */
    @Override
    public int insertHomeBanner(HomeBanner homeBanner)
    {
        homeBanner.setCreateTime(DateUtils.getNowDate());
        return homeBannerMapper.insertHomeBanner(homeBanner);
    }

    /**
     * 修改首页Banner
     * 
     * @param homeBanner 首页Banner
     * @return 结果
     */
    @Override
    public int updateHomeBanner(HomeBanner homeBanner)
    {
        homeBanner.setUpdateTime(DateUtils.getNowDate());
        return homeBannerMapper.updateHomeBanner(homeBanner);
    }

    /**
     * 批量删除首页Banner
     * 
     * @param ids 需要删除的首页Banner主键
     * @return 结果
     */
    @Override
    public int deleteHomeBannerByIds(Long[] ids)
    {
        return homeBannerMapper.deleteHomeBannerByIds(ids);
    }

    /**
     * 删除首页Banner信息
     * 
     * @param id 首页Banner主键
     * @return 结果
     */
    @Override
    public int deleteHomeBannerById(Long id)
    {
        return homeBannerMapper.deleteHomeBannerById(id);
    }
} 