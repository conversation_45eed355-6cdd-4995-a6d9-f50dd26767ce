<template>
  <div class="join-us-page">
    <!-- Hero Banner -->
    <section class="hero-banner">
      <div class="hero-overlay"></div>
      <div class="hero-content">
        <div class="container">
          <h1>{{ currentLang === 'zh' ? '加入我們' : 'Join Us' }}</h1>
          <p class="hero-subtitle">
            {{ currentLang === 'zh'
              ? '成為香港電商物流協會會員，與業界精英共同推動行業發展，分享資源與機遇'
              : 'Become a member of HKELA, collaborate with industry elites to promote sector development, and share resources and opportunities'
            }}
          </p>
        </div>
      </div>
    </section>

    <div class="main-container">
      <!-- Membership Types Section -->
      <section class="section" v-if="!loading && membershipTypes.length > 0">
        <div class="section-header">
          <div class="section-number">{{ getSectionNumber('membershipTypes') }}</div>
          <h2>{{ currentLang === 'zh' ? '會員類型' : 'Membership Types' }}</h2>
        </div>

        <div class="membership-grid" v-loading="loading">
          <div
            v-for="membershipType in membershipTypes"
            :key="membershipType.id"
            class="membership-card"
            :class="membershipType.typeCode"
          >
            <div class="membership-badge">{{ currentLang === 'zh' ? membershipType.nameZh : membershipType.nameEn }}</div>
            <div class="membership-icon">
              <i :class="membershipType.icon"></i>
            </div>
            <div class="membership-info">
              <h3>{{ currentLang === 'zh' ? membershipType.nameZh : membershipType.nameEn }}</h3>
              <p class="membership-description">
                {{ currentLang === 'zh' ? membershipType.descriptionZh : membershipType.descriptionEn }}
              </p>
              <div class="membership-fee">
                <span class="currency">HKD</span>
                <span class="amount">{{ membershipType.annualFee }}</span>
                <span class="period">{{ currentLang === 'zh' ? '/年' : '/year' }}</span>
              </div>
              <ul class="membership-benefits">
                <li
                  v-for="(benefit, index) in (currentLang === 'zh' ? membershipType.benefitsZh : membershipType.benefitsEn)"
                  :key="index"
                >
                  {{ benefit }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <!-- Benefits Section -->
      <section class="section" v-if="!benefitsLoading && memberBenefits.length > 0">
        <div class="section-header">
          <div class="section-number">{{ getSectionNumber('memberBenefits') }}</div>
          <h2>{{ currentLang === 'zh' ? '會員權益' : 'Member Benefits' }}</h2>
        </div>

        <div class="benefits-grid" v-loading="benefitsLoading">
          <div
            v-for="benefit in memberBenefits"
            :key="benefit.id"
            class="benefit-card"
          >
            <div class="benefit-icon">
              <img :src="benefit.icon" :alt="currentLang === 'zh' ? benefit.titleZh : benefit.titleEn" v-if="benefit.icon" />
            </div>
            <h4>{{ currentLang === 'zh' ? benefit.titleZh : benefit.titleEn }}</h4>
            <p>{{ currentLang === 'zh' ? benefit.descriptionZh : benefit.descriptionEn }}</p>
          </div>
        </div>
      </section>

      <!-- Application Process Section -->
      <section class="section" v-if="!processLoading && applicationProcess.length > 0">
        <div class="section-header">
          <div class="section-number">{{ getSectionNumber('applicationProcess') }}</div>
          <h2>{{ currentLang === 'zh' ? '申請流程' : 'Application Process' }}</h2>
        </div>

        <div class="process-timeline" v-loading="processLoading">
          <div
            v-for="process in applicationProcess"
            :key="process.id"
            class="process-step"
          >
            <div class="step-number">{{ process.stepNumber }}</div>
            <div class="step-content">
              <h4>{{ currentLang === 'zh' ? process.titleZh : process.titleEn }}</h4>
              <p>{{ currentLang === 'zh' ? process.descriptionZh : process.descriptionEn }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- Association Benefits Section -->
      <section class="section">
        <div class="section-header">
          <div class="section-number">{{ getSectionNumber('associationBenefits') }}</div>
          <h2>{{ currentLang === 'zh' ? '加入協會的好處' : 'Benefits of Joining the Association' }}</h2>
        </div>

        <div class="association-benefits-content">
          <div class="benefits-text">
            <div class="benefits-section">
              <h3>{{ currentLang === 'zh' ? '加入協會的好處' : 'BENEFITS' }}</h3>
              <p v-if="currentLang === 'zh'">
                你期望在電子商務物流行業展開職業生涯嗎？或者想於新興網上市場中發揮專長嗎？香港電商物流協會致力提供一個讓這類會員聯繫和成長的社群。作為香港電子商貿物流協會會員，可參與本會的會員活動，包括交流、實地物流考察及市場調查。此外，會員並能參與提升個人發展的一系機會，包括會員指導計劃、管理培訓生計劃及會員推薦計劃。
              </p>
              <p v-else>
                Are you eager to advance your career in the logistics industry for E-commerce? Or do you want to exert your specialties in the emerging online market? HKELA aims to provide a community for members like you to connect and grow. As a HKELA member, you can participate in events including networking, logistics site tours and market researches. In addition, members are open to personal development opportunities including mentorship, management trainee programme, and referral programme.
              </p>

            </div>
          </div>
        </div>
      </section>

      <!-- Application Form Section -->
      <section class="section">
        <div class="section-header">
          <div class="section-number">{{ getSectionNumber('applicationForm') }}</div>
          <h2>{{ currentLang === 'zh' ? '會員申請表' : 'Membership Application Form' }}</h2>
        </div>

        <div class="application-form">
          <div class="form-content">
            <div class="form-container">

            <!-- Step 1: Personal Particulars -->
            <div v-if="currentStep === 1" class="form-step">
              <h3>{{ currentLang === 'zh' ? '個人資料' : 'Personal Particulars' }}</h3>
              
              <div class="form-row">
                <div class="form-group">
                  <label>{{ currentLang === 'zh' ? '會員類型' : 'Membership Type' }} *</label>
                  <select v-model="applicationForm.membershipType" required>
                    <option value="">{{ currentLang === 'zh' ? '請選擇會員類型' : 'Select Membership Type' }}</option>
                    <option
                      v-for="membershipType in membershipTypes"
                      :key="membershipType.id"
                      :value="membershipType.typeCode"
                    >
                      {{ currentLang === 'zh' ? membershipType.nameZh : membershipType.nameEn }}
                    </option>
                  </select>
                </div>
                <div class="form-group">
                  <label>{{ currentLang === 'zh' ? '稱謂' : 'Title' }} *</label>
                  <select v-model="applicationForm.title" required>
                    <option value="">{{ currentLang === 'zh' ? '請選擇' : 'Select' }}</option>
                    <option value="Dr.">Dr.</option>
                    <option value="Mr.">Mr.</option>
                    <option value="Mrs.">Mrs.</option>
                    <option value="Ms.">Ms.</option>
                  </select>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>{{ currentLang === 'zh' ? '英文姓氏' : 'Surname' }} *</label>
                  <input type="text" v-model="applicationForm.surnameEn" required />
                </div>
                <div class="form-group">
                  <label>{{ currentLang === 'zh' ? '英文名字' : 'Other Name' }} *</label>
                  <input type="text" v-model="applicationForm.otherNameEn" required />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>{{ currentLang === 'zh' ? '中文姓名' : 'Name in Chinese' }} *</label>
                  <input type="text" v-model="applicationForm.nameZh" required />
                </div>
                <div class="form-group">
                  <label>{{ currentLang === 'zh' ? '護照/身分證號碼' : 'Passport/I.D. No.' }} *</label>
                  <input type="text" v-model="applicationForm.passportId" required />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>{{ currentLang === 'zh' ? '出生日期' : 'Date of Birth' }} *</label>
                  <input type="date" v-model="applicationForm.birthDate" required />
                </div>
              </div>

              <h4>{{ currentLang === 'zh' ? '聯繫方式' : 'Contact Information' }}</h4>
              
              <div class="form-row">
                <div class="form-group full-width">
                  <label>{{ currentLang === 'zh' ? '通訊地址' : 'Correspondence Address' }} *</label>
                  <textarea v-model="applicationForm.correspondenceAddress" required rows="3"></textarea>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>{{ currentLang === 'zh' ? '電子郵箱' : 'Email Address' }} *</label>
                  <input type="email" v-model="applicationForm.email" required />
                </div>
                <div class="form-group">
                  <label>{{ currentLang === 'zh' ? '手提電話號碼' : 'Mobile' }} *</label>
                  <input type="tel" v-model="applicationForm.mobile" required />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>{{ currentLang === 'zh' ? '辦公室電話號碼' : 'Telephone No.: Workplace' }}</label>
                  <input type="tel" v-model="applicationForm.workplacePhone" />
                </div>
                <div class="form-group">
                  <label>{{ currentLang === 'zh' ? '家居電話號碼' : 'Telephone No.: Residence' }}</label>
                  <input type="tel" v-model="applicationForm.residencePhone" />
                </div>
              </div>
            </div>

            <!-- Step 2: Academic Qualifications -->
            <div v-if="currentStep === 2" class="form-step">
              <h3>{{ currentLang === 'zh' ? '學歷' : 'Academic Qualifications' }}</h3>
              
              <div class="qualification-section">
                <h4>{{ currentLang === 'zh' ? '學歷 1 (必填)' : 'Qualification 1 (Required)' }}</h4>
                <div class="form-row">
                  <div class="form-group">
                    <label>{{ currentLang === 'zh' ? '院校' : 'School/College/University' }} *</label>
                    <input type="text" v-model="applicationForm.education1School" required 
                           :placeholder="currentLang === 'zh' ? '如：香港大學' : 'e.g., The University of Hong Kong'" />
                  </div>
                  <div class="form-group">
                    <label>{{ currentLang === 'zh' ? '授予日期' : 'Date of Award' }} *</label>
                    <input type="date" v-model="applicationForm.education1AwardDate" required />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group full-width">
                    <label>{{ currentLang === 'zh' ? '資歷' : 'Course/Qualification' }} *</label>
                    <input type="text" v-model="applicationForm.education1Qualification" required 
                           :placeholder="currentLang === 'zh' ? '如：工商管理學士' : 'e.g., Bachelor of Business Administration'" />
                  </div>
                </div>
              </div>

              <div class="qualification-section">
                <h4>{{ currentLang === 'zh' ? '學歷 2 (可選)' : 'Qualification 2 (Optional)' }}</h4>
                <div class="form-row">
                  <div class="form-group">
                    <label>{{ currentLang === 'zh' ? '院校' : 'School/College/University' }}</label>
                    <input type="text" v-model="applicationForm.education2School" />
                  </div>
                  <div class="form-group">
                    <label>{{ currentLang === 'zh' ? '授予日期' : 'Date of Award' }}</label>
                    <input type="date" v-model="applicationForm.education2AwardDate" />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group full-width">
                    <label>{{ currentLang === 'zh' ? '資歷' : 'Course/Qualification' }}</label>
                    <input type="text" v-model="applicationForm.education2Qualification" />
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 3: Professional Qualifications -->
            <div v-if="currentStep === 3" class="form-step">
              <h3>{{ currentLang === 'zh' ? '專業資歷' : 'Professional Qualifications' }}</h3>
              
              <div class="qualification-section">
                <h4>{{ currentLang === 'zh' ? '專業資歷 1' : 'Qualification 1' }}</h4>
                <div class="form-row">
                  <div class="form-group full-width">
                    <label>{{ currentLang === 'zh' ? '專業資歷' : 'Training Programs/Professional Studies/Qualifications' }}</label>
                    <input type="text" v-model="applicationForm.professional1Qualification" 
                           :placeholder="currentLang === 'zh' ? '如：認證供應鏈專業人員 (CSCP)' : 'e.g., Certified Supply Chain Professional (CSCP)'" />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <label>{{ currentLang === 'zh' ? '頒授機構' : 'College/Institution' }}</label>
                    <input type="text" v-model="applicationForm.professional1Institution" 
                           :placeholder="currentLang === 'zh' ? '如：香港職業訓練局' : 'e.g., Hong Kong Vocational Training Council'" />
                  </div>
                  <div class="form-group">
                    <label>{{ currentLang === 'zh' ? '授予日期' : 'Date of Award' }}</label>
                    <input type="date" v-model="applicationForm.professional1AwardDate" />
                  </div>
                </div>
              </div>

              <div class="qualification-section">
                <h4>{{ currentLang === 'zh' ? '專業資歷 2 (可選)' : 'Qualification 2 (Optional)' }}</h4>
                <div class="form-row">
                  <div class="form-group full-width">
                    <label>{{ currentLang === 'zh' ? '專業資歷' : 'Training Programs/Professional Studies/Qualifications' }}</label>
                    <input type="text" v-model="applicationForm.professional2Qualification" />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <label>{{ currentLang === 'zh' ? '頒授機構' : 'College/Institution' }}</label>
                    <input type="text" v-model="applicationForm.professional2Institution" />
                  </div>
                  <div class="form-group">
                    <label>{{ currentLang === 'zh' ? '授予日期' : 'Date of Award' }}</label>
                    <input type="date" v-model="applicationForm.professional2AwardDate" />
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 4: Employment Record -->
            <div v-if="currentStep === 4" class="form-step">
              <h3>{{ currentLang === 'zh' ? '工作經驗' : 'Employment Record' }}</h3>
              
              <div class="qualification-section">
                <h4>{{ currentLang === 'zh' ? '工作經驗 1 (必填)' : 'Employment 1 (Required)' }}</h4>
                <div class="form-row">
                  <div class="form-group full-width">
                    <label>{{ currentLang === 'zh' ? '公司' : 'Company' }} *</label>
                    <input type="text" v-model="applicationForm.employment1Company" required 
                           :placeholder="currentLang === 'zh' ? '如：DHL Supply Chain (Hong Kong) Limited' : 'e.g., DHL Supply Chain (Hong Kong) Limited'" />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <label>{{ currentLang === 'zh' ? '開始日期' : 'From' }} *</label>
                    <input type="date" v-model="applicationForm.employment1FromDate" required />
                  </div>
                  <div class="form-group">
                    <label>{{ currentLang === 'zh' ? '結束日期' : 'To' }}</label>
                    <input type="date" v-model="applicationForm.employment1ToDate" :disabled="applicationForm.employment1IsCurrent" />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                      <input type="checkbox" v-model="applicationForm.employment1IsCurrent" @change="onCurrentJobChange(1)" />
                      <span class="checkmark"></span>
                      {{ currentLang === 'zh' ? '至今在職' : 'Current Position' }}
                    </label>
                  </div>
                  <div class="form-group">
                    <label>{{ currentLang === 'zh' ? '職位' : 'Job Title' }} *</label>
                    <input type="text" v-model="applicationForm.employment1JobTitle" required 
                           :placeholder="currentLang === 'zh' ? '如：物流經理' : 'e.g., Logistics Manager'" />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group full-width">
                    <label>{{ currentLang === 'zh' ? '主要職責' : 'Main Duties' }} *</label>
                    <textarea v-model="applicationForm.employment1MainDuties" required rows="4" 
                              :placeholder="currentLang === 'zh' ? '請簡述核心工作內容（3-5項，用分號隔開）' : 'Please describe core responsibilities (3-5 items, separated by semicolons)'"></textarea>
                  </div>
                </div>
              </div>

              <div class="qualification-section">
                <h4>{{ currentLang === 'zh' ? '工作經驗 2 (可選)' : 'Employment 2 (Optional)' }}</h4>
                <div class="form-row">
                  <div class="form-group full-width">
                    <label>{{ currentLang === 'zh' ? '公司' : 'Company' }}</label>
                    <input type="text" v-model="applicationForm.employment2Company" />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <label>{{ currentLang === 'zh' ? '開始日期' : 'From' }}</label>
                    <input type="date" v-model="applicationForm.employment2FromDate" />
                  </div>
                  <div class="form-group">
                    <label>{{ currentLang === 'zh' ? '結束日期' : 'To' }}</label>
                    <input type="date" v-model="applicationForm.employment2ToDate" :disabled="applicationForm.employment2IsCurrent" />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                      <input type="checkbox" v-model="applicationForm.employment2IsCurrent" @change="onCurrentJobChange(2)" />
                      <span class="checkmark"></span>
                      {{ currentLang === 'zh' ? '至今在職' : 'Current Position' }}
                    </label>
                  </div>
                  <div class="form-group">
                    <label>{{ currentLang === 'zh' ? '職位' : 'Job Title' }}</label>
                    <input type="text" v-model="applicationForm.employment2JobTitle" />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group full-width">
                    <label>{{ currentLang === 'zh' ? '主要職責' : 'Main Duties' }}</label>
                    <textarea v-model="applicationForm.employment2MainDuties" rows="4"></textarea>
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 5: Document Requirements -->
            <div v-if="currentStep === 5" class="form-step">
              <h3>{{ currentLang === 'zh' ? '申請者須知' : 'Applicant Requirements' }}</h3>
              
              <div class="requirements-content">
                <div class="requirement-section">
                  <h4>{{ currentLang === 'zh' ? '文件提交要求' : 'Document Submission Requirements' }}</h4>
                  <p>{{ currentLang === 'zh' 
                    ? '必須隨申請表附上以下文件副本（需郵寄至協會指定地址）：' 
                    : 'The following document copies must be attached with the application form (to be mailed to the designated address of the Association):' 
                  }}</p>
                  <ul>
                    <li>{{ currentLang === 'zh' ? '學歷證明（Academic Qualifications）' : 'Academic Qualifications' }}</li>
                    <li>{{ currentLang === 'zh' ? '專業資格證書（Professional Qualifications）' : 'Professional Qualifications' }}</li>
                    <li>{{ currentLang === 'zh' ? '工作證明（Employment Record，如離職證明、在職信等）' : 'Employment Record (such as resignation certificate, employment letter, etc.)' }}</li>
                  </ul>
                </div>

                <div class="requirement-section">
                  <h4>{{ currentLang === 'zh' ? '保密條款' : 'Privacy Policy' }}</h4>
                  <p>{{ currentLang === 'zh' 
                    ? '所有提交的資料將嚴格保密，僅用於會員申請審核。未經申請人事先同意，協會不得將資料用於其他用途或向第三方披露。' 
                    : 'All submitted information will be strictly confidential and used only for membership application review. Without prior consent from the applicant, the Association shall not use the information for other purposes or disclose it to third parties.' 
                  }}</p>
                </div>

                <div class="agreement-section">
                  <label class="checkbox-label">
                    <input type="checkbox" v-model="applicationForm.documentRequirementsAgreed" required />
                    <span class="checkmark"></span>
                    <span>{{ currentLang === 'zh' ? '我同意以上文件提交要求和保密條款' : 'I agree to the above document submission requirements and privacy policy' }}</span>
                  </label>
                </div>
              </div>
            </div>

            <!-- Step 6: Declaration -->
            <div v-if="currentStep === 6" class="form-step">
              <h3>{{ currentLang === 'zh' ? '聲明' : 'Declaration' }}</h3>
              
              <div class="declaration-content">
                <div class="declaration-section">
                  <h4>{{ currentLang === 'zh' ? '申請人確認' : 'Applicant Confirmation' }}</h4>
                  <p>{{ currentLang === 'zh' 
                    ? '申請人需確認所填寫的資料均真實無誤，並同意遵守協會現行及未來修訂的所有章程。' 
                    : 'The applicant confirms that all information provided is correct to the best of my knowledge and belief, and agrees to be governed by the HKELA rules as currently in force and as may be amended from time to time.' 
                  }}</p>
                  
                  <div class="legal-notice">
                    <p><strong>{{ currentLang === 'zh' ? '法律效力' : 'Legal Effect' }}</strong></p>
                    <p>{{ currentLang === 'zh' 
                      ? '此聲明具有法律約束力，虛假陳述可能導致會籍取消或法律責任。' 
                      : 'This declaration has legal binding effect. False statements may result in membership cancellation or legal liability.' 
                    }}</p>
                  </div>
                </div>

                <div class="agreement-section">
                  <label class="checkbox-label">
                    <input type="checkbox" v-model="applicationForm.declarationAgreed" required />
                    <span class="checkmark"></span>
                    <span>{{ currentLang === 'zh' ? '我確認所填寫的資料均真實無誤' : 'I confirm that all information provided is correct to the best of my knowledge and belief' }}</span>
                  </label>
                  
                  <label class="checkbox-label">
                    <input type="checkbox" v-model="applicationForm.rulesAgreement" required />
                    <span class="checkmark"></span>
                    <span>{{ currentLang === 'zh' ? '我同意遵守協會現行及未來修訂的所有章程' : 'I agree to be governed by the HKELA rules as currently in force and as may be amended from time to time' }}</span>
                  </label>
                </div>
              </div>
            </div>

            <!-- Form Navigation -->
            <div class="form-navigation">
              <button 
                type="button" 
                class="nav-btn prev-btn" 
                @click="previousStep" 
                v-if="currentStep > 1"
              >
                {{ currentLang === 'zh' ? '上一步' : 'Previous' }}
              </button>
              
              <button 
                type="button" 
                class="nav-btn save-btn" 
                @click="saveCurrentStep" 
                :disabled="saving"
              >
                {{ saving ? (currentLang === 'zh' ? '保存中...' : 'Saving...') : (currentLang === 'zh' ? '保存草稿' : 'Save Draft') }}
              </button>
              
              <button 
                type="button" 
                class="nav-btn next-btn" 
                @click="nextStep" 
                v-if="currentStep < 6"
                :disabled="!canProceedToNextStep"
              >
                {{ currentLang === 'zh' ? '下一步' : 'Next' }}
              </button>
              
              <button 
                type="button" 
                class="nav-btn submit-btn" 
                @click="submitDetailedApplication" 
                v-if="currentStep === 6"
                :disabled="!canSubmitApplication || submitting"
              >
                {{ submitting ? (currentLang === 'zh' ? '提交中...' : 'Submitting...') : (currentLang === 'zh' ? '提交申請' : 'Submit Application') }}
              </button>
            </div>

            <div class="form-note">
              <p>{{ currentLang === 'zh' ? '* 為必填項目。您可以隨時保存進度，稍後繼續填寫。' : '* Required fields. You can save your progress at any time and continue later.' }}</p>
            </div>
          </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import { getPublicMembershipTypes } from '@/api/website/membership-types'
import { getPublicMemberBenefits } from '@/api/website/member-benefits'
import { getPublicApplicationProcess } from '@/api/website/application-process'
import { 
  submitDetailedApplication,
  saveDraftApplication,
  updateApplicationStep,
  completeApplication
} from '@/api/website/membership-application'

export default {
  name: 'JoinUs',
  props: {
    currentLang: {
      type: String,
      default: 'zh'
    }
  },
  data() {
    return {
      membershipTypes: [],
      loading: false,
      memberBenefits: [],
      benefitsLoading: false,
      applicationProcess: [],
      processLoading: false,
      currentStep: 1,
      saving: false,
      submitting: false,
      applicationId: null,
      applicationForm: {
        // Step 1: Personal Particulars
        membershipType: '',
        title: '',
        surnameEn: '',
        otherNameEn: '',
        nameZh: '',
        passportId: '',
        birthDate: '',
        correspondenceAddress: '',
        email: '',
        mobile: '',
        workplacePhone: '',
        residencePhone: '',
        
        // Step 2: Academic Qualifications
        education1School: '',
        education1AwardDate: '',
        education1Qualification: '',
        education2School: '',
        education2AwardDate: '',
        education2Qualification: '',
        
        // Step 3: Professional Qualifications
        professional1Qualification: '',
        professional1Institution: '',
        professional1AwardDate: '',
        professional2Qualification: '',
        professional2Institution: '',
        professional2AwardDate: '',
        
        // Step 4: Employment Record
        employment1Company: '',
        employment1FromDate: '',
        employment1ToDate: '',
        employment1IsCurrent: false,
        employment1JobTitle: '',
        employment1MainDuties: '',
        employment2Company: '',
        employment2FromDate: '',
        employment2ToDate: '',
        employment2IsCurrent: false,
        employment2JobTitle: '',
        employment2MainDuties: '',
        
        // Step 5: Document Requirements
        documentRequirementsAgreed: false,
        
        // Step 6: Declaration
        declarationAgreed: false,
        rulesAgreement: false
      }
    }
  },
  mounted() {
    this.loadMembershipTypes()
    this.loadMemberBenefits()
    this.loadApplicationProcess()
  },
  methods: {
    async loadMembershipTypes() {
      try {
        this.loading = true
        const response = await getPublicMembershipTypes()

        if (response.code === 200) {
          this.membershipTypes = response.data || []
        }
      } catch (error) {
        console.error('加载会员类型失败:', error)
      } finally {
        this.loading = false
      }
    },

    async loadMemberBenefits() {
      try {
        this.benefitsLoading = true
        const response = await getPublicMemberBenefits()

        if (response.code === 200) {
          this.memberBenefits = response.data || []
        }
      } catch (error) {
        console.error('加载会员权益失败:', error)
      } finally {
        this.benefitsLoading = false
      }
    },

    async loadApplicationProcess() {
      try {
        this.processLoading = true
        const response = await getPublicApplicationProcess()

        if (response.code === 200) {
          this.applicationProcess = response.data || []
        }
      } catch (error) {
        console.error('加载申请流程失败:', error)
      } finally {
        this.processLoading = false
      }
    },

    getSectionNumber(sectionType) {
      let sectionNumber = 1

      // 检查会员类型section是否显示
      if (sectionType === 'membershipTypes') {
        return String(sectionNumber).padStart(2, '0')
      }
      if (!this.loading && this.membershipTypes.length > 0) {
        sectionNumber++
      }

      // 检查会员权益section是否显示
      if (sectionType === 'memberBenefits') {
        return String(sectionNumber).padStart(2, '0')
      }
      if (!this.benefitsLoading && this.memberBenefits.length > 0) {
        sectionNumber++
      }

      // 检查申请流程section是否显示
      if (sectionType === 'applicationProcess') {
        return String(sectionNumber).padStart(2, '0')
      }
      if (!this.processLoading && this.applicationProcess.length > 0) {
        sectionNumber++
      }

      // 加入协会的好处section总是显示
      if (sectionType === 'associationBenefits') {
        return String(sectionNumber).padStart(2, '0')
      }
      sectionNumber++

      // 申请表单section总是显示
      if (sectionType === 'applicationForm') {
        return String(sectionNumber).padStart(2, '0')
      }

      return String(sectionNumber).padStart(2, '0')
    },

    getStepTitle(step) {
      const titles = {
        1: this.currentLang === 'zh' ? '個人資料' : 'Personal Info',
        2: this.currentLang === 'zh' ? '學歷' : 'Education',
        3: this.currentLang === 'zh' ? '專業資歷' : 'Professional',
        4: this.currentLang === 'zh' ? '工作經驗' : 'Employment',
        5: this.currentLang === 'zh' ? '申請須知' : 'Requirements',
        6: this.currentLang === 'zh' ? '聲明' : 'Declaration'
      }
      return titles[step] || ''
    },

    goToStep(step) {
      if (step <= this.currentStep || this.canGoToStep(step)) {
        this.currentStep = step
      }
    },

    canGoToStep(step) {
      // 只能前往已完成的步驟或下一步
      return step <= this.currentStep + 1
    },

    previousStep() {
      if (this.currentStep > 1) {
        this.currentStep--
      }
    },

    async nextStep() {
      if (this.currentStep < 6 && this.canProceedToNextStep) {
        // 先保存当前步骤
        await this.saveCurrentStep()
        this.currentStep++
      }
    },

    onCurrentJobChange(employmentNumber) {
      if (employmentNumber === 1 && this.applicationForm.employment1IsCurrent) {
        this.applicationForm.employment1ToDate = ''
      } else if (employmentNumber === 2 && this.applicationForm.employment2IsCurrent) {
        this.applicationForm.employment2ToDate = ''
      }
    },

    async saveCurrentStep() {
      this.saving = true
      try {
        const formData = this.prepareFormData()
        let response
        
        if (this.applicationId) {
          // 更新现有申请
          response = await updateApplicationStep(this.applicationId, this.currentStep, formData)
        } else {
          // 保存新的草稿
          response = await saveDraftApplication(formData)
        }
        
        if (response.code === 200) {
          if (!this.applicationId) {
            this.applicationId = response.data
          }
          this.$message.success(this.currentLang === 'zh' ? '保存成功！' : 'Saved successfully!')
        } else {
          this.$message.error(this.currentLang === 'zh' ? '保存失敗，請稍後重試。' : 'Save failed, please try again later.')
        }
      } catch (error) {
        console.error('Save step error:', error)
        this.$message.error(this.currentLang === 'zh' ? '保存失敗，請稍後重試。' : 'Save failed, please try again later.')
      } finally {
        this.saving = false
      }
    },

    async submitDetailedApplication() {
      this.submitting = true
      try {
        const formData = this.prepareFormData()
        let response
        
        if (this.applicationId) {
          // 完成现有申请
          response = await completeApplication(this.applicationId, formData)
        } else {
          // 直接提交新申请
          response = await submitDetailedApplication(formData)
        }
        
        if (response.code === 200) {
          this.$message.success(this.currentLang === 'zh' ? '申請提交成功！我們將在5個工作日內與您聯繫。' : 'Application submitted successfully! We will contact you within 5 business days.')
          this.resetForm()
        } else {
          this.$message.error(this.currentLang === 'zh' ? '申請提交失敗，請稍後重試。' : 'Application submission failed, please try again later.')
        }
      } catch (error) {
        console.error('Submit detailed application error:', error)
        this.$message.error(this.currentLang === 'zh' ? '申請提交失敗，請稍後重試。' : 'Application submission failed, please try again later.')
      } finally {
        this.submitting = false
      }
    },

    resetForm() {
      this.currentStep = 1
      this.applicationId = null
      this.applicationForm = {
        // Step 1: Personal Particulars
        membershipType: '',
        title: '',
        surnameEn: '',
        otherNameEn: '',
        nameZh: '',
        passportId: '',
        birthDate: '',
        correspondenceAddress: '',
        email: '',
        mobile: '',
        workplacePhone: '',
        residencePhone: '',
        
        // Step 2: Academic Qualifications
        education1School: '',
        education1AwardDate: '',
        education1Qualification: '',
        education2School: '',
        education2AwardDate: '',
        education2Qualification: '',
        
        // Step 3: Professional Qualifications
        professional1Qualification: '',
        professional1Institution: '',
        professional1AwardDate: '',
        professional2Qualification: '',
        professional2Institution: '',
        professional2AwardDate: '',
        
        // Step 4: Employment Record
        employment1Company: '',
        employment1FromDate: '',
        employment1ToDate: '',
        employment1IsCurrent: false,
        employment1JobTitle: '',
        employment1MainDuties: '',
        employment2Company: '',
        employment2FromDate: '',
        employment2ToDate: '',
        employment2IsCurrent: false,
        employment2JobTitle: '',
        employment2MainDuties: '',
        
        // Step 5: Document Requirements
        documentRequirementsAgreed: false,
        
        // Step 6: Declaration
        declarationAgreed: false,
        rulesAgreement: false
      }
    },

    async loadIncompleteApplication() {
      // TODO: 实现根据邮箱加载未完成申请的功能
      // 暂时注释掉，避免ESLint错误
      console.log('Load incomplete application feature to be implemented')
    },

    prepareFormData() {
      return {
        // 基本信息
        membershipType: this.applicationForm.membershipType,
        title: this.applicationForm.title,
        surnameEn: this.applicationForm.surnameEn,
        otherNameEn: this.applicationForm.otherNameEn,
        nameZh: this.applicationForm.nameZh,
        passportId: this.applicationForm.passportId,
        birthDate: this.applicationForm.birthDate,
        correspondenceAddress: this.applicationForm.correspondenceAddress,
        email: this.applicationForm.email,
        mobile: this.applicationForm.mobile,
        workplacePhone: this.applicationForm.workplacePhone,
        residencePhone: this.applicationForm.residencePhone,
        
        // 学历信息
        education1School: this.applicationForm.education1School,
        education1AwardDate: this.applicationForm.education1AwardDate,
        education1Qualification: this.applicationForm.education1Qualification,
        education2School: this.applicationForm.education2School,
        education2AwardDate: this.applicationForm.education2AwardDate,
        education2Qualification: this.applicationForm.education2Qualification,
        
        // 专业资历
        professional1Qualification: this.applicationForm.professional1Qualification,
        professional1Institution: this.applicationForm.professional1Institution,
        professional1AwardDate: this.applicationForm.professional1AwardDate,
        professional2Qualification: this.applicationForm.professional2Qualification,
        professional2Institution: this.applicationForm.professional2Institution,
        professional2AwardDate: this.applicationForm.professional2AwardDate,
        
        // 工作经验
        employment1Company: this.applicationForm.employment1Company,
        employment1FromDate: this.applicationForm.employment1FromDate,
        employment1ToDate: this.applicationForm.employment1ToDate,
        employment1IsCurrent: this.applicationForm.employment1IsCurrent ? 1 : 0,
        employment1JobTitle: this.applicationForm.employment1JobTitle,
        employment1MainDuties: this.applicationForm.employment1MainDuties,
        employment2Company: this.applicationForm.employment2Company,
        employment2FromDate: this.applicationForm.employment2FromDate,
        employment2ToDate: this.applicationForm.employment2ToDate,
        employment2IsCurrent: this.applicationForm.employment2IsCurrent ? 1 : 0,
        employment2JobTitle: this.applicationForm.employment2JobTitle,
        employment2MainDuties: this.applicationForm.employment2MainDuties,
        
        // 申请须知和声明
        documentRequirementsAgreed: this.applicationForm.documentRequirementsAgreed ? 1 : 0,
        declarationAgreed: this.applicationForm.declarationAgreed ? 1 : 0,
        rulesAgreement: this.applicationForm.rulesAgreement ? 1 : 0,
        
        // 当前步骤
        currentStep: this.currentStep
      }
    }
  },
  computed: {
    canProceedToNextStep() {
      switch (this.currentStep) {
        case 1:
          return this.applicationForm.membershipType &&
                 this.applicationForm.title &&
                 this.applicationForm.surnameEn &&
                 this.applicationForm.otherNameEn &&
                 this.applicationForm.nameZh &&
                 this.applicationForm.passportId &&
                 this.applicationForm.birthDate &&
                 this.applicationForm.correspondenceAddress &&
                 this.applicationForm.email &&
                 this.applicationForm.mobile
        case 2:
          return this.applicationForm.education1School &&
                 this.applicationForm.education1AwardDate &&
                 this.applicationForm.education1Qualification
        case 3:
          return true // Professional qualifications are optional
        case 4:
          return this.applicationForm.employment1Company &&
                 this.applicationForm.employment1FromDate &&
                 this.applicationForm.employment1JobTitle &&
                 this.applicationForm.employment1MainDuties &&
                 (this.applicationForm.employment1IsCurrent || this.applicationForm.employment1ToDate)
        case 5:
          return this.applicationForm.documentRequirementsAgreed
        case 6:
          return false // Last step, no next step
        default:
          return false
      }
    },
    
    canSubmitApplication() {
      return this.currentStep === 6 &&
             this.applicationForm.declarationAgreed &&
             this.applicationForm.rulesAgreement
    }
  }
}
</script>

<style scoped>
* {
  box-sizing: border-box;
}

.join-us-page {
  background-color: #fff;
}

.hero-banner {
  height: 500px;
  background-image: url('~@/assets/banner/joinUs.png');
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0;
  overflow: hidden;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.hero-content h1 {
  font-size: 4rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.2rem;
  font-weight: 300;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.6;
  opacity: 0.9;
  max-width: 700px;
  margin: 0 auto 2rem auto;
}

.banner-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  max-width: 600px;
  margin: 0 auto;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 1.5rem;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.stat-number {
  font-size: 2.2rem;
  font-weight: 900;
  color: #ffffff;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.section {
  margin-bottom: 5rem;
  padding: 3rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 3rem;
  gap: 1rem;
}

.section-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #1e293b, #334155);
  color: white;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 800;
  flex-shrink: 0;
}

.section-header h2 {
  font-size: 2rem;
  font-weight: 800;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.02em;
}

/* Membership Grid */
.membership-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.membership-card {
  background: #ffffff;
  border: 2px solid #e2e8f0;
  padding: 2rem;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.membership-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.membership-card.corporate {
  border-right: 6px solid #1e3a8a;
  border-bottom: 6px solid #1e3a8a;
}

.membership-card.individual {
  border-right: 6px solid #16a34a;
  border-bottom: 6px solid #16a34a;
}

.membership-card.associate {
  border-right: 6px solid #d97706;
  border-bottom: 6px solid #d97706;
}

.membership-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: #1e293b;
  color: white;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 700;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.membership-card.corporate .membership-badge {
  background: #1e3a8a;
}

.membership-card.individual .membership-badge {
  background: #16a34a;
}

.membership-card.associate .membership-badge {
  background: #d97706;
}

.membership-icon {
  font-size: 3rem;
  color: #64748b;
  margin-bottom: 1rem;
}

.membership-info h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.membership-description {
  font-size: 0.95rem;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.membership-fee {
  margin-bottom: 1.5rem;
}

.currency {
  font-size: 1rem;
  color: #64748b;
  vertical-align: top;
}

.amount {
  font-size: 2.5rem;
  font-weight: 900;
  color: #1e293b;
}

.period {
  font-size: 1rem;
  color: #64748b;
}

.membership-benefits {
  list-style: none;
  padding: 0;
  text-align: left;
}

.membership-benefits li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f1f5f9;
  font-size: 0.9rem;
  color: #475569;
}

.membership-benefits li:before {
  content: "✓";
  color: #16a34a;
  font-weight: bold;
  margin-right: 0.5rem;
}

/* Requirements Section */
.requirements-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 3rem;
  margin-top: 2rem;
}

.requirement-category h4 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e2e8f0;
}

.requirement-list {
  list-style: none;
  padding: 0;
}

.requirement-list li {
  padding: 0.75rem 0;
  border-bottom: 1px solid #f1f5f9;
  font-size: 0.95rem;
  color: #475569;
  line-height: 1.5;
}

.requirement-list li:before {
  content: "•";
  color: #3b82f6;
  font-weight: bold;
  margin-right: 0.75rem;
}

/* Benefits Grid */
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-top: 2rem;
}

.benefit-card {
  background: #ffffff;
  border: 2px solid #e2e8f0;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.benefit-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.benefit-icon {
  font-size: 2rem;
  color: #3b82f6;
  margin-bottom: 0.75rem;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  margin: 0 auto 0.75rem auto;
}

.benefit-icon img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.benefit-card h4 {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.75rem;
}

.benefit-card p {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
}

/* Process Timeline */
.process-timeline {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.process-step {
  text-align: center;
  position: relative;
}

.step-number {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 800;
  margin: 0 auto 1rem auto;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.step-content h4 {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.step-content p {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
}

/* Association Benefits */
.association-benefits-content {
  margin-top: 2rem;
}

.benefits-text {
  max-width: 900px;
  margin: 0 auto;
}

.benefits-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-left: 4px solid #1e293b;
  padding: 2.5rem;
  margin-bottom: 2rem;
}

.benefits-section h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.benefits-section p {
  font-size: 1rem;
  line-height: 1.8;
  color: #475569;
  margin-bottom: 1.5rem;
  text-align: justify;
}

.benefits-section p:last-child {
  margin-bottom: 0;
}

.benefits-note {
  font-style: italic;
  color: #64748b !important;
  border-top: 1px solid #e2e8f0;
  padding-top: 1.5rem;
  margin-top: 1.5rem;
}

/* Application Form */
.application-form {
  margin-top: 2rem;
}

.form-content {
   width: 100%;
 }
 
 .form-container {
   background: #ffffff;
   border: 2px solid #e2e8f0;
   padding: 2rem;
   box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
   border-radius: 12px;
   width: 100%;
 }

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
}

/* Custom Checkbox Styles */
.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-label {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.9rem;
  color: #374151;
  margin-bottom: 0 !important;
  padding-left: 35px;
  user-select: none;
}

.checkbox-label input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  height: 20px;
  width: 20px;
  background-color: #fff;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.checkbox-label:hover input ~ .checkmark {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.checkbox-label input:checked ~ .checkmark {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.checkbox-label input:checked ~ .checkmark:after {
  display: block;
}

.checkbox-label .checkmark:after {
  left: 6px;
  top: 2px;
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-label input:focus ~ .checkmark {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.agreement-section .checkbox-label {
  margin-bottom: 1rem;
  line-height: 1.5;
}

.agreement-section .checkbox-label span:not(.checkmark) {
  margin-left: 10px;
}

.form-actions {
  margin-top: 2rem;
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  align-items: center;
}

.nav-btn {
  padding: 0.875rem 2rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nav-btn:hover:before {
  left: 100%;
}

.prev-btn {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: #475569;
  border: 1px solid #cbd5e1;
}

.prev-btn:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.save-btn {
  background: linear-gradient(135deg, #d86337 0%, #c55a2e 100%);
  color: white;
}

.save-btn:hover {
  background: linear-gradient(135deg, #c55a2e 0%, #b04f26 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(216, 99, 55, 0.4);
}

.next-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.next-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.nav-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.nav-btn:disabled:before {
  display: none;
}

.nav-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.submit-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 0.875rem 2rem;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.submit-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.submit-btn:hover:before {
  left: 100%;
}

.submit-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.submit-btn:disabled:before {
  display: none;
}

.submit-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.form-note {
  margin-top: 1rem;
}

.form-note p {
  font-size: 0.85rem;
  color: #64748b;
  margin: 0;
}

.contact-info {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  padding: 2rem;
  height: fit-content;
}

.contact-info h4 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #475569;
}

.contact-item i {
  color: #3b82f6;
  width: 16px;
}

.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  color: #64748b;
  font-size: 1.1rem;
}

.empty-state p {
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-container {
    padding: 0 1.5rem;
  }

  .membership-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .requirements-content {
    grid-template-columns: 1fr;
  }

  .application-form {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .hero-banner {
    height: 400px;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .banner-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
    max-width: 300px;
  }

  .section {
    padding: 2rem 0;
    margin-bottom: 3rem;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .section-number {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .membership-grid,
  .benefits-grid,
  .process-timeline {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .hero-banner {
    height: 300px;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 0.9rem;
  }

  .hero-content {
    padding: 0 1rem;
  }

  .main-container {
    padding: 0 1rem;
  }

  .membership-card,
  .benefit-card,
  .form-container,
  .contact-info {
    padding: 1.5rem;
  }
}
</style>
