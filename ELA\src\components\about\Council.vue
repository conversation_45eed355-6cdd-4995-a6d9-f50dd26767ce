<template>
  <div class="council-page">
    <!-- Hero Banner -->
    <section class="hero-banner">
      <div class="hero-overlay"></div>
      <div class="hero-content">
        <div class="container">
          <h1>{{ currentLang === 'zh' ? (categoryData.heroTitleZh || '香港電商物流協會') : (categoryData.heroTitleEn || 'HKELA') }}</h1>
          <p class="hero-subtitle">
            {{ currentLang === 'zh'
              ? (categoryData.heroSubtitleZh || '匯聚業界精英，引領組織戰略發展，共創香港電商物流未來')
              : (categoryData.heroSubtitleEn || 'Bringing together industry elites to lead strategic development and create the future of Hong Kong e-commerce logistics')
            }}
          </p>
        </div>
      </div>
    </section>

    <div class="main-container" v-if="!loading">
      <!-- 动态渲染各个成员类型 -->
      <section
        v-for="(members, typeCode) in groupedMembers"
        :key="typeCode"
        class="section"
      >
        <div class="section-header">
          <h2>
            {{ currentLang === 'zh'
              ? ((members[0] && members[0].memberTypeInfo && members[0].memberTypeInfo.nameZh) || typeCode)
              : ((members[0] && members[0].memberTypeInfo && members[0].memberTypeInfo.nameEn) || typeCode)
            }}
          </h2>
        </div>

        <!-- 根据成员类型显示不同的布局 -->
        <div
          class="members-grid"
          :class="{
            'leadership-grid': isLeadershipType(typeCode),
            'directors-grid': !isLeadershipType(typeCode),
            'directors-three-column': isDirectorsType(typeCode),
            'multi-column': members.length > 3
          }"
        >
          <div
            v-for="member in members"
            :key="member.id"
            class="member-card"
            :class="getMemberCardClass(member)"
            :style="getRandomBorderStyle(member.id)"
          >
            <!-- 社交媒体图标 - 移动到右上角 -->
            <div v-if="member.linkedinUrl" class="member-social">
              <a v-if="member.linkedinUrl" :href="member.linkedinUrl" target="_blank" rel="noopener noreferrer" class="social-link">
                <img src="@/assets/linkedin.png" alt="LinkedIn" class="social-icon" />
              </a>
            </div>

            <div class="member-content">
              <div class="member-photo" @click="openImageModal(member)">
                <img :src="member.avatarUrl || require('@/assets/avater.png')" :alt="member.nameZh" />
              </div>
              <div class="member-info">
                <h3 class="member-name">{{ currentLang === 'zh' ? member.nameZh : member.nameEn }}</h3>
                <p class="member-role">{{ currentLang === 'zh' ? member.positionZh : member.positionEn }}</p>
                <p class="member-department">{{ currentLang === 'zh' ? member.departmentZh : member.departmentEn }}</p>
                <p v-if="member.companyZh" class="member-company">{{ currentLang === 'zh' ? member.companyZh : member.companyEn }}</p>
                <p v-if="member.bioZh" class="member-bio">{{ currentLang === 'zh' ? member.bioZh : member.bioEn }}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 如果没有数据显示提示 -->
      <div v-if="Object.keys(groupedMembers).length === 0" class="no-data">
        <p>{{ currentLang === 'zh' ? '暂无成员数据' : 'No member data available' }}</p>
      </div>
    </div>

    <!-- Loading State -->
    <div v-else class="loading-container">
      <div class="loading-spinner"></div>
      <p>{{ currentLang === 'zh' ? '加載中...' : 'Loading...' }}</p>
    </div>

    <!-- Image Modal -->
    <div v-if="showImageModal" class="image-modal" @click="closeImageModal">
      <div class="modal-content" @click.stop>
        <span class="close-button" @click="closeImageModal">&times;</span>
        <img :src="selectedMember.avatarUrl || require('@/assets/avater.png')" :alt="selectedMember.nameZh" class="modal-image" />
        <div class="modal-info">
          <h3>{{ currentLang === 'zh' ? selectedMember.nameZh : selectedMember.nameEn }}</h3>
          <p>{{ currentLang === 'zh' ? selectedMember.positionZh : selectedMember.positionEn }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getOrganizationData } from '@/api/organization'

export default {
  name: 'Council',
  props: {
    currentLang: {
      type: String,
      default: 'zh'
    }
  },
  data() {
    return {
      loading: false,
      categoryData: {},
      organizationMembers: [],
      groupedMembers: {},
      showImageModal: false,
      selectedMember: {}
    }
  },
  computed: {
    totalMembers() {
      return this.organizationMembers.length
    },
    leadershipCount() {
      return this.organizationMembers.filter(member => member.isLeadership === 1).length
    },
    categoryId() {
      // 优先从路由参数获取分类ID
      if (this.$route.params.categorySlug) {
        const categoryId = this.slugToCategoryId(this.$route.params.categorySlug)
        console.log('Debug - categorySlug:', this.$route.params.categorySlug, '-> categoryId:', categoryId)
        return categoryId
      }

      // 其次从查询参数获取
      if (this.$route.query.categoryId) {
        return this.$route.query.categoryId
      }

      // 最后根据路径判断（保持向后兼容）
      const path = this.$route.path
      if (path.includes('/council')) {
        return 1 // 理事会的ID
      } else if (path.includes('/advisors')) {
        return 2 // 顾问的ID
      } else if (path.includes('/youth-committee')) {
        return 3 // 青年委员会的ID
      }

      // 默认显示理事会
      return 1
    }
  },
  mounted() {
    this.loadOrganizationData()
  },
  watch: {
    '$route'() {
      this.loadOrganizationData()
    }
  },
  methods: {
    async loadOrganizationData() {
      this.loading = true
      try {
        const categoryId = this.categoryId
        const response = await getOrganizationData(categoryId)
        this.categoryData = response.data.category
        this.organizationMembers = response.data.members
        this.groupedMembers = response.data.groupedMembers
        console.log('Loaded organization data:', this.groupedMembers)
      } catch (error) {
        console.error('加载组织架构数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 判断是否为领导层类型
    isLeadershipType(typeCode) {
      const leadershipTypes = ['president', 'vice_president', 'chairman', 'vice_chairman', 'secretary_general']
      return leadershipTypes.includes(typeCode)
    },

    // 判断是否为理事类型
    isDirectorsType(typeCode) {
      // 检查成员类型名称是否为"理事"
      const members = this.groupedMembers[typeCode]
      if (members && members.length > 0 && members[0].memberTypeInfo) {
        const nameZh = members[0].memberTypeInfo.nameZh
        return nameZh === '理事'
      }
      return false
    },

    // 获取成员卡片样式类
    getMemberCardClass(member) {
      const typeCode = (member.memberTypeInfo && member.memberTypeInfo.typeCode) || ''
      if (typeCode === 'president') return 'president'
      if (typeCode === 'vice_president') return 'executive'
      if (typeCode === 'chairman') return 'president'
      if (typeCode === 'vice_chairman') return 'executive'
      if (member.isLeadership === 1) return 'leadership'
      return ''
    },

    // 为每个成员生成不同的照片ID
    getRandomPhotoId(seed) {
      const photoIds = [
        '1560250097-0b93528c311a',
        '1519244703995-f4e0f30006d5',
        '1506794778202-cad84cf45f1d',
        '1472099645785-5658abf4ff4e',
        '1507003211169-0a1dd7228f2d',
        '1494790108755-2616c8671746',
        '1500648767791-00dcc994a43e',
        '1507591064344-4c6ce609b9',
        '1551836022-d5c9c27da41c',
        '1573496359142-b8d87734a5a2',
        '1580489944761-15a19d654956',
        '1566492031773-4f4e44671d66',
        '1438761681033-6461ffad8d80',
        '1463453091185-61582044d8fb',
        '1582750433449-648ed127bb54'
      ]
      return photoIds[seed % photoIds.length]
    },

    slugToCategoryId(slug) {
      // 处理各种可能的slug变体
      if (slug.includes('council') || slug.includes('director') || slug.includes('board')) {
        return 1 // 理事会的ID
      }
      if (slug.includes('advisor') || slug.includes('founding') || slug.includes('permanent')) {
        return 2 // 顾问的ID
      }
      if (slug.includes('youth') || slug.includes('committee')) {
        return 3 // 青年委员会的ID
      }
      if (slug.includes('secretariat')) {
        return 4 // 秘书处的ID
      }

      // 精确匹配（保持向后兼容）
      switch (slug) {
        case 'council':
          return 1 // 理事会的ID
        case 'advisors':
          return 2 // 顾问的ID
        case 'youth-committee':
          return 3 // 青年委员会的ID
        case 'secretariat':
          return 4 // 秘书处的ID
        default:
          return 1 // 默认理事会的ID
      }
    },

    // 打开图片模态框
    openImageModal(member) {
      this.selectedMember = member
      this.showImageModal = true
      document.body.style.overflow = 'hidden'
    },

    // 关闭图片模态框
    closeImageModal() {
      this.showImageModal = false
      this.selectedMember = {}
      document.body.style.overflow = 'auto'
    },

    // 生成随机边框颜色
    getRandomBorderStyle(memberId) {
      // 使用成员ID作为种子，确保每个成员的颜色是固定的
      const colors = [
        '#1e3a8a', // 深蓝
        '#4338ca', // 紫蓝
        '#059669', // 绿色
        '#dc2626', // 红色
        '#ea580c', // 橙色
        '#7c3aed', // 紫色
        '#0891b2', // 青色
        '#be123c', // 玫红
        '#166534', // 深绿
        '#92400e', // 棕色
        '#1f2937', // 深灰
        '#6b21a8' // 深紫
      ]
      
      // 使用成员ID生成一个稳定的随机索引
      const index = memberId % colors.length
      const color = colors[index]
      
      return {
        'border-right': `6px solid ${color}`,
        'border-bottom': `6px solid ${color}`
      }
    }
  }
}
</script>

<style scoped>
* {
  box-sizing: border-box;
}

.council-page {
  background-color: #fff;
}

.hero-banner {
  height: 500px;
  background-image: url('~@/assets/banner/aboutUs.png');
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0;
  overflow: hidden;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.hero-content h1 {
  font-size: 4rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.2rem;
  font-weight: 300;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.6;
  opacity: 0.9;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.banner-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  max-width: 600px;
  margin: 0 auto;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 1.5rem;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.stat-number {
  font-size: 2.2rem;
  font-weight: 900;
  color: #ffffff;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.section {
  margin-bottom: 3rem;
  padding: 2rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.section-header h2 {
  font-size: 2rem;
  font-weight: 800;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.02em;
}

/* 成员网格布局 */
.members-grid {
  display: grid;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.leadership-grid {
  grid-template-columns: 1fr;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.directors-grid {
  grid-template-columns: 1fr;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

/* 理事类型专用的三列布局 */
.directors-three-column {
  grid-template-columns: repeat(3, 1fr) !important;
  max-width: 1200px !important;
}

/* 当成员数量超过3个时，改为三列布局 */
.members-grid.multi-column {
  grid-template-columns: repeat(3, 1fr);
  max-width: 1200px;
}

/* 成员卡片 */
.member-card {
  background: #ffffff;
  border: 2px solid #e2e8f0;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.member-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

/* 移除固定边框颜色，使用动态随机颜色 */

.member-content {
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
}

.member-photo {
  width: 120px;
  height: 120px;
  overflow: hidden;
  border: 3px solid #f1f5f9;
  transition: all 0.3s ease;
  cursor: pointer;
  border-radius: 8px;
  flex-shrink: 0;
}

.member-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.member-card:hover .member-photo {
  border-color: #e2e8f0;
}

.member-card:hover .member-photo img {
  transform: scale(1.05);
}

.member-info {
  text-align: left;
  flex: 1;
}

.member-name {
  font-size: 1.5rem;
  font-weight: 800;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.member-role {
  font-size: 1rem;
  font-weight: 600;
  color: #475569;
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.member-department {
  font-size: 0.95rem;
  color: #64748b;
  line-height: 1.4;
  margin: 0 0 0.5rem 0;
}

.member-company {
  color: #999;
  font-size: 0.85rem;
  font-style: italic;
  margin: 0 0 0.5rem 0;
}

.member-bio {
  color: #64748b;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0.5rem 0 0 0;
}

.member-social {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  display: flex;
  gap: 8px;
}

.social-link {
  display: inline-block;
  width: 48px;
  height: 48px;
  transition: opacity 0.3s ease;
}

.social-link:hover {
  opacity: 0.8;
}

.social-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.no-data {
  text-align: center;
  padding: 3rem;
  color: #64748b;
  font-size: 1.1rem;
}

/* Image Modal Styles */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  position: relative;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 2rem;
  color: #666;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: #e5e5e5;
  color: #333;
}

.modal-image {
  max-width: 400px;
  max-height: 400px;
  width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.modal-info {
  text-align: center;
}

.modal-info h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.modal-info p {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

/* Loading Styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0056b3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  color: #666;
  font-size: 1.1rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-container {
    padding: 0 1.5rem;
  }

  .directors-grid {
    grid-template-columns: 1fr;
  }

  .members-grid.multi-column {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .hero-banner {
    height: 400px;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .section {
    padding: 1.5rem 0;
    margin-bottom: 2rem;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .leadership-grid {
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .directors-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .members-grid.multi-column {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .hero-banner {
    height: 300px;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 0.9rem;
  }

  .hero-content {
    padding: 0 1rem;
  }

  .main-container {
    padding: 0 1rem;
  }

  .member-card {
    padding: 1rem;
  }

  .member-content {
    flex-direction: column;
    gap: 1rem;
  }

  .member-photo {
    width: 75px;
    height: 75px;
    align-self: center;
  }

  .member-name {
    font-size: 1.25rem;
  }

  .directors-grid {
    grid-template-columns: 1fr;
  }
}
</style>
