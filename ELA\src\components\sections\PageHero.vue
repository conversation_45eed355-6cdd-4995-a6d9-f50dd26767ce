<template>
  <section class="page-hero">
    <div class="hero-background">
      <div class="particles">
        <div class="particle" v-for="p in 30" :key="p"></div>
      </div>
      <div class="grid-lines"></div>
    </div>
    <div class="container">
      <div class="hero-content">
        <h1 class="hero-title" v-if="section.config.title">{{ section.config.title }}</h1>
        <p class="hero-subtitle" v-if="section.config.subtitle">{{ section.config.subtitle }}</p>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'PageHero',
  props: {
    section: {
      type: Object,
      required: true
    }
  }
}
</script>

<style scoped>
.page-hero {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 120px 0;
  min-height: 40vh;
  background-color: var(--bg-dark-primary);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-background);
  opacity: 0.6;
}

.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 240, 255, 0.07) 1px, transparent 1px),
    linear-gradient(to right, rgba(0, 240, 255, 0.07) 1px, transparent 1px);
  background-size: 40px 40px;
  animation: pan-grid 60s linear infinite;
}

@keyframes pan-grid {
  0% { background-position: 0 0; }
  100% { background-position: 1200px 1200px; }
}

.particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  background: var(--primary-color);
  border-radius: 50%;
  opacity: 0;
  animation: particle-animation 10s infinite;
}

@keyframes particle-animation {
  from {
    opacity: 0;
    transform: translateY(0) scale(0);
  }
  25% {
    opacity: 1;
  }
  75% {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translateY(-100px) scale(1.5);
  }
}

.particle:nth-child(1) { left: 10%; animation-delay: 1s; width: 3px; height: 3px; }
.particle:nth-child(2) { left: 20%; animation-delay: 3s; width: 2px; height: 2px; }
.particle:nth-child(3) { left: 30%; animation-delay: 2s; width: 4px; height: 4px; }
.particle:nth-child(4) { left: 40%; animation-delay: 5s; width: 2px; height: 2px; }
.particle:nth-child(5) { left: 50%; animation-delay: 0s; width: 3px; height: 3px; }
.particle:nth-child(6) { left: 60%; animation-delay: 6s; width: 2px; height: 2px; }
.particle:nth-child(7) { left: 70%; animation-delay: 4s; width: 4px; height: 4px; }
.particle:nth-child(8) { left: 80%; animation-delay: 8s; width: 3px; height: 3px; }
.particle:nth-child(9) { left: 90%; animation-delay: 7s; width: 2px; height: 2px; }
.particle:nth-child(10) { left: 15%; animation-delay: 9s; width: 3px; height: 3px; }
.particle:nth-child(n+11) { display: none; } /* Show only 10 for performance */

.container {
  position: relative;
  z-index: 1;
  text-align: center;
}

.hero-title {
  font-size: 48px;
  font-weight: 800;
  color: var(--text-heading);
  margin-bottom: 16px;
  line-height: 1.2;
  text-shadow: 0 0 15px rgba(0, 240, 255, 0.5);
  animation: fadeInDown 1s ease-out;
}

.hero-subtitle {
  font-size: 18px;
  color: var(--text-light);
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  animation: fadeInUp 1s ease-out 0.3s;
  animation-fill-mode: both;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .page-hero {
    padding: 100px 0;
    min-height: 30vh;
  }
  .hero-title {
    font-size: 36px;
  }
  .hero-subtitle {
    font-size: 16px;
  }
}
</style> 