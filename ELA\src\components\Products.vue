<template>
  <div class="products">
    <!-- 大背景Banner -->
    <section class="hero-banner">
      <div class="hero-overlay"></div>
      <div class="hero-content">
        <h1 class="hero-title">业务与产品</h1>
      </div>
    </section>

    <!-- 产品卡片展示 -->
    <section class="products-section">
      <div class="container">
        <div class="products-grid">
          <div 
            v-for="product in products" 
            :key="product.id" 
            class="product-card"
            :style="{ backgroundImage: `url(${product.image})` }"
            @click="goToCategory(product.categoryId)"
          >
            <div class="product-overlay">
              <div class="product-content">
                <h3 class="product-title">{{ product.title }}</h3>
                <p class="product-subtitle">{{ product.subtitle }}</p>
                <p class="product-description">{{ product.description }}</p>
                <div class="product-features">
                  <div v-for="feature in product.features" :key="feature" class="feature-item">
                    {{ feature }}
                  </div>
                </div>
                <button class="product-btn">了解更多</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品详情模态框 -->
    <div v-if="showModal" class="product-modal" @click="closeModal">
      <div class="modal-content" @click.stop>
        <button class="modal-close" @click="closeModal">×</button>
        <div class="modal-header">
          <img :src="selectedProduct.image" :alt="selectedProduct.title" class="modal-image">
          <div class="modal-info">
            <h2>{{ selectedProduct.title }}</h2>
            <p class="modal-subtitle">{{ selectedProduct.subtitle }}</p>
            <p class="modal-description">{{ selectedProduct.description }}</p>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-section">
            <h3>产品特点</h3>
            <ul>
              <li v-for="feature in selectedProduct.features" :key="feature">{{ feature }}</li>
            </ul>
          </div>
          <div class="modal-section">
            <h3>应用领域</h3>
            <div class="applications">
              <span v-for="app in selectedProduct.applications" :key="app" class="app-tag">
                {{ app }}
              </span>
            </div>
          </div>
          <div class="modal-section">
            <h3>技术参数</h3>
            <div class="tech-specs">
              <div class="spec-row">
                <span class="spec-label">产品纯度：</span>
                <span class="spec-value">{{ selectedProduct.purity }}</span>
              </div>
              <div class="spec-row">
                <span class="spec-label">包装规格：</span>
                <span class="spec-value">{{ selectedProduct.packaging }}</span>
              </div>
              <div class="spec-row">
                <span class="spec-label">储存条件：</span>
                <span class="spec-value">{{ selectedProduct.storage }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary">索取技术资料</button>
          <button class="btn btn-secondary">联系销售</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Products',
  data() {
    return {
      showModal: false,
      selectedProduct: {},
      products: [
        {
          id: 1,
          title: '氟碳化学品',
          subtitle: 'Fluorochemicals',
          description: '提供多样化的氟碳产品，广泛应用于制冷、发泡、清洗等多个工业领域',
          image: 'https://images.unsplash.com/photo-1581092795442-8820a2c9e1dc?w=800&h=600&fit=crop&q=80',
          features: ['环保制冷剂', '高性能发泡剂', '精密清洗剂', '特种溶剂'],
          applications: ['制冷空调', '建筑保温', '电子清洗', '工业溶剂'],
          purity: '≥99.5%',
          packaging: '钢瓶装/ISO罐装',
          storage: '阴凉干燥处，避免高温',
          categoryId: 'fluorochemicals'
        },
        {
          id: 2,
          title: '汽车化学品',
          subtitle: 'Automotive Chemicals',
          description: '为汽车工业提供高性能制冷剂、添加剂和功能性化学品',
          image: 'https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?w=800&h=600&fit=crop&q=80',
          features: ['汽车空调制冷剂', '发动机添加剂', '刹车液', '防冻液'],
          applications: ['汽车空调', '发动机保护', '制动系统', '冷却系统'],
          purity: '≥99.9%',
          packaging: '小包装/散装',
          storage: '常温储存，避免阳光直射',
          categoryId: 'automotive'
        },
        {
          id: 3,
          title: '新能源材料',
          subtitle: 'New Energy Materials',
          description: '专注于太阳能、风能等新能源领域的功能性化学材料',
          image: 'https://images.unsplash.com/photo-1509391366360-2e959784a276?w=800&h=600&fit=crop&q=80',
          features: ['光伏背板材料', '电池电解液', '储能添加剂', '导热材料'],
          applications: ['太阳能电池', '储能系统', '风力发电', '电动汽车'],
          purity: '≥99.8%',
          packaging: '密封包装',
          storage: '干燥环境，防潮储存',
          categoryId: 'new-energy'
        },
        {
          id: 4,
          title: '农化产品',
          subtitle: 'Agricultural Chemicals',
          description: '为现代农业提供高效、环保的农药中间体和功能性助剂',
          image: 'https://images.unsplash.com/photo-1574943320219-553eb213f72d?w=800&h=600&fit=crop&q=80',
          features: ['含氟农药中间体', '植物生长调节剂', '农药助剂', '土壤改良剂'],
          applications: ['作物保护', '土壤改良', '植物营养', '生物农药'],
          purity: '≥98.0%',
          packaging: '防潮包装',
          storage: '阴凉干燥，远离火源',
          categoryId: 'agricultural'
        },
        {
          id: 5,
          title: '医药中间体',
          subtitle: 'Pharmaceutical Intermediates',
          description: '为制药工业提供高品质的含氟医药中间体和原料药',
          image: 'https://images.unsplash.com/photo-1559757191-5ca4f7c7b467?w=800&h=600&fit=crop&q=80',
          features: ['含氟医药中间体', '原料药', '医药溶剂', '制药助剂'],
          applications: ['抗癌药物', '心血管药物', '神经系统药物', '抗感染药物'],
          purity: '≥99.5%',
          packaging: 'GMP标准包装',
          storage: '低温干燥，严格控制',
          categoryId: 'pharmaceutical'
        }
      ]
    }
  },
  methods: {
    goToCategory(categoryId) {
      this.$router.push(`/products/${categoryId}`)
    },
    showProductDetail(product) {
      this.selectedProduct = product
      this.showModal = true
      document.body.style.overflow = 'hidden'
    },
    closeModal() {
      this.showModal = false
      document.body.style.overflow = 'auto'
    }
  },
  beforeDestroy() {
    document.body.style.overflow = 'auto'
  }
}
</script>

<style scoped>
.products {
  width: 100%;
}

/* Hero Banner */
.hero-banner {
  height: 500px;
  background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=800&fit=crop&q=80');
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
}

/* Products Section */
.products-section {
  padding: 0;
  background: #f8f9fa;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  height: 600px;
  gap: 0;
}

.product-card {
  position: relative;
  background-size: cover;
  background-position: center;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.product-card:hover {
  transform: scale(1.02);
  z-index: 10;
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.8) 100%
  );
  display: flex;
  align-items: flex-end;
  padding: 2rem;
  color: white;
  transition: all 0.3s ease;
}

.product-card:hover .product-overlay {
  background: linear-gradient(
    to bottom,
    rgba(0, 86, 179, 0.1) 0%,
    rgba(0, 86, 179, 0.5) 50%,
    rgba(0, 86, 179, 0.9) 100%
  );
}

.product-content {
  width: 100%;
}

.product-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.product-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-bottom: 1rem;
  font-style: italic;
}

.product-description {
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.product-features {
  margin-bottom: 1rem;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.feature-item {
  font-size: 0.8rem;
  padding: 0.2rem 0;
  opacity: 0.9;
}

.feature-item:before {
  content: '• ';
  color: #ffd700;
  font-weight: bold;
}

.product-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid white;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  opacity: 0;
  transform: translateY(20px);
}

.product-card:hover .product-description,
.product-card:hover .product-features,
.product-card:hover .product-btn {
  opacity: 1;
  max-height: 200px;
  transform: translateY(0);
}

.product-btn:hover {
  background: white;
  color: #0056b3;
  transform: translateY(-2px);
}

/* Modal */
.product-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: #666;
  z-index: 10;
}

.modal-header {
  display: flex;
  gap: 2rem;
  padding: 2rem;
  border-bottom: 1px solid #e9ecef;
}

.modal-image {
  width: 200px;
  height: 150px;
  object-fit: cover;
  border-radius: 8px;
}

.modal-info {
  flex: 1;
}

.modal-info h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
}

.modal-subtitle {
  color: #0056b3;
  font-style: italic;
  margin-bottom: 1rem;
}

.modal-description {
  color: #666;
  line-height: 1.6;
}

.modal-body {
  padding: 2rem;
}

.modal-section {
  margin-bottom: 2rem;
}

.modal-section h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  border-bottom: 2px solid #0056b3;
  padding-bottom: 0.5rem;
}

.modal-section ul {
  list-style: none;
  padding: 0;
}

.modal-section li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  padding-left: 1.5rem;
}

.modal-section li:before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: bold;
}

.applications {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.app-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.tech-specs {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
}

.spec-row {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;
}

.spec-row:last-child {
  border-bottom: none;
}

.spec-label {
  font-weight: 500;
  color: #333;
}

.spec-value {
  color: #0056b3;
  font-weight: 500;
}

.modal-footer {
  padding: 1rem 2rem 2rem;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #0056b3;
  color: white;
}

.btn-primary:hover {
  background: #004494;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
    height: auto;
  }
  
  .product-card {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .product-card {
    height: 250px;
  }
  
  .modal-header {
    flex-direction: column;
  }
  
  .modal-image {
    width: 100%;
    height: 200px;
  }
  
  .modal-footer {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .product-overlay {
    padding: 1rem;
  }
  
  .product-title {
    font-size: 1.2rem;
  }
}
</style>
