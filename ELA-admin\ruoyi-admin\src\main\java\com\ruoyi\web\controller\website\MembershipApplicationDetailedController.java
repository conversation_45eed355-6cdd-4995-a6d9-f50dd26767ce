package com.ruoyi.web.controller.website;

import java.util.List;
import java.util.Date;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.MembershipApplicationDetailed;
import com.ruoyi.system.service.IMembershipApplicationDetailedService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 详细会员申请表Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/website/membership-application-detailed")
public class MembershipApplicationDetailedController extends BaseController
{
    @Autowired
    private IMembershipApplicationDetailedService membershipApplicationDetailedService;

    /**
     * 查询详细会员申请表列表
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application-detailed:list')")
    @GetMapping("/list")
    public TableDataInfo list(MembershipApplicationDetailed membershipApplicationDetailed)
    {
        startPage();
        List<MembershipApplicationDetailed> list = membershipApplicationDetailedService.selectMembershipApplicationDetailedList(membershipApplicationDetailed);
        return getDataTable(list);
    }

    /**
     * 根据步骤查询申请列表
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application-detailed:list')")
    @GetMapping("/listByStep/{step}")
    public TableDataInfo listByStep(@PathVariable Integer step)
    {
        startPage();
        List<MembershipApplicationDetailed> list = membershipApplicationDetailedService.selectByCurrentStep(step);
        return getDataTable(list);
    }

    /**
     * 根据状态查询申请列表
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application-detailed:list')")
    @GetMapping("/listByStatus/{status}")
    public TableDataInfo listByStatus(@PathVariable Integer status)
    {
        startPage();
        List<MembershipApplicationDetailed> list = membershipApplicationDetailedService.selectByStatus(status);
        return getDataTable(list);
    }

    /**
     * 导出详细会员申请表列表
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application-detailed:export')")
    @Log(title = "详细会员申请表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MembershipApplicationDetailed membershipApplicationDetailed)
    {
        List<MembershipApplicationDetailed> list = membershipApplicationDetailedService.selectMembershipApplicationDetailedList(membershipApplicationDetailed);
        ExcelUtil<MembershipApplicationDetailed> util = new ExcelUtil<MembershipApplicationDetailed>(MembershipApplicationDetailed.class);
        util.exportExcel(response, list, "详细会员申请表数据");
    }

    /**
     * 获取详细会员申请表详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application-detailed:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(membershipApplicationDetailedService.selectMembershipApplicationDetailedById(id));
    }

    /**
     * 新增详细会员申请表
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application-detailed:add')")
    @Log(title = "详细会员申请表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MembershipApplicationDetailed membershipApplicationDetailed)
    {
        membershipApplicationDetailed.setCreateBy(getUsername());
        return toAjax(membershipApplicationDetailedService.insertMembershipApplicationDetailed(membershipApplicationDetailed));
    }

    /**
     * 修改详细会员申请表
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application-detailed:edit')")
    @Log(title = "详细会员申请表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MembershipApplicationDetailed membershipApplicationDetailed)
    {
        membershipApplicationDetailed.setUpdateBy(getUsername());
        // 如果状态变为已处理或已拒绝，且处理时间为空，则设置当前时间
        if ((membershipApplicationDetailed.getStatus() != null && 
            ("1".equals(membershipApplicationDetailed.getStatus()) || "2".equals(membershipApplicationDetailed.getStatus()))) 
            && membershipApplicationDetailed.getProcessedTime() == null) {
            membershipApplicationDetailed.setProcessedTime(new Date());
            membershipApplicationDetailed.setProcessedBy(getUsername());
        }
        return toAjax(membershipApplicationDetailedService.updateMembershipApplicationDetailed(membershipApplicationDetailed));
    }

    /**
     * 删除详细会员申请表
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application-detailed:remove')")
    @Log(title = "详细会员申请表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(membershipApplicationDetailedService.deleteMembershipApplicationDetailedByIds(ids));
    }

    /**
     * 提交申请到下一步
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application-detailed:edit')")
    @Log(title = "提交申请到下一步", businessType = BusinessType.UPDATE)
    @PutMapping("/nextStep/{id}")
    public AjaxResult nextStep(@PathVariable Long id)
    {
        return toAjax(membershipApplicationDetailedService.submitToNextStep(id));
    }

    /**
     * 更新申请步骤
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application-detailed:edit')")
    @Log(title = "更新申请步骤", businessType = BusinessType.UPDATE)
    @PutMapping("/updateStep")
    public AjaxResult updateStep(@RequestParam Long id, @RequestParam Integer currentStep)
    {
        return toAjax(membershipApplicationDetailedService.updateCurrentStep(id, currentStep));
    }

    /**
     * 审核申请
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application-detailed:review')")
    @Log(title = "审核申请", businessType = BusinessType.UPDATE)
    @PutMapping("/review")
    public AjaxResult review(@RequestParam Long id, 
                           @RequestParam Boolean approved, 
                           @RequestParam(required = false) String processNotes)
    {
        String processedBy = getUsername();
        return toAjax(membershipApplicationDetailedService.reviewApplication(id, approved, processNotes, processedBy));
    }

    /**
     * 完成申请
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application-detailed:edit')")
    @Log(title = "完成申请", businessType = BusinessType.UPDATE)
    @PutMapping("/complete/{id}")
    public AjaxResult complete(@PathVariable Long id)
    {
        return toAjax(membershipApplicationDetailedService.completeApplication(id));
    }

    /**
     * 更新申请状态
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application-detailed:edit')")
    @Log(title = "更新申请状态", businessType = BusinessType.UPDATE)
    @PutMapping("/updateStatus")
    public AjaxResult updateStatus(@RequestParam Long id, 
                                 @RequestParam Integer status, 
                                 @RequestParam(required = false) String processNotes)
    {
        String processedBy = getUsername();
        return toAjax(membershipApplicationDetailedService.updateStatus(id, status, processNotes, processedBy));
    }
}