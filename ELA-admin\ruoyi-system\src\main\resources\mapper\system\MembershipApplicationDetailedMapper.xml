<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MembershipApplicationDetailedMapper">

    <resultMap type="MembershipApplicationDetailed" id="MembershipApplicationDetailedResult">
        <result property="id"                           column="id"                           />
        <result property="membershipType"               column="membership_type"             />
        <result property="title"                        column="title"                        />
        <result property="surnameEn"                    column="surname_en"                  />
        <result property="otherNameEn"                  column="other_name_en"               />
        <result property="nameZh"                       column="name_zh"                     />
        <result property="passportId"                   column="passport_id"                 />
        <result property="birthDate"                    column="birth_date"                  />
        <result property="correspondenceAddress"        column="correspondence_address"      />
        <result property="email"                        column="email"                        />
        <result property="mobile"                       column="mobile"                       />

        <result property="workplacePhone"               column="workplace_phone"             />
        <result property="residencePhone"               column="residence_phone"             />
        <result property="education1School"            column="education_1_school"          />
        <result property="education1AwardDate"          column="education_1_award_date"     />
        <result property="education1Qualification"      column="education_1_qualification"  />
        <result property="education2School"            column="education_2_school"          />
        <result property="education2AwardDate"          column="education_2_award_date"     />
        <result property="education2Qualification"      column="education_2_qualification"  />
        <result property="professional1Qualification"   column="professional_1_qualification" />
        <result property="professional1Institution"    column="professional_1_institution"  />
        <result property="professional1AwardDate"      column="professional_1_award_date"   />
        <result property="professional2Qualification"   column="professional_2_qualification" />
        <result property="professional2Institution"    column="professional_2_institution"  />
        <result property="professional2AwardDate"      column="professional_2_award_date"   />
        <result property="employment1Company"           column="employment_1_company"         />
        <result property="employment1FromDate"          column="employment_1_from_date"       />
        <result property="employment1ToDate"            column="employment_1_to_date"         />
        <result property="employment1IsCurrent"         column="employment_1_is_current"      />
        <result property="employment1JobTitle"          column="employment_1_job_title"       />
        <result property="employment1MainDuties"        column="employment_1_main_duties"     />
        <result property="employment2Company"           column="employment_2_company"         />
        <result property="employment2FromDate"          column="employment_2_from_date"       />
        <result property="employment2ToDate"            column="employment_2_to_date"         />
        <result property="employment2IsCurrent"         column="employment_2_is_current"      />
        <result property="employment2JobTitle"          column="employment_2_job_title"       />
        <result property="employment2MainDuties"        column="employment_2_main_duties"     />
        <result property="documentRequirementsAgreed"   column="document_requirements_agreed" />
        <result property="privacyPolicyAgreed"          column="privacy_policy_agreed"       />
        <result property="declarationAgreed"            column="declaration_agreed"          />
        <result property="rulesAgreement"               column="rules_agreement"             />
        <result property="currentStep"                  column="current_step"                />
        <result property="isCompleted"                  column="is_completed"                />
        <result property="status"                       column="status"                       />
        <result property="processNotes"                 column="process_notes"               />
        <result property="processedBy"                  column="processed_by"                />
        <result property="processedTime"                column="processed_time"              />
        <result property="createBy"                     column="create_by"                   />
        <result property="createTime"                   column="create_time"                 />
        <result property="updateBy"                     column="update_by"                   />
        <result property="updateTime"                   column="update_time"                 />
        <result property="remark"                       column="remark"                       />
    </resultMap>

    <sql id="selectMembershipApplicationDetailedVo">
        select id, membership_type, title, surname_en, other_name_en, name_zh, passport_id, birth_date,
               correspondence_address, email, mobile, workplace_phone, residence_phone,
               education_1_school, education_1_award_date, education_1_qualification,
        education_2_school, education_2_award_date, education_2_qualification,
               professional_1_qualification, professional_1_institution, professional_1_award_date,
        professional_2_qualification, professional_2_institution, professional_2_award_date,
               employment_1_company, employment_1_from_date, employment_1_to_date, employment_1_is_current,
        employment_1_job_title, employment_1_main_duties,
        employment_2_company, employment_2_from_date, employment_2_to_date, employment_2_is_current,
        employment_2_job_title, employment_2_main_duties,
               document_requirements_agreed, privacy_policy_agreed, declaration_agreed, rules_agreement,
               current_step, is_completed, status, process_notes, processed_by, processed_time,
               create_by, create_time, update_by, update_time, remark
        from membership_applications_detailed
    </sql>

    <select id="selectMembershipApplicationDetailedList" parameterType="MembershipApplicationDetailed" resultMap="MembershipApplicationDetailedResult">
        <include refid="selectMembershipApplicationDetailedVo"/>
        <where>  
            <if test="membershipType != null  and membershipType != ''"> and membership_type = #{membershipType}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="surnameEn != null  and surnameEn != ''"> and surname_en like concat('%', #{surnameEn}, '%')</if>
            <if test="otherNameEn != null  and otherNameEn != ''"> and other_name_en like concat('%', #{otherNameEn}, '%')</if>
            <if test="nameZh != null  and nameZh != ''"> and name_zh like concat('%', #{nameZh}, '%')</if>
            <if test="passportId != null  and passportId != ''"> and passport_id = #{passportId}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="currentStep != null "> and current_step = #{currentStep}</if>
            <if test="isCompleted != null "> and is_completed = #{isCompleted}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="processedBy != null  and processedBy != ''"> and processed_by = #{processedBy}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMembershipApplicationDetailedById" parameterType="Long" resultMap="MembershipApplicationDetailedResult">
        <include refid="selectMembershipApplicationDetailedVo"/>
        where id = #{id}
    </select>

    <select id="selectByCurrentStep" parameterType="Integer" resultMap="MembershipApplicationDetailedResult">
        <include refid="selectMembershipApplicationDetailedVo"/>
        where current_step = #{currentStep}
        order by create_time desc
    </select>

    <select id="selectByStatus" parameterType="Integer" resultMap="MembershipApplicationDetailedResult">
        <include refid="selectMembershipApplicationDetailedVo"/>
        where status = #{status}
        order by create_time desc
    </select>
        
    <insert id="insertMembershipApplicationDetailed" parameterType="MembershipApplicationDetailed" useGeneratedKeys="true" keyProperty="id">
        insert into membership_applications_detailed
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="membershipType != null and membershipType != ''">membership_type,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="surnameEn != null and surnameEn != ''">surname_en,</if>
            <if test="otherNameEn != null and otherNameEn != ''">other_name_en,</if>
            <if test="nameZh != null and nameZh != ''">name_zh,</if>
            <if test="passportId != null and passportId != ''">passport_id,</if>
            <if test="birthDate != null">birth_date,</if>
            <if test="correspondenceAddress != null and correspondenceAddress != ''">correspondence_address,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="workplacePhone != null and workplacePhone != ''">workplace_phone,</if>
            <if test="residencePhone != null and residencePhone != ''">residence_phone,</if>
            <if test="education1School != null and education1School != ''">education_1_school,</if>
            <if test="education1AwardDate != null">education_1_award_date,</if>
            <if test="education1Qualification != null and education1Qualification != ''">education_1_qualification,</if>
            <if test="education2School != null and education2School != ''">education_2_school,</if>
            <if test="education2AwardDate != null">education_2_award_date,</if>
            <if test="education2Qualification != null and education2Qualification != ''">education_2_qualification,</if>
            <if test="professional1Qualification != null and professional1Qualification != ''">professional_1_qualification,</if>
            <if test="professional1Institution != null and professional1Institution != ''">professional_1_institution,</if>
            <if test="professional1AwardDate != null">professional_1_award_date,</if>
            <if test="professional2Qualification != null and professional2Qualification != ''">professional_2_qualification,</if>
            <if test="professional2Institution != null and professional2Institution != ''">professional_2_institution,</if>
            <if test="professional2AwardDate != null">professional_2_award_date,</if>
            <if test="employment1Company != null and employment1Company != ''">employment_1_company,</if>
            <if test="employment1FromDate != null">employment_1_from_date,</if>
            <if test="employment1ToDate != null">employment_1_to_date,</if>
            <if test="employment1IsCurrent != null">employment_1_is_current,</if>
            <if test="employment1JobTitle != null and employment1JobTitle != ''">employment_1_job_title,</if>
            <if test="employment1MainDuties != null and employment1MainDuties != ''">employment_1_main_duties,</if>
            <if test="employment2Company != null and employment2Company != ''">employment_2_company,</if>
            <if test="employment2FromDate != null">employment_2_from_date,</if>
            <if test="employment2ToDate != null">employment_2_to_date,</if>
            <if test="employment2IsCurrent != null">employment_2_is_current,</if>
            <if test="employment2JobTitle != null and employment2JobTitle != ''">employment_2_job_title,</if>
            <if test="employment2MainDuties != null and employment2MainDuties != ''">employment_2_main_duties,</if>
            <if test="documentRequirementsAgreed != null">document_requirements_agreed,</if>
            <if test="privacyPolicyAgreed != null">privacy_policy_agreed,</if>
            <if test="declarationAgreed != null">declaration_agreed,</if>
            <if test="rulesAgreement != null">rules_agreement,</if>
            <if test="currentStep != null">current_step,</if>
            <if test="isCompleted != null">is_completed,</if>
            <if test="status != null">status,</if>
            <if test="processNotes != null and processNotes != ''">process_notes,</if>
            <if test="processedBy != null and processedBy != ''">processed_by,</if>
            <if test="processedTime != null">processed_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="membershipType != null and membershipType != ''">#{membershipType},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="surnameEn != null and surnameEn != ''">#{surnameEn},</if>
            <if test="otherNameEn != null and otherNameEn != ''">#{otherNameEn},</if>
            <if test="nameZh != null and nameZh != ''">#{nameZh},</if>
            <if test="passportId != null and passportId != ''">#{passportId},</if>
            <if test="birthDate != null">#{birthDate},</if>
            <if test="correspondenceAddress != null and correspondenceAddress != ''">#{correspondenceAddress},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="workplacePhone != null and workplacePhone != ''">#{workplacePhone},</if>
            <if test="residencePhone != null and residencePhone != ''">#{residencePhone},</if>
            <if test="education1School != null and education1School != ''">#{education1School},</if>
            <if test="education1AwardDate != null">#{education1AwardDate},</if>
            <if test="education1Qualification != null and education1Qualification != ''">#{education1Qualification},</if>
            <if test="education2School != null and education2School != ''">#{education2School},</if>
            <if test="education2AwardDate != null">#{education2AwardDate},</if>
            <if test="education2Qualification != null and education2Qualification != ''">#{education2Qualification},</if>
            <if test="professional1Qualification != null and professional1Qualification != ''">#{professional1Qualification},</if>
            <if test="professional1Institution != null and professional1Institution != ''">#{professional1Institution},</if>
            <if test="professional1AwardDate != null">#{professional1AwardDate},</if>
            <if test="professional2Qualification != null and professional2Qualification != ''">#{professional2Qualification},</if>
            <if test="professional2Institution != null and professional2Institution != ''">#{professional2Institution},</if>
            <if test="professional2AwardDate != null">#{professional2AwardDate},</if>
            <if test="employment1Company != null and employment1Company != ''">#{employment1Company},</if>
            <if test="employment1FromDate != null">#{employment1FromDate},</if>
            <if test="employment1ToDate != null">#{employment1ToDate},</if>
            <if test="employment1IsCurrent != null">#{employment1IsCurrent},</if>
            <if test="employment1JobTitle != null and employment1JobTitle != ''">#{employment1JobTitle},</if>
            <if test="employment1MainDuties != null and employment1MainDuties != ''">#{employment1MainDuties},</if>
            <if test="employment2Company != null and employment2Company != ''">#{employment2Company},</if>
            <if test="employment2FromDate != null">#{employment2FromDate},</if>
            <if test="employment2ToDate != null">#{employment2ToDate},</if>
            <if test="employment2IsCurrent != null">#{employment2IsCurrent},</if>
            <if test="employment2JobTitle != null and employment2JobTitle != ''">#{employment2JobTitle},</if>
            <if test="employment2MainDuties != null and employment2MainDuties != ''">#{employment2MainDuties},</if>
            <if test="documentRequirementsAgreed != null">#{documentRequirementsAgreed},</if>
            <if test="privacyPolicyAgreed != null">#{privacyPolicyAgreed},</if>
            <if test="declarationAgreed != null">#{declarationAgreed},</if>
            <if test="rulesAgreement != null">#{rulesAgreement},</if>
            <if test="currentStep != null">#{currentStep},</if>
            <if test="isCompleted != null">#{isCompleted},</if>
            <if test="status != null">#{status},</if>
            <if test="processNotes != null and processNotes != ''">#{processNotes},</if>
            <if test="processedBy != null and processedBy != ''">#{processedBy},</if>
            <if test="processedTime != null">#{processedTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMembershipApplicationDetailed" parameterType="MembershipApplicationDetailed">
        update membership_applications_detailed
        <trim prefix="SET" suffixOverrides=",">
            <if test="membershipType != null and membershipType != ''">membership_type = #{membershipType},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="surnameEn != null and surnameEn != ''">surname_en = #{surnameEn},</if>
            <if test="otherNameEn != null and otherNameEn != ''">other_name_en = #{otherNameEn},</if>
            <if test="nameZh != null and nameZh != ''">name_zh = #{nameZh},</if>
            <if test="passportId != null and passportId != ''">passport_id = #{passportId},</if>
            <if test="birthDate != null">birth_date = #{birthDate},</if>
            <if test="correspondenceAddress != null and correspondenceAddress != ''">correspondence_address = #{correspondenceAddress},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="workplacePhone != null and workplacePhone != ''">workplace_phone = #{workplacePhone},</if>
            <if test="residencePhone != null and residencePhone != ''">residence_phone = #{residencePhone},</if>
            <if test="education1School != null and education1School != ''">education_1_school = #{education1School},</if>
            <if test="education1AwardDate != null">education_1_award_date = #{education1AwardDate},</if>
            <if test="education1Qualification != null and education1Qualification != ''">education_1_qualification = #{education1Qualification},</if>
            <if test="education2School != null and education2School != ''">education_2_school = #{education2School},</if>
            <if test="education2AwardDate != null">education_2_award_date = #{education2AwardDate},</if>
            <if test="education2Qualification != null and education2Qualification != ''">education_2_qualification = #{education2Qualification},</if>
            <if test="professional1Qualification != null and professional1Qualification != ''">professional_1_qualification = #{professional1Qualification},</if>
            <if test="professional1Institution != null and professional1Institution != ''">professional_1_institution = #{professional1Institution},</if>
            <if test="professional1AwardDate != null">professional_1_award_date = #{professional1AwardDate},</if>
            <if test="professional2Qualification != null and professional2Qualification != ''">professional_2_qualification = #{professional2Qualification},</if>
            <if test="professional2Institution != null and professional2Institution != ''">professional_2_institution = #{professional2Institution},</if>
            <if test="professional2AwardDate != null">professional_2_award_date = #{professional2AwardDate},</if>
            <if test="employment1Company != null and employment1Company != ''">employment_1_company = #{employment1Company},</if>
            <if test="employment1FromDate != null">employment_1_from_date = #{employment1FromDate},</if>
            <if test="employment1ToDate != null">employment_1_to_date = #{employment1ToDate},</if>
            <if test="employment1IsCurrent != null">employment_1_is_current = #{employment1IsCurrent},</if>
            <if test="employment1JobTitle != null and employment1JobTitle != ''">employment_1_job_title = #{employment1JobTitle},</if>
            <if test="employment1MainDuties != null and employment1MainDuties != ''">employment_1_main_duties = #{employment1MainDuties},</if>
            <if test="employment2Company != null and employment2Company != ''">employment_2_company = #{employment2Company},</if>
            <if test="employment2FromDate != null">employment_2_from_date = #{employment2FromDate},</if>
            <if test="employment2ToDate != null">employment_2_to_date = #{employment2ToDate},</if>
            <if test="employment2IsCurrent != null">employment_2_is_current = #{employment2IsCurrent},</if>
            <if test="employment2JobTitle != null and employment2JobTitle != ''">employment_2_job_title = #{employment2JobTitle},</if>
            <if test="employment2MainDuties != null and employment2MainDuties != ''">employment_2_main_duties = #{employment2MainDuties},</if>
            <if test="documentRequirementsAgreed != null">document_requirements_agreed = #{documentRequirementsAgreed},</if>
            <if test="privacyPolicyAgreed != null">privacy_policy_agreed = #{privacyPolicyAgreed},</if>
            <if test="declarationAgreed != null">declaration_agreed = #{declarationAgreed},</if>
            <if test="rulesAgreement != null">rules_agreement = #{rulesAgreement},</if>
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="isCompleted != null">is_completed = #{isCompleted},</if>
            <if test="status != null">status = #{status},</if>
            <if test="processNotes != null and processNotes != ''">process_notes = #{processNotes},</if>
            <if test="processedBy != null and processedBy != ''">processed_by = #{processedBy},</if>
            <if test="processedTime != null">processed_time = #{processedTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateCurrentStep">
        update membership_applications_detailed
        set current_step = #{currentStep}, update_time = now()
        where id = #{id}
    </update>

    <update id="updateStatus">
        update membership_applications_detailed
        set status = #{status}, 
            process_notes = #{processNotes}, 
            processed_by = #{processedBy}, 
            processed_time = now(),
            update_time = now()
        where id = #{id}
    </update>

    <delete id="deleteMembershipApplicationDetailedById" parameterType="Long">
        delete from membership_applications_detailed where id = #{id}
    </delete>

    <delete id="deleteMembershipApplicationDetailedByIds" parameterType="String">
        delete from membership_applications_detailed where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>