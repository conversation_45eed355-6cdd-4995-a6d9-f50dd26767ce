import request from '@/utils/request'

// 查询页脚配置列表
export function listFooterConfig(query) {
  return request({
    url: '/website/footerConfig/list',
    method: 'get',
    params: query
  })
}

// 查询页脚配置详细
export function getFooterConfig(id) {
  return request({
    url: '/website/footerConfig/' + id,
    method: 'get'
  })
}

// 新增页脚配置
export function addFooterConfig(data) {
  return request({
    url: '/website/footerConfig',
    method: 'post',
    data: data
  })
}

// 修改页脚配置
export function updateFooterConfig(data) {
  return request({
    url: '/website/footerConfig',
    method: 'put',
    data: data
  })
}

// 删除页脚配置
export function delFooterConfig(id) {
  return request({
    url: '/website/footerConfig/' + id,
    method: 'delete'
  })
}

// 导出页脚配置
export function exportFooterConfig(query) {
  return request({
    url: '/website/footerConfig/export',
    method: 'post',
    data: query
  })
}

// 查询页脚链接列表
export function listFooterLink(query) {
  return request({
    url: '/website/footerLink/list',
    method: 'get',
    params: query
  })
}

// 查询页脚链接详细
export function getFooterLink(id) {
  return request({
    url: '/website/footerLink/' + id,
    method: 'get'
  })
}

// 新增页脚链接
export function addFooterLink(data) {
  return request({
    url: '/website/footerLink',
    method: 'post',
    data: data
  })
}

// 修改页脚链接
export function updateFooterLink(data) {
  return request({
    url: '/website/footerLink',
    method: 'put',
    data: data
  })
}

// 删除页脚链接
export function delFooterLink(id) {
  return request({
    url: '/website/footerLink/' + id,
    method: 'delete'
  })
} 