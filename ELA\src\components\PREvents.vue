<template>
  <div class="pr-events-page">
    <!-- Hero Banner -->
    <section class="hero-banner">
      <div class="hero-overlay"></div>
      <div class="hero-content">
        <div class="container">
          <h1>{{ currentLang === 'zh' ? '公關及活動' : 'PR & Events' }}</h1>
          <p class="hero-subtitle">
            {{ currentLang === 'zh'
              ? '了解我們的最新公關活動和企業動態，共同推動香港電商物流行業發展'
              : 'Discover our latest PR activities and corporate events, promoting the development of Hong Kong e-commerce logistics industry'
            }}
          </p>
        </div>
      </div>
    </section>

    <div class="main-container">
      <!-- Activities Section -->
      <section class="section">
        <div class="section-header">
<!--          <div class="section-number">01</div>-->
          <h2>{{ currentLang === 'zh' ? '活動展示' : 'Activities' }}</h2>
        </div>

        <div class="activities-list" v-loading="loading">
          <div
            v-for="activity in activities"
            :key="activity.id"
            class="activity-card"
            :style="{ backgroundImage: `url(${activity.coverImageUrl})` }"
            @click="goToActivityDetail(activity.id)"
          >
            <div class="activity-overlay">
              <div class="activity-content">
                <h3 class="activity-title">{{ currentLang === 'zh' ? activity.title : (activity.titleEn || activity.title) }}</h3>
                <div class="activity-divider"></div>
                <p class="activity-time">{{ formatDate(activity.startTime) }}</p>
              </div>
            </div>
          </div>

          <!-- 如果没有数据，显示空状态 -->
          <div v-if="!loading && activities.length === 0" class="empty-state">
            <p>{{ currentLang === 'zh' ? '暫無活動' : 'No activities' }}</p>
          </div>
        </div>
      </section>

      <!-- Media Coverage Section -->

    </div>
  </div>
</template>

<script>
import { getPublicActivities } from '@/api/website/activities'

export default {
  name: 'PREvents',
  props: {
    currentLang: {
      type: String,
      default: 'zh'
    }
  },
  data() {
    return {
      activities: [],
      loading: false
    }
  },
  mounted() {
    this.loadActivities()
  },
  methods: {
    async loadActivities() {
      try {
        this.loading = true
        const response = await getPublicActivities()

        if (response.code === 200) {
          this.activities = response.data || []
        }
      } catch (error) {
        console.error('加载活动数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      if (this.currentLang === 'zh') {
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      } else {
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      }
    },

    // 跳转到活动详情页面
    goToActivityDetail(activityId) {
      this.$router.push({
        name: 'ActivityDetail',
        params: { id: activityId },
        query: { lang: this.currentLang }
      })
    }
  }
}
</script>

<style scoped>
* {
  box-sizing: border-box;
}

.pr-events-page {
  background-color: #fff;
}

.hero-banner {
  height: 500px;
  background-image: url('~@/assets/banner/prEvents.png');
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0;
  overflow: hidden;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.hero-content h1 {
  font-size: 4rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.2rem;
  font-weight: 300;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.6;
  opacity: 0.9;
  max-width: 700px;
  margin: 0 auto 2rem auto;
}

.banner-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  max-width: 600px;
  margin: 0 auto;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 1.5rem;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.stat-number {
  font-size: 2.2rem;
  font-weight: 900;
  color: #ffffff;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.section {
  margin-bottom: 5rem;
  padding: 3rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 3rem;
  gap: 1rem;
}

.section-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #1e293b, #334155);
  color: white;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 800;
  flex-shrink: 0;
}

.section-header h2 {
  font-size: 2rem;
  font-weight: 800;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.02em;
}

/* Activities List */
.activities-list {
  display: flex;
  flex-direction: column;
  gap: 3rem;
  margin-top: 2rem;
}

.activity-card {
  width: 100%;
  height: 500px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.activity-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.activity-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.1) 30%,
    rgba(0, 0, 0, 0.7) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 3rem;
}

.activity-content {
  color: white;
  text-align: left;
}

.activity-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1.5rem 0;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.activity-divider {
  width: 80px;
  height: 4px;
  background: #ffffff;
  margin: 1.5rem 0;
  border-radius: 2px;
}

.activity-time {
  font-size: 1.3rem;
  font-weight: 500;
  margin: 0;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.empty-state {
  text-align: center;
  padding: 5rem;
  color: #64748b;
  font-size: 1.3rem;
}

.empty-state p {
  margin: 0;
}

/* Media Grid */
.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.media-card {
  background: #ffffff;
  border: 2px solid #e2e8f0;
  padding: 1.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.media-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.media-type {
  display: inline-block;
  background: #1e293b;
  color: white;
  padding: 0.4rem 0.8rem;
  font-size: 0.75rem;
  font-weight: 700;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.media-card h4 {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.media-source {
  font-size: 0.875rem;
  color: #475569;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.media-date {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-container {
    padding: 0 1.5rem;
  }

  .activities-list {
    gap: 2.5rem;
  }

  .activity-card {
    height: 450px;
  }

  .activity-overlay {
    padding: 2.5rem;
  }

  .activity-title {
    font-size: 2.2rem;
  }

  .activity-time {
    font-size: 1.2rem;
  }

  .media-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .hero-banner {
    height: 400px;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .banner-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
    max-width: 300px;
  }

  .section {
    padding: 2rem 0;
    margin-bottom: 3rem;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .section-number {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .activities-list {
    gap: 2rem;
  }

  .activity-card {
    height: 350px;
  }

  .activity-overlay {
    padding: 2rem;
  }

  .activity-title {
    font-size: 1.8rem;
  }

  .activity-time {
    font-size: 1rem;
  }

  .media-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .hero-banner {
    height: 300px;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 0.9rem;
  }

  .hero-content {
    padding: 0 1rem;
  }

  .main-container {
    padding: 0 1rem;
  }

  .activities-list {
    gap: 1.5rem;
  }

  .activity-card {
    height: 300px;
  }

  .activity-overlay {
    padding: 1.5rem;
  }

  .activity-title {
    font-size: 1.5rem;
  }

  .activity-time {
    font-size: 0.9rem;
  }

  .activity-divider {
    width: 60px;
    height: 3px;
    margin: 1rem 0;
  }

  .media-card {
    padding: 1rem;
  }
}
</style>
