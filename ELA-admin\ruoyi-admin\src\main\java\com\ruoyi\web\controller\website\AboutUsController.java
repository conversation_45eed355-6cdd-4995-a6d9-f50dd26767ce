package com.ruoyi.web.controller.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.AboutUs;
import com.ruoyi.system.service.IAboutUsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 关于我们Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/website/aboutUs")
public class AboutUsController extends BaseController
{
    @Autowired
    private IAboutUsService aboutUsService;

    /**
     * 查询关于我们列表
     */
    @PreAuthorize("@ss.hasPermi('website:aboutus:list')")
    @GetMapping("/list")
    public TableDataInfo list(AboutUs aboutUs)
    {
        startPage();
        List<AboutUs> list = aboutUsService.selectAboutUsList(aboutUs);
        return getDataTable(list);
    }

    /**
     * 导出关于我们列表
     */
    @PreAuthorize("@ss.hasPermi('website:aboutus:export')")
    @Log(title = "关于我们", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AboutUs aboutUs)
    {
        List<AboutUs> list = aboutUsService.selectAboutUsList(aboutUs);
        ExcelUtil<AboutUs> util = new ExcelUtil<AboutUs>(AboutUs.class);
        util.exportExcel(response, list, "关于我们数据");
    }

    /**
     * 获取关于我们详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:aboutus:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(aboutUsService.selectAboutUsById(id));
    }

    /**
     * 新增关于我们
     */
    @PreAuthorize("@ss.hasPermi('website:aboutus:add')")
    @Log(title = "关于我们", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AboutUs aboutUs)
    {
        return toAjax(aboutUsService.insertAboutUs(aboutUs));
    }

    /**
     * 修改关于我们
     */
    @PreAuthorize("@ss.hasPermi('website:aboutus:edit')")
    @Log(title = "关于我们", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AboutUs aboutUs)
    {
        return toAjax(aboutUsService.updateAboutUs(aboutUs));
    }

    /**
     * 删除关于我们
     */
    @PreAuthorize("@ss.hasPermi('website:aboutus:remove')")
    @Log(title = "关于我们", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(aboutUsService.deleteAboutUsByIds(ids));
    }
} 