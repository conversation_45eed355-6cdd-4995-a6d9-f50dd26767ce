package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.MemberType;

/**
 * 成员类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-01
 */
public interface MemberTypeMapper 
{
    /**
     * 查询成员类型
     * 
     * @param id 成员类型主键
     * @return 成员类型
     */
    public MemberType selectMemberTypeById(Long id);

    /**
     * 查询成员类型列表
     * 
     * @param memberType 成员类型
     * @return 成员类型集合
     */
    public List<MemberType> selectMemberTypeList(MemberType memberType);

    /**
     * 新增成员类型
     * 
     * @param memberType 成员类型
     * @return 结果
     */
    public int insertMemberType(MemberType memberType);

    /**
     * 修改成员类型
     * 
     * @param memberType 成员类型
     * @return 结果
     */
    public int updateMemberType(MemberType memberType);

    /**
     * 删除成员类型
     * 
     * @param id 成员类型主键
     * @return 结果
     */
    public int deleteMemberTypeById(Long id);

    /**
     * 批量删除成员类型
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMemberTypeByIds(Long[] ids);

    /**
     * 根据类型代码查询成员类型
     * 
     * @param typeCode 类型代码
     * @return 成员类型
     */
    public MemberType selectMemberTypeByCode(String typeCode);

    /**
     * 检查类型代码是否唯一
     * 
     * @param typeCode 类型代码
     * @return 结果
     */
    public int checkTypeCodeUnique(String typeCode);

    /**
     * 获取成员类型选项列表（用于下拉框）
     * 
     * @return 成员类型选项列表
     */
    public List<MemberType> selectMemberTypeOptions();

    /**
     * 根据组织架构分类ID获取成员类型选项列表
     * 
     * @param categoryId 组织架构分类ID
     * @return 成员类型选项列表
     */
    public List<MemberType> selectMemberTypeOptionsByCategoryId(Long categoryId);
} 