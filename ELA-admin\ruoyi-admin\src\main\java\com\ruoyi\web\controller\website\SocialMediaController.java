package com.ruoyi.web.controller.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SocialMedia;
import com.ruoyi.system.service.ISocialMediaService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 社交媒体信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/website/socialMedia")
public class SocialMediaController extends BaseController
{
    @Autowired
    private ISocialMediaService socialMediaService;

    /**
     * 查询社交媒体信息列表
     */
    @PreAuthorize("@ss.hasPermi('website:socialMedia:list')")
    @GetMapping("/list")
    public TableDataInfo list(SocialMedia socialMedia)
    {
        startPage();
        List<SocialMedia> list = socialMediaService.selectSocialMediaList(socialMedia);
        return getDataTable(list);
    }

    /**
     * 获取社交媒体信息列表（公开接口，无需权限）
     */
    @GetMapping("/public/list")
    public AjaxResult getPublicList()
    {
        List<SocialMedia> list = socialMediaService.selectSocialMediaList(new SocialMedia());
        return success(list);
    }

    /**
     * 导出社交媒体信息列表
     */
    @PreAuthorize("@ss.hasPermi('website:socialMedia:export')")
    @Log(title = "社交媒体信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SocialMedia socialMedia)
    {
        List<SocialMedia> list = socialMediaService.selectSocialMediaList(socialMedia);
        ExcelUtil<SocialMedia> util = new ExcelUtil<SocialMedia>(SocialMedia.class);
        util.exportExcel(response, list, "社交媒体信息数据");
    }

    /**
     * 获取社交媒体信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:socialMedia:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(socialMediaService.selectSocialMediaById(id));
    }

    /**
     * 新增社交媒体信息
     */
    @PreAuthorize("@ss.hasPermi('website:socialMedia:add')")
    @Log(title = "社交媒体信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SocialMedia socialMedia)
    {
        return toAjax(socialMediaService.insertSocialMedia(socialMedia));
    }

    /**
     * 修改社交媒体信息
     */
    @PreAuthorize("@ss.hasPermi('website:socialMedia:edit')")
    @Log(title = "社交媒体信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SocialMedia socialMedia)
    {
        return toAjax(socialMediaService.updateSocialMedia(socialMedia));
    }

    /**
     * 删除社交媒体信息
     */
    @PreAuthorize("@ss.hasPermi('website:socialMedia:remove')")
    @Log(title = "社交媒体信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(socialMediaService.deleteSocialMediaByIds(ids));
    }
} 