<template>
  <div class="product-category">
    <!-- 雪山背景Banner -->
    <section class="hero-banner">
      <div class="hero-overlay"></div>
      <div class="hero-content">
        <h1 class="hero-title">{{ categoryData.title }}</h1>
      </div>
    </section>

    <!-- 产品分类内容 -->
    <section class="category-content">
      <div class="container">
        <!-- 产品简介 -->
        <div class="category-intro">
          <p class="intro-text">{{ categoryData.description }}</p>
        </div>

        <!-- 产品分类卡片 -->
        <div class="subcategory-grid">
          <div 
            v-for="subcategory in categoryData.subcategories" 
            :key="subcategory.id"
            class="subcategory-card"
            :style="{ backgroundImage: `url(${subcategory.image})` }"
            @click="viewSubcategory(subcategory)"
          >
            <div class="subcategory-overlay">
              <div class="subcategory-content">
                <h3 class="subcategory-title">{{ subcategory.title }}</h3>
                <p class="subcategory-subtitle">{{ subcategory.subtitle }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 产品详情列表 -->
        <div class="products-list" v-if="selectedSubcategory">
          <h2 class="products-title">{{ selectedSubcategory.title }} - 产品列表</h2>
          <div class="products-grid">
            <div 
              v-for="product in selectedSubcategory.products" 
              :key="product.id"
              class="product-item"
            >
              <div class="product-image">
                <img :src="product.image" :alt="product.name">
              </div>
              <div class="product-info">
                <h4 class="product-name">{{ product.name }}</h4>
                <p class="product-formula">{{ product.formula }}</p>
                <p class="product-cas">CAS: {{ product.cas }}</p>
                <div class="product-specs">
                  <div class="spec-item">
                    <span class="spec-label">纯度:</span>
                    <span class="spec-value">{{ product.purity }}</span>
                  </div>
                  <div class="spec-item">
                    <span class="spec-label">包装:</span>
                    <span class="spec-value">{{ product.packaging }}</span>
                  </div>
                </div>
                <div class="product-applications">
                  <div class="app-tag" v-for="app in product.applications" :key="app">
                    {{ app }}
                  </div>
                </div>
                <div class="product-actions">
                  <button class="btn btn-primary" @click="requestQuote(product)">
                    询价
                  </button>
                  <button class="btn btn-secondary" @click="viewDetails(product)">
                    详情
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'ProductCategory',
  props: {
    categoryId: {
      type: String,
      default: 'fluorochemicals'
    }
  },
  data() {
    return {
      selectedSubcategory: null,
      categoryData: {}
    }
  },
  computed: {
    currentCategory() {
      return this.getCategoryData(this.categoryId)
    }
  },
  watch: {
    categoryId: {
      immediate: true,
      handler(newId) {
        this.categoryData = this.getCategoryData(newId)
      }
    }
  },
  methods: {
    getCategoryData(categoryId) {
      const categories = {
        fluorochemicals: {
          title: '氟碳化学品',
          description: '向全球提供绿色环保制冷剂、发泡剂、灭火剂和气雾剂等，拥有全系列各规格制冷剂产品。',
          subcategories: [
          {
            id: 1,
            title: '制冷剂',
            subtitle: 'Refrigerants',
            image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop&q=80',
            products: [
              {
                id: 1,
                name: 'R-134a',
                formula: 'CH₂FCF₃',
                cas: '811-97-2',
                purity: '≥99.9%',
                packaging: '钢瓶装',
                image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&q=80',
                applications: ['汽车空调', '商用制冷', '工业制冷']
              },
              {
                id: 2,
                name: 'R-410A',
                formula: 'CH₂F₂/CHF₂CF₃',
                cas: '354-33-6',
                purity: '≥99.5%',
                packaging: '钢瓶装',
                image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&q=80',
                applications: ['家用空调', '商用空调', '热泵系统']
              },
              {
                id: 3,
                name: 'R-32',
                formula: 'CH₂F₂',
                cas: '75-10-5',
                purity: '≥99.9%',
                packaging: '钢瓶装',
                image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&q=80',
                applications: ['新型空调', '热泵', '冷水机组']
              }
            ]
          },
          {
            id: 2,
            title: '灭火剂',
            subtitle: 'Fire Extinguishing Agents',
            image: 'https://images.unsplash.com/photo-1551731409-43eb3e517a1a?w=800&h=600&fit=crop&q=80',
            products: [
              {
                id: 4,
                name: 'HFC-227ea',
                formula: 'CF₃CHFCF₃',
                cas: '431-89-0',
                purity: '≥99.6%',
                packaging: '钢瓶装',
                image: 'https://images.unsplash.com/photo-1551731409-43eb3e517a1a?w=400&h=300&fit=crop&q=80',
                applications: ['机房灭火', '档案室', '电气设备']
              },
              {
                id: 5,
                name: 'HFC-236fa',
                formula: 'CF₃CH₂CF₃',
                cas: '690-39-1',
                purity: '≥99.5%',
                packaging: '钢瓶装',
                image: 'https://images.unsplash.com/photo-1551731409-43eb3e517a1a?w=400&h=300&fit=crop&q=80',
                applications: ['航空灭火', '舰船灭火', '特殊场所']
              }
            ]
          },
          {
            id: 3,
            title: '发泡剂',
            subtitle: 'Blowing Agents',
            image: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=800&h=600&fit=crop&q=80',
            products: [
              {
                id: 6,
                name: 'HFC-245fa',
                formula: 'CHF₂CH₂CF₃',
                cas: '460-73-1',
                purity: '≥99.5%',
                packaging: '钢瓶装',
                image: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop&q=80',
                applications: ['聚氨酯硬泡', '保温材料', '冷藏板材']
              },
              {
                id: 7,
                name: 'HFC-365mfc',
                formula: 'CF₃CH₂CF₂CH₃',
                cas: '406-58-6',
                purity: '≥99.5%',
                packaging: '钢瓶装',
                image: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop&q=80',
                applications: ['喷射泡沫', '现场发泡', '管道保温']
              }
            ]
          }
        ]
        },
        automotive: {
          title: '汽车化学品',
          description: '为汽车工业提供高性能制冷剂、添加剂和功能性化学品，满足汽车制造和维修的各种需求。',
          subcategories: [
            {
              id: 1,
              title: '汽车空调制冷剂',
              subtitle: 'Automotive Refrigerants',
              image: 'https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?w=800&h=600&fit=crop&q=80',
              products: [
                {
                  id: 1,
                  name: 'R-134a (汽车级)',
                  formula: 'CH₂FCF₃',
                  cas: '811-97-2',
                  purity: '≥99.9%',
                  packaging: '小钢瓶装',
                  image: 'https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?w=400&h=300&fit=crop&q=80',
                  applications: ['汽车空调', '商用车辆', '客车空调']
                }
              ]
            },
            {
              id: 2,
              title: '发动机添加剂',
              subtitle: 'Engine Additives',
              image: 'https://images.unsplash.com/photo-1486754735734-325b5831c3ad?w=800&h=600&fit=crop&q=80',
              products: [
                {
                  id: 2,
                  name: '燃油添加剂',
                  formula: 'C₈H₁₈O',
                  cas: '111-87-5',
                  purity: '≥99.0%',
                  packaging: '桶装',
                  image: 'https://images.unsplash.com/photo-1486754735734-325b5831c3ad?w=400&h=300&fit=crop&q=80',
                  applications: ['燃油优化', '发动机清洁', '性能提升']
                }
              ]
            }
          ]
        },
        'new-energy': {
          title: '新能源材料',
          description: '专注于太阳能、风能等新能源领域的功能性化学材料，推动清洁能源技术发展。',
          subcategories: [
            {
              id: 1,
              title: '光伏材料',
              subtitle: 'Photovoltaic Materials',
              image: 'https://images.unsplash.com/photo-1509391366360-2e959784a276?w=800&h=600&fit=crop&q=80',
              products: [
                {
                  id: 1,
                  name: 'EVA封装膜',
                  formula: '(C₂H₄)n(C₄H₆O₂)m',
                  cas: '24937-78-8',
                  purity: '≥99.5%',
                  packaging: '卷装',
                  image: 'https://images.unsplash.com/photo-1509391366360-2e959784a276?w=400&h=300&fit=crop&q=80',
                  applications: ['太阳能电池', '组件封装', '光伏背板']
                }
              ]
            }
          ]
        },
        agricultural: {
          title: '农化产品',
          description: '为现代农业提供高效、环保的农药中间体和功能性助剂，助力农业可持续发展。',
          subcategories: [
            {
              id: 1,
              title: '农药中间体',
              subtitle: 'Pesticide Intermediates',
              image: 'https://images.unsplash.com/photo-1574943320219-553eb213f72d?w=800&h=600&fit=crop&q=80',
              products: [
                {
                  id: 1,
                  name: '含氟农药中间体',
                  formula: 'C₁₀H₅F₃N₂O',
                  cas: '83121-18-0',
                  purity: '≥98.0%',
                  packaging: '袋装',
                  image: 'https://images.unsplash.com/photo-1574943320219-553eb213f72d?w=400&h=300&fit=crop&q=80',
                  applications: ['杀虫剂', '除草剂', '杀菌剂']
                }
              ]
            }
          ]
        },
        pharmaceutical: {
          title: '医药中间体',
          description: '为制药工业提供高品质的含氟医药中间体和原料药，严格符合GMP标准。',
          subcategories: [
            {
              id: 1,
              title: '含氟医药中间体',
              subtitle: 'Fluorinated Pharmaceutical Intermediates',
              image: 'https://images.unsplash.com/photo-1559757191-5ca4f7c7b467?w=800&h=600&fit=crop&q=80',
              products: [
                {
                  id: 1,
                  name: '氟化医药中间体',
                  formula: 'C₁₂H₈F₃NO₂',
                  cas: '458-37-7',
                  purity: '≥99.5%',
                  packaging: 'GMP包装',
                  image: 'https://images.unsplash.com/photo-1559757191-5ca4f7c7b467?w=400&h=300&fit=crop&q=80',
                  applications: ['抗癌药物', '心血管药物', '神经系统药物']
                }
              ]
            }
          ]
        }
      }
      
      return categories[categoryId] || categories.fluorochemicals
    },
    viewSubcategory(subcategory) {
      this.selectedSubcategory = subcategory
      // 滚动到产品列表
      this.$nextTick(() => {
        const productsList = document.querySelector('.products-list')
        if (productsList) {
          productsList.scrollIntoView({ behavior: 'smooth' })
        }
      })
    },
    requestQuote(product) {
      alert(`申请 ${product.name} 的报价`)
    },
    viewDetails(product) {
      alert(`查看 ${product.name} 的详细信息`)
    }
  }
}
</script>

<style scoped>
.product-category {
  width: 100%;
}

/* Hero Banner */
.hero-banner {
  height: 400px;
  background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=800&fit=crop&q=80');
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
}

/* Category Content */
.category-content {
  padding: 80px 0;
  background: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.category-intro {
  text-align: center;
  margin-bottom: 4rem;
}

.intro-text {
  font-size: 1.2rem;
  color: #666;
  line-height: 1.8;
  max-width: 800px;
  margin: 0 auto;
}

/* Subcategory Grid */
.subcategory-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-bottom: 5rem;
}

.subcategory-card {
  height: 300px;
  background-size: cover;
  background-position: center;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.subcategory-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.subcategory-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.8) 100%
  );
  display: flex;
  align-items: flex-end;
  padding: 2rem;
  color: white;
  transition: all 0.3s ease;
}

.subcategory-card:hover .subcategory-overlay {
  background: linear-gradient(
    to bottom,
    rgba(0, 86, 179, 0.2) 0%,
    rgba(0, 86, 179, 0.5) 50%,
    rgba(0, 86, 179, 0.9) 100%
  );
}

.subcategory-content {
  width: 100%;
}

.subcategory-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.subcategory-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  font-style: italic;
}

/* Products List */
.products-list {
  margin-top: 5rem;
}

.products-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  text-align: center;
  margin-bottom: 3rem;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.product-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.product-image {
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-item:hover .product-image img {
  transform: scale(1.05);
}

.product-info {
  padding: 1.5rem;
}

.product-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.product-formula {
  font-family: monospace;
  font-size: 1.1rem;
  color: #0056b3;
  margin-bottom: 0.5rem;
}

.product-cas {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1rem;
}

.product-specs {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.spec-item:last-child {
  margin-bottom: 0;
}

.spec-label {
  font-weight: 500;
  color: #666;
}

.spec-value {
  font-weight: 600;
  color: #333;
}

.product-applications {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.app-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.product-actions {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
}

.btn-primary {
  background: #0056b3;
  color: white;
}

.btn-primary:hover {
  background: #004494;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .subcategory-grid {
    grid-template-columns: 1fr;
  }
  
  .subcategory-card {
    height: 250px;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .product-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .subcategory-title {
    font-size: 1.5rem;
  }
  
  .products-title {
    font-size: 2rem;
  }
}
</style> 