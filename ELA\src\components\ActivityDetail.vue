<template>
  <div class="activity-detail-page">
    <!-- Loading状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>{{ lang === 'zh' ? '加載中...' : 'Loading...' }}</p>
    </div>

    <!-- 活动详情内容 -->
    <div v-else-if="activity" class="activity-detail">
      <!-- Hero Banner -->
      <section class="hero-banner" :style="{ backgroundImage: `url(${activity.coverImageUrl})` }">
        <div class="hero-overlay"></div>
        <div class="hero-content">
          <div class="container">
            <div class="breadcrumb">
              <router-link to="/pr-events" class="breadcrumb-link">
                {{ lang === 'zh' ? '公關及活動' : 'PR & Events' }}
              </router-link>
              <span class="breadcrumb-separator">></span>
              <span class="breadcrumb-current">{{ lang === 'zh' ? '活動詳情' : 'Activity Detail' }}</span>
            </div>
            <h1 class="activity-title">{{ lang === 'zh' ? activity.title : (activity.titleEn || activity.title) }}</h1>
            <div class="activity-meta">
              <div class="meta-item">
                <i class="icon-calendar"></i>
                <span>{{ formatDate(activity.startTime) }}</span>
              </div>
              <div v-if="activity.location" class="meta-item">
                <i class="icon-location"></i>
                <span>{{ activity.location }}</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 活动内容 -->
      <section class="activity-content">
        <div class="container">
          <div class="content-wrapper">
            <!-- 主要内容 -->
            <div class="main-content">
              <!-- 内容图片轮播 -->
              <div v-if="activity.contentImages && activity.contentImages.length > 0" class="content-section">
                <h2>{{ lang === 'zh' ? '內容圖片' : 'Content Images' }}</h2>
                <div class="carousel-container">
                  <div class="carousel-wrapper">
                    <div class="carousel-track" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
                      <div 
                        v-for="(image, index) in activity.contentImages" 
                        :key="index" 
                        class="carousel-slide"
                        @click="openImageModal({ url: image, alt: lang === 'zh' ? activity.title : (activity.titleEn || activity.title) })"
                      >
                        <img :src="image" :alt="lang === 'zh' ? activity.title : (activity.titleEn || activity.title)" class="carousel-image" />
                      </div>
                    </div>
                  </div>
                  
                  <!-- 轮播控制按钮 -->
                  <button 
                    v-if="activity.contentImages.length > 1"
                    @click="prevSlide" 
                    class="carousel-btn carousel-btn-prev"
                    :disabled="currentSlide === 0"
                  >
                    <i class="icon-arrow-left"></i>
                  </button>
                  <button 
                    v-if="activity.contentImages.length > 1"
                    @click="nextSlide" 
                    class="carousel-btn carousel-btn-next"
                    :disabled="currentSlide === activity.contentImages.length - 1"
                  >
                    <i class="icon-arrow-right"></i>
                  </button>
                  
                  <!-- 轮播指示器 -->
                  <div v-if="activity.contentImages.length > 1" class="carousel-indicators">
                    <button 
                      v-for="(image, index) in activity.contentImages" 
                      :key="index"
                      @click="goToSlide(index)"
                      class="indicator"
                      :class="{ active: currentSlide === index }"
                    ></button>
                  </div>
                </div>
              </div>

              <div class="content-section">
                <h2>{{ lang === 'zh' ? '活動詳情' : 'Activity Details' }}</h2>
                <div class="content-text" v-html="lang === 'zh' ? activity.content : (activity.contentEn || activity.content)"></div>
              </div>

              <!-- 活动图片展示 -->
              <div v-if="activity.images && activity.images.length > 0" class="content-section">
                <h2>{{ lang === 'zh' ? '活動圖片' : 'Activity Images' }}</h2>
                <div class="image-gallery">
                  <div 
                    v-for="(image, index) in activity.images" 
                    :key="index" 
                    class="gallery-item"
                    @click="openImageModal(image)"
                  >
                    <img :src="image.url" :alt="image.alt || (lang === 'zh' ? activity.title : (activity.titleEn || activity.title))" class="gallery-image" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
              <div class="info-card">
                <h3>{{ lang === 'zh' ? '活動信息' : 'Event Information' }}</h3>
                <div class="info-list">
                  <div class="info-item">
                    <span class="info-label">{{ lang === 'zh' ? '活動時間' : 'Activity Time' }}:</span>
                    <span class="info-value">{{ formatDate(activity.startTime) }}</span>
                  </div>
                  <div v-if="activity.location" class="info-item">
                    <span class="info-label">{{ lang === 'zh' ? '地點' : 'Location' }}:</span>
                    <span class="info-value">{{ activity.location }}</span>
                  </div>
                  <div v-if="activity.organizer" class="info-item">
                    <span class="info-label">{{ lang === 'zh' ? '主辦方' : 'Organizer' }}:</span>
                    <span class="info-value">{{ activity.organizer }}</span>
                  </div>
                </div>
              </div>

              <!-- 返回按钮 -->
              <div class="action-card">
                <button @click="goBack" class="back-button">
                  <i class="icon-arrow-left"></i>
                  {{ lang === 'zh' ? '返回活動列表' : 'Back to Activities' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <div class="error-content">
        <h2>{{ lang === 'zh' ? '活動未找到' : 'Activity Not Found' }}</h2>
        <p>{{ lang === 'zh' ? '抱歉，您查找的活動不存在或已被刪除。' : 'Sorry, the activity you are looking for does not exist or has been deleted.' }}</p>
        <button @click="goBack" class="back-button">
          {{ lang === 'zh' ? '返回活動列表' : 'Back to Activities' }}
        </button>
      </div>
    </div>

    <!-- 图片模态框 -->
    <div v-if="showImageModal" class="image-modal" @click="closeImageModal">
      <div class="modal-content" @click.stop>
        <span class="close-button" @click="closeImageModal">&times;</span>
        <img :src="selectedImage.url || selectedImage" :alt="selectedImage.alt || (lang === 'zh' ? activity.title : (activity.titleEn || activity.title))" class="modal-image" />
      </div>
    </div>
  </div>
</template>

<script>
import { getActivityDetail } from '@/api/website/activities'

export default {
  name: 'ActivityDetail',
  props: {
    currentLang: {
      type: String,
      default: 'zh'
    }
  },
  computed: {
    // 从props或查询参数获取当前语言
    lang() {
      return this.currentLang || this.$route.query.lang || 'zh'
    }
  },
  data() {
    return {
      activity: null,
      loading: false,
      showImageModal: false,
      selectedImage: null,
      currentSlide: 0
    }
  },
  mounted() {
    this.loadActivityDetail()
  },
  watch: {
    '$route'() {
      this.loadActivityDetail()
    }
  },
  methods: {
    async loadActivityDetail() {
      const activityId = this.$route.params.id
      if (!activityId) {
        this.$router.push('/pr-events')
        return
      }

      try {
        this.loading = true
        const response = await getActivityDetail(activityId)
        
        if (response.code === 200 && response.data) {
          this.activity = response.data
        } else {
          this.activity = null
        }
      } catch (error) {
        console.error('加载活动详情失败:', error)
        this.activity = null
      } finally {
        this.loading = false
      }
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      if (this.lang === 'zh') {
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      } else {
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      }
    },

    openImageModal(image) {
      this.selectedImage = image
      this.showImageModal = true
      document.body.style.overflow = 'hidden'
    },

    closeImageModal() {
      this.showImageModal = false
      this.selectedImage = null
      document.body.style.overflow = 'auto'
    },

    goBack() {
      this.$router.push('/pr-events')
    },

    // 轮播图控制方法
    nextSlide() {
      if (this.activity.contentImages && this.currentSlide < this.activity.contentImages.length - 1) {
        this.currentSlide++
      }
    },

    prevSlide() {
      if (this.currentSlide > 0) {
        this.currentSlide--
      }
    },

    goToSlide(index) {
      this.currentSlide = index
    }
  }
}
</script>

<style scoped>
* {
  box-sizing: border-box;
}

.activity-detail-page {
  background-color: #fff;
  min-height: 100vh;
}

/* Loading状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #64748b;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Hero Banner */
.hero-banner {
  height: 400px;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0;
  overflow: hidden;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.hero-content {
  position: relative;
  z-index: 2;
  color: white;
  width: 100%;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.breadcrumb {
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.breadcrumb-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb-link:hover {
  color: white;
}

.breadcrumb-separator {
  margin: 0 0.5rem;
  color: rgba(255, 255, 255, 0.6);
}

.breadcrumb-current {
  color: white;
}

.activity-title {
  font-size: 3rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 1px;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.activity-meta {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 500;
}

.meta-item i {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* 活动内容 */
.activity-content {
  padding: 4rem 0;
}

.content-wrapper {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 3rem;
}

.main-content {
  min-width: 0;
}

.content-section {
  margin-bottom: 3rem;
}

.content-section h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 3px solid #3b82f6;
}

.content-text {
  font-size: 1rem;
  line-height: 1.8;
  color: #374151;
}

.content-text p {
  margin-bottom: 1.5rem;
}

.content-text h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  margin: 2rem 0 1rem 0;
}

.content-text ul, .content-text ol {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

.content-text li {
  margin-bottom: 0.5rem;
}

/* 图片画廊 */
.image-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.gallery-item {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.gallery-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
}

/* 侧边栏 */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.info-card, .action-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
}

.info-card h3 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-value {
  font-size: 0.95rem;
  color: #374151;
  font-weight: 500;
}

.back-button {
  width: 100%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.back-button:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.back-button i {
  font-size: 1rem;
}

/* 错误状态 */
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 2rem;
}

.error-content {
  text-align: center;
  max-width: 500px;
}

.error-content h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.error-content p {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 2rem;
}

/* 图片模态框 */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
}

.close-button {
  position: absolute;
  top: -40px;
  right: 0;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  background: none;
  border: none;
  padding: 0.5rem;
  line-height: 1;
}

.close-button:hover {
  opacity: 0.7;
}

.modal-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

/* 图标样式 */
.icon-calendar::before {
  content: "📅";
}

.icon-location::before {
  content: "📍";
}

.icon-arrow-left::before {
  content: "←";
}

.icon-arrow-right::before {
  content: "→";
}

/* 轮播图样式 */
.carousel-container {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.carousel-wrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.carousel-track {
  display: flex;
  transition: transform 0.5s ease-in-out;
}

.carousel-slide {
  min-width: 100%;
  position: relative;
  cursor: pointer;
}

.carousel-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
  display: block;
}

.image-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 2rem 1.5rem 1rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: #374151;
  transition: all 0.3s ease;
  z-index: 2;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.carousel-btn:hover:not(:disabled) {
  background: white;
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.carousel-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.carousel-btn-prev {
  left: 15px;
}

.carousel-btn-next {
  right: 15px;
}

.carousel-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 2;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: white;
  transform: scale(1.2);
}

.indicator:hover {
  background: rgba(255, 255, 255, 0.8);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-wrapper {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .hero-banner {
    height: 300px;
  }
  
  .activity-title {
    font-size: 2rem;
  }
  
  .activity-meta {
    gap: 1rem;
  }
  
  .meta-item {
    font-size: 1rem;
  }
  
  .activity-content {
    padding: 2rem 0;
  }
  
  .content-section h2 {
    font-size: 1.5rem;
  }
  
  .image-gallery {
    grid-template-columns: 1fr;
  }
  
  .carousel-image {
    height: 300px;
  }
  
  .carousel-btn {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .carousel-btn-prev {
    left: 10px;
  }
  
  .carousel-btn-next {
    right: 10px;
  }
}

@media (max-width: 480px) {
  .hero-banner {
    height: 250px;
  }
  
  .activity-title {
    font-size: 1.5rem;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .info-card, .action-card {
    padding: 1rem;
  }
  
  .carousel-image {
    height: 250px;
  }
  
  .image-caption {
    padding: 1.5rem 1rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .carousel-btn {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }
  
  .indicator {
    width: 10px;
    height: 10px;
  }
}
</style>