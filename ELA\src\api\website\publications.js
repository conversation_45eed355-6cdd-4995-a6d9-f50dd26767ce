import request from '@/utils/request'

// 查询公开资讯列表
export function getPublicPublications() {
  return request({
    url: '/api/publications/public',
    method: 'get'
  })
}

// 查询最新资讯列表
export function getLatestPublications() {
  return request({
    url: '/api/publications/latest',
    method: 'get'
  })
}

// 查询首页资讯列表
export function getHomepagePublications() {
  return request({
    url: '/api/publications/homepage',
    method: 'get'
  })
}

// 根据ID查询资讯详情
export function getPublicationById(id) {
  return request({
    url: `/api/publications/${id}`,
    method: 'get'
  })
}