package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 网站Banner对象 website_banner
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class WebsiteBanner extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** Banner标题(中文) */
    @Excel(name = "Banner标题(中文)")
    private String titleZh;

    /** Banner标题(英文) */
    @Excel(name = "Banner标题(英文)")
    private String titleEn;

    /** Banner副标题(中文) */
    @Excel(name = "Banner副标题(中文)")
    private String subtitleZh;

    /** Banner副标题(英文) */
    @Excel(name = "Banner副标题(英文)")
    private String subtitleEn;

    /** Banner图片URL */
    @Excel(name = "Banner图片URL")
    private String imageUrl;

    /** 链接地址 */
    @Excel(name = "链接地址")
    private String linkUrl;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 是否显示按钮（0不显示 1显示） */
    @Excel(name = "是否显示按钮", readConverterExp = "0=不显示,1=显示")
    private String showButton;

    /** 按钮文字(中文) */
    @Excel(name = "按钮文字(中文)")
    private String buttonTextZh;

    /** 按钮文字(英文) */
    @Excel(name = "按钮文字(英文)")
    private String buttonTextEn;

    /** 按钮链接 */
    @Excel(name = "按钮链接")
    private String buttonLink;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTitleZh(String titleZh) 
    {
        this.titleZh = titleZh;
    }

    public String getTitleZh() 
    {
        return titleZh;
    }
    public void setTitleEn(String titleEn) 
    {
        this.titleEn = titleEn;
    }

    public String getTitleEn() 
    {
        return titleEn;
    }
    public void setSubtitleZh(String subtitleZh) 
    {
        this.subtitleZh = subtitleZh;
    }

    public String getSubtitleZh() 
    {
        return subtitleZh;
    }
    public void setSubtitleEn(String subtitleEn) 
    {
        this.subtitleEn = subtitleEn;
    }

    public String getSubtitleEn() 
    {
        return subtitleEn;
    }
    public void setImageUrl(String imageUrl) 
    {
        this.imageUrl = imageUrl;
    }

    public String getImageUrl() 
    {
        return imageUrl;
    }
    public void setLinkUrl(String linkUrl) 
    {
        this.linkUrl = linkUrl;
    }

    public String getLinkUrl() 
    {
        return linkUrl;
    }
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setShowButton(String showButton) 
    {
        this.showButton = showButton;
    }

    public String getShowButton() 
    {
        return showButton;
    }
    public void setButtonTextZh(String buttonTextZh) 
    {
        this.buttonTextZh = buttonTextZh;
    }

    public String getButtonTextZh() 
    {
        return buttonTextZh;
    }
    public void setButtonTextEn(String buttonTextEn) 
    {
        this.buttonTextEn = buttonTextEn;
    }

    public String getButtonTextEn() 
    {
        return buttonTextEn;
    }
    public void setButtonLink(String buttonLink) 
    {
        this.buttonLink = buttonLink;
    }

    public String getButtonLink() 
    {
        return buttonLink;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("titleZh", getTitleZh())
            .append("titleEn", getTitleEn())
            .append("subtitleZh", getSubtitleZh())
            .append("subtitleEn", getSubtitleEn())
            .append("imageUrl", getImageUrl())
            .append("linkUrl", getLinkUrl())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("showButton", getShowButton())
            .append("buttonTextZh", getButtonTextZh())
            .append("buttonTextEn", getButtonTextEn())
            .append("buttonLink", getButtonLink())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 