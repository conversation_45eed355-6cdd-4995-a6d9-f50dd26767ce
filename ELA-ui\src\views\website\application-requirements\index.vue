<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="中文标题" prop="titleZh">
        <el-input
          v-model="queryParams.titleZh"
          placeholder="请输入中文标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="英文标题" prop="titleEn">
        <el-input
          v-model="queryParams.titleEn"
          placeholder="请输入英文标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['website:application-requirements:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['website:application-requirements:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['website:application-requirements:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['website:application-requirements:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="applicationRequirementList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="中文标题" align="center" prop="titleZh" />
      <el-table-column label="英文标题" align="center" prop="titleEn" />
      <el-table-column label="中文条件" align="center" prop="requirementsZh" width="300">
        <template slot-scope="scope">
          <el-tag v-for="(item, index) in scope.row.requirementsZh" :key="index" size="small" style="margin: 2px;">
            {{ item }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="英文条件" align="center" prop="requirementsEn" width="300">
        <template slot-scope="scope">
          <el-tag v-for="(item, index) in scope.row.requirementsEn" :key="index" size="small" style="margin: 2px;">
            {{ item }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['website:application-requirements:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['website:application-requirements:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改申请条件对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="中文标题" prop="titleZh">
          <el-input v-model="form.titleZh" placeholder="请输入中文标题" />
        </el-form-item>
        <el-form-item label="英文标题" prop="titleEn">
          <el-input v-model="form.titleEn" placeholder="请输入英文标题" />
        </el-form-item>
        <el-form-item label="中文条件">
          <div v-for="(item, index) in form.requirementsZh" :key="index" class="dynamic-item">
            <el-input
              v-model="form.requirementsZh[index]"
              placeholder="请输入中文条件"
              style="width: 400px;"
            />
            <el-button type="danger" size="mini" @click="removeRequirementZh(index)">删除</el-button>
          </div>
          <el-button type="primary" size="mini" @click="addRequirementZh">添加中文条件</el-button>
        </el-form-item>
        <el-form-item label="英文条件">
          <div v-for="(item, index) in form.requirementsEn" :key="index" class="dynamic-item">
            <el-input
              v-model="form.requirementsEn[index]"
              placeholder="请输入英文条件"
              style="width: 400px;"
            />
            <el-button type="danger" size="mini" @click="removeRequirementEn(index)">删除</el-button>
          </div>
          <el-button type="primary" size="mini" @click="addRequirementEn">添加英文条件</el-button>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listApplicationRequirements, getApplicationRequirement, delApplicationRequirement, addApplicationRequirement, updateApplicationRequirement } from "@/api/website/application-requirements";

export default {
  name: "ApplicationRequirements",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 申请条件表格数据
      applicationRequirementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        titleZh: null,
        titleEn: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        titleZh: [
          { required: true, message: "中文标题不能为空", trigger: "blur" }
        ],
        titleEn: [
          { required: true, message: "英文标题不能为空", trigger: "blur" }
        ],
        sortOrder: [
          { required: true, message: "排序字段不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询申请条件列表 */
    getList() {
      this.loading = true;
      listApplicationRequirements(this.queryParams).then(response => {
        this.applicationRequirementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        titleZh: null,
        titleEn: null,
        requirementsZh: [],
        requirementsEn: [],
        sortOrder: 0,
        status: 1
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加申请条件";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getApplicationRequirement(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改申请条件";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateApplicationRequirement(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addApplicationRequirement(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除申请条件编号为"' + ids + '"的数据项？').then(function() {
        return delApplicationRequirement(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('website/application-requirements/export', {
        ...this.queryParams
      }, `application_requirements_${new Date().getTime()}.xlsx`)
    },
    /** 添加中文条件 */
    addRequirementZh() {
      this.form.requirementsZh.push('')
    },
    /** 移除中文条件 */
    removeRequirementZh(index) {
      this.form.requirementsZh.splice(index, 1)
    },
    /** 添加英文条件 */
    addRequirementEn() {
      this.form.requirementsEn.push('')
    },
    /** 移除英文条件 */
    removeRequirementEn(index) {
      this.form.requirementsEn.splice(index, 1)
    }
  }
};
</script>

<style scoped>
.dynamic-item {
  margin-bottom: 10px;
}

.dynamic-item .el-button {
  margin-left: 10px;
}
</style> 