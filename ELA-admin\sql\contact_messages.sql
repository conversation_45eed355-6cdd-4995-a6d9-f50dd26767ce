-- 联系消息表
DROP TABLE IF EXISTS `contact_messages`;
CREATE TABLE `contact_messages` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `name` varchar(100) NOT NULL COMMENT '姓名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `subject` varchar(200) DEFAULT NULL COMMENT '主题',
  `message` text NOT NULL COMMENT '消息内容',
  `status` char(1) DEFAULT '0' COMMENT '处理状态（0未处理 1已处理）',
  `processed_by` varchar(64) DEFAULT NULL COMMENT '处理人',
  `processed_time` datetime DEFAULT NULL COMMENT '处理时间',
  `processed_note` text COMMENT '处理备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='联系消息表';

-- 插入测试数据
INSERT INTO `contact_messages` (`name`, `email`, `subject`, `message`, `status`, `create_time`) VALUES
('张三', '<EMAIL>', '会员申请咨询', '您好，我想了解如何申请成为会员，请问需要什么条件？', '0', NOW()),
('李四', '<EMAIL>', '活动信息咨询', '请问最近有什么活动吗？我想参加。', '1', NOW()),
('王五', '<EMAIL>', '合作洽谈', '我们公司想与贵协会建立合作关系，请回复。', '0', NOW());

-- 添加联系消息管理菜单
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('联系消息管理', 2000, 7, 'contactMessage', 'website/contactMessage/index', NULL, 1, 0, 'C', '0', '0', 'website:contactMessage:list', 'message', 'admin', NOW(), '', NULL, '联系消息管理菜单');

-- 获取刚插入的菜单ID
SET @contactMessageMenuId = LAST_INSERT_ID();

-- 添加联系消息管理按钮
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('联系消息查询', @contactMessageMenuId, 1, '#', '', NULL, 1, 0, 'F', '0', '0', 'website:contactMessage:query', '#', 'admin', NOW(), '', NULL, ''),
('联系消息导出', @contactMessageMenuId, 2, '#', '', NULL, 1, 0, 'F', '0', '0', 'website:contactMessage:export', '#', 'admin', NOW(), '', NULL, ''),
('联系消息删除', @contactMessageMenuId, 3, '#', '', NULL, 1, 0, 'F', '0', '0', 'website:contactMessage:remove', '#', 'admin', NOW(), '', NULL, ''),
('联系消息编辑', @contactMessageMenuId, 4, '#', '', NULL, 1, 0, 'F', '0', '0', 'website:contactMessage:edit', '#', 'admin', NOW(), '', NULL, ''); 