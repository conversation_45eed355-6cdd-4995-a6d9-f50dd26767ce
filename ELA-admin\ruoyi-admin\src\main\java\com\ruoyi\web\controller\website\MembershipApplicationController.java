package com.ruoyi.web.controller.website;

import java.util.List;
import java.util.Date;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.MembershipApplication;
import com.ruoyi.system.service.IMembershipApplicationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 会员申请表Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/website/membership-application")
public class MembershipApplicationController extends BaseController
{
    @Autowired
    private IMembershipApplicationService membershipApplicationService;

    /**
     * 查询会员申请表列表
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application:list')")
    @GetMapping("/list")
    public TableDataInfo list(MembershipApplication membershipApplication)
    {
        startPage();
        List<MembershipApplication> list = membershipApplicationService.selectMembershipApplicationList(membershipApplication);
        return getDataTable(list);
    }

    /**
     * 导出会员申请表列表
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application:export')")
    @Log(title = "会员申请表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MembershipApplication membershipApplication)
    {
        List<MembershipApplication> list = membershipApplicationService.selectMembershipApplicationList(membershipApplication);
        ExcelUtil<MembershipApplication> util = new ExcelUtil<MembershipApplication>(MembershipApplication.class);
        util.exportExcel(response, list, "会员申请表数据");
    }

    /**
     * 获取会员申请表详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(membershipApplicationService.selectMembershipApplicationById(id));
    }

    /**
     * 新增会员申请表
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application:add')")
    @Log(title = "会员申请表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MembershipApplication membershipApplication)
    {
        return toAjax(membershipApplicationService.insertMembershipApplication(membershipApplication));
    }

    /**
     * 修改会员申请表
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application:edit')")
    @Log(title = "会员申请表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MembershipApplication membershipApplication)
    {
        // 如果状态变为已处理或已拒绝，且处理时间为空，则设置当前时间
        if (("1".equals(membershipApplication.getStatus()) || "2".equals(membershipApplication.getStatus())) 
            && membershipApplication.getProcessedTime() == null) {
            membershipApplication.setProcessedTime(new Date());
        }
        return toAjax(membershipApplicationService.updateMembershipApplication(membershipApplication));
    }

    /**
     * 删除会员申请表
     */
    @PreAuthorize("@ss.hasPermi('website:membership-application:remove')")
    @Log(title = "会员申请表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(membershipApplicationService.deleteMembershipApplicationByIds(ids));
    }
} 