import request from '@/utils/request'

// 查询联系消息列表
export function listContactMessage(query) {
  return request({
    url: '/website/contactMessage/list',
    method: 'get',
    params: query
  })
}

// 查询联系消息详细
export function getContactMessage(id) {
  return request({
    url: '/website/contactMessage/' + id,
    method: 'get'
  })
}

// 新增联系消息
export function addContactMessage(data) {
  return request({
    url: '/website/contactMessage',
    method: 'post',
    data: data
  })
}

// 修改联系消息
export function updateContactMessage(data) {
  return request({
    url: '/website/contactMessage',
    method: 'put',
    data: data
  })
}

// 删除联系消息
export function delContactMessage(id) {
  return request({
    url: '/website/contactMessage/' + id,
    method: 'delete'
  })
}

// 导出联系消息
export function exportContactMessage(query) {
  return request({
    url: '/website/contactMessage/export',
    method: 'post',
    data: query
  })
} 