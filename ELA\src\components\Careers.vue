<template>
  <div class="careers-page">
    <!-- 大背景Banner -->
    <section class="hero-banner">
      <div class="hero-overlay"></div>
      <div class="hero-content">
        <h1 class="hero-title">人才招聘</h1>
        <p class="hero-subtitle">汇聚全球英才，共创美好未来</p>
      </div>
    </section>

    <!-- 人才理念/工作机会切换 -->
    <section class="careers-tabs-section">
      <div class="container">
        <div class="careers-tabs">
          <button
            class="careers-tab"
            :class="{ active: activeTab === 'concept' }"
            @click="switchTab('concept')"
          >
            人才理念
          </button>
          <button
            class="careers-tab"
            :class="{ active: activeTab === 'opportunities' }"
            @click="switchTab('opportunities')"
          >
            工作机会
          </button>
        </div>
      </div>
    </section>

    <!-- 人才理念内容 -->
    <section v-if="activeTab === 'concept'" class="talent-concept-section">
      <div class="container">
        <!-- 特色横幅 -->
        <div class="concept-banner">
          <div class="banner-text">
            <p>香港电子商务物流协会关注岗位规范化管理，建立并优化高端公司长期、健康、可持续发展的"3类9级"岗位体系，为员工提供清晰、多元化的职业发展通道，为员工的职业生涯提升创造更实际有力的条件。</p>
          </div>
        </div>

        <!-- 三个职业序列 -->
        <div class="career-tracks">
          <div class="track-item">
            <h3 class="track-title">管理序列</h3>
            <div class="track-divider"></div>
            <p class="track-description">从事组织管理的相关人员</p>
          </div>
          <div class="track-item">
            <h3 class="track-title">技术序列</h3>
            <div class="track-divider"></div>
            <p class="track-description">在工作中需要特近距离创新发展的化工技术创新力、工程技术能力、产业技术能力的人员</p>
          </div>
          <div class="track-item">
            <h3 class="track-title">技能序列</h3>
            <div class="track-divider"></div>
            <p class="track-description">从事生产操作、分析检验等生产经营管理相关的工作岗位或辅助类岗位的人员。</p>
          </div>
        </div>

        <!-- 培养体系图 -->
        <div class="training-system">
          <div class="training-content">
            <p class="training-description">香港电子商务物流协会对不同类型的员工匹配不同的培训资源，聚焦专业人才能力培养，以课程体系建设为基础，以讲师队伍建设为引擎，为公司未来发展培养一支"结构合理、充满活力、能力突出"的人才队伍。</p>
          </div>
          <div class="training-diagram">
            <div class="training-circle training-top">
              <span>培养<br/>体系</span>
            </div>
            <div class="training-circle training-left">
              <span>讲师<br/>体系</span>
            </div>
            <div class="training-circle training-right">
              <span>课程<br/>体系</span>
            </div>
            <!-- 连接线 -->
            <svg class="connection-lines" viewBox="0 0 300 200">
              <path d="M150 60 L90 140" stroke="#66b3ff" stroke-width="3" fill="none" marker-end="url(#arrowhead1)"/>
              <path d="M150 60 L210 140" stroke="#66b3ff" stroke-width="3" fill="none" marker-end="url(#arrowhead2)"/>
              <path d="M90 140 L210 140" stroke="#66b3ff" stroke-width="3" fill="none" marker-end="url(#arrowhead3)"/>
              <defs>
                <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                  <polygon points="0 0, 10 3.5, 0 7" fill="#66b3ff"/>
                </marker>
                <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                  <polygon points="0 0, 10 3.5, 0 7" fill="#66b3ff"/>
                </marker>
                <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                  <polygon points="0 0, 10 3.5, 0 7" fill="#66b3ff"/>
                </marker>
              </defs>
            </svg>
          </div>
        </div>

        <!-- 招聘卡片 -->
        <div class="recruitment-cards">
          <div class="recruitment-card social-recruitment">
            <h3>社会招聘</h3>
            <p class="recruitment-subtitle">SOCIAL RECRUITMENT</p>
            <button class="recruitment-btn">详细了解 →</button>
          </div>
          <div class="recruitment-card campus-recruitment">
            <h3>校园招聘</h3>
            <p class="recruitment-subtitle">CAMPUS RECRUITMENT</p>
            <button class="recruitment-btn">详细了解 →</button>
          </div>
        </div>
      </div>
    </section>

    <!-- 工作机会内容 -->
    <section v-if="activeTab === 'opportunities'" class="job-opportunities-section">
      <div class="container">
        <div class="opportunities-content">
          <h2>工作机会</h2>
          <p>欢迎优秀人才加入我们的团队</p>
          <!-- 这里可以添加具体的职位列表 -->
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'Careers',
  data() {
    return {
      activeTab: 'concept'
    }
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab
    }
  }
}
</script>

<style scoped>
.careers-page {
  background-color: #fff;
}

/* Hero Banner */
.hero-banner {
  height: 500px;
  background-image: url('@/assets/banner/joinUs.png');
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.2rem;
  font-weight: 300;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.6;
  opacity: 0.9;
}

/* 标签切换 */
.careers-tabs-section {
  background-color: #f8f9fa;
  padding: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.careers-tabs {
  display: flex;
  justify-content: center;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.careers-tab {
  background: none;
  border: none;
  padding: 20px 40px;
  font-size: 18px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  position: relative;
  transition: color 0.3s ease;
}

.careers-tab:hover {
  color: #0056b3;
}

.careers-tab.active {
  color: #0056b3;
}

.careers-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background-color: #0056b3;
}

/* 人才理念内容 */
.talent-concept-section {
  padding: 60px 0;
  background-color: #f8f9fa;
}

.concept-banner {
  background: linear-gradient(135deg, rgba(139, 119, 101, 0.9) 0%, rgba(160, 140, 120, 0.8) 100%);
  background-image: url('https://www.sinochemlt.com/portals/290/images/64f928e4-8a43-41a4-9e79-8d1964d84f93.jpg');
  background-size: cover;
  background-position: center;
  border-radius: 12px;
  padding: 60px 40px;
  margin-bottom: 60px;
  position: relative;
  overflow: hidden;
}

.concept-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(139, 119, 101, 0.7);
}

.banner-text {
  position: relative;
  z-index: 2;
  color: white;
  max-width: 800px;
}

.banner-text p {
  font-size: 16px;
  line-height: 1.8;
  margin: 0;
}

/* 职业序列 */
.career-tracks {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  margin-bottom: 80px;
}

.track-item {
  text-align: center;
  padding: 30px 20px;
}

.track-title {
  font-size: 24px;
  font-weight: 600;
  color: #0056b3;
  margin-bottom: 15px;
}

.track-divider {
  width: 60px;
  height: 3px;
  background-color: #0056b3;
  margin: 0 auto 20px;
}

.track-description {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

/* 培养体系 */
.training-system {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  margin-bottom: 80px;
}

.training-description {
  color: #666;
  line-height: 1.8;
  font-size: 16px;
}

.training-diagram {
  position: relative;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.training-circle {
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  text-align: center;
  line-height: 1.2;
}

.training-top {
  background-color: #1e90ff;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.training-left {
  background-color: #555;
  bottom: 20px;
  left: 20px;
}

.training-right {
  background-color: #0056b3;
  bottom: 20px;
  right: 20px;
}

.connection-lines {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 招聘卡片 */
.recruitment-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
}

.recruitment-card {
  height: 250px;
  border-radius: 12px;
  padding: 40px;
  color: white;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.social-recruitment {
  background: linear-gradient(135deg, #1e90ff 0%, #0066cc 100%);
}

.campus-recruitment {
  background: linear-gradient(135deg, #ff8c00 0%, #ff6600 100%);
}

.recruitment-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.recruitment-card h3 {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 10px 0;
  position: relative;
  z-index: 2;
}

.recruitment-subtitle {
  font-size: 14px;
  opacity: 0.8;
  margin: 0 0 30px 0;
  position: relative;
  z-index: 2;
}

.recruitment-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: white;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  align-self: flex-start;
}

.recruitment-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* 工作机会内容 */
.job-opportunities-section {
  padding: 60px 0;
  background-color: #f8f9fa;
}

.opportunities-content {
  text-align: center;
  padding: 100px 0;
}

.opportunities-content h2 {
  font-size: 32px;
  color: #333;
  margin-bottom: 20px;
}

.opportunities-content p {
  font-size: 16px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-banner {
    height: 400px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .career-tracks {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .training-system {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .recruitment-cards {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .concept-banner {
    padding: 40px 20px;
  }

  .careers-tabs {
    flex-direction: column;
  }

  .careers-tab {
    padding: 15px 20px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .hero-banner {
    height: 300px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 0.9rem;
  }

  .hero-content {
    padding: 0 1rem;
  }
}
</style>
