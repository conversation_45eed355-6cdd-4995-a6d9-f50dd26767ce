package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.FooterConfig;

/**
 * 页脚配置Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IFooterConfigService 
{
    /**
     * 查询页脚配置
     * 
     * @param id 页脚配置主键
     * @return 页脚配置
     */
    public FooterConfig selectFooterConfigById(Long id);

    /**
     * 查询页脚配置列表
     * 
     * @param footerConfig 页脚配置
     * @return 页脚配置集合
     */
    public List<FooterConfig> selectFooterConfigList(FooterConfig footerConfig);

    /**
     * 新增页脚配置
     * 
     * @param footerConfig 页脚配置
     * @return 结果
     */
    public int insertFooterConfig(FooterConfig footerConfig);

    /**
     * 修改页脚配置
     * 
     * @param footerConfig 页脚配置
     * @return 结果
     */
    public int updateFooterConfig(FooterConfig footerConfig);

    /**
     * 批量删除页脚配置
     * 
     * @param ids 需要删除的页脚配置主键集合
     * @return 结果
     */
    public int deleteFooterConfigByIds(Long[] ids);

    /**
     * 删除页脚配置信息
     * 
     * @param id 页脚配置主键
     * @return 结果
     */
    public int deleteFooterConfigById(Long id);
} 