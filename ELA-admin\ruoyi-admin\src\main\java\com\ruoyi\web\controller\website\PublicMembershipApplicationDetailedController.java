package com.ruoyi.web.controller.website;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.MembershipApplicationDetailed;
import com.ruoyi.system.service.IMembershipApplicationDetailedService;
import java.util.Date;

/**
 * 前台详细会员申请表Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/public/membership-application-detailed")
public class PublicMembershipApplicationDetailedController extends BaseController
{
    @Autowired
    private IMembershipApplicationDetailedService membershipApplicationDetailedService;

    /**
     * 提交详细会员申请表
     */
    @PostMapping("/submit")
    public AjaxResult submit(@RequestBody MembershipApplicationDetailed membershipApplicationDetailed)
    {
        try {
            // 设置初始状态
            membershipApplicationDetailed.setStatus("1"); // 待处理
            membershipApplicationDetailed.setCurrentStep(1); // 第一步
            membershipApplicationDetailed.setIsCompleted(0); // 未完成
            // 确保创建时间为当前时间
            membershipApplicationDetailed.setCreateTime(new Date());

            int result = membershipApplicationDetailedService.insertMembershipApplicationDetailed(membershipApplicationDetailed);
            if (result > 0) {
                return AjaxResult.success("申请提交成功，我们将在5个工作日内与您联系", membershipApplicationDetailed.getId());
            } else {
                return AjaxResult.error("申请提交失败，请稍后重试");
            }
        } catch (Exception e) {
            logger.error("提交详细会员申请失败", e);
            return AjaxResult.error("申请提交失败，请稍后重试");
        }
    }

    /**
     * 保存申请草稿
     */
    @PostMapping("/saveDraft")
    public AjaxResult saveDraft(@RequestBody MembershipApplicationDetailed membershipApplicationDetailed)
    {
        try {
            // 如果是新申请，设置初始状态
            if (membershipApplicationDetailed.getId() == null) {
                membershipApplicationDetailed.setStatus("0"); // 待处理
                membershipApplicationDetailed.setCurrentStep(1); // 第一步
                membershipApplicationDetailed.setIsCompleted(0); // 未完成
                membershipApplicationDetailed.setCreateTime(new Date());

                int result = membershipApplicationDetailedService.insertMembershipApplicationDetailed(membershipApplicationDetailed);
                if (result > 0) {
                    return AjaxResult.success("草稿保存成功", membershipApplicationDetailed.getId());
                }
            } else {
                // 更新现有申请
                membershipApplicationDetailed.setUpdateTime(new Date());
                int result = membershipApplicationDetailedService.updateMembershipApplicationDetailed(membershipApplicationDetailed);
                if (result > 0) {
                    return AjaxResult.success("草稿保存成功", membershipApplicationDetailed.getId());
                }
            }
            return AjaxResult.error("草稿保存失败，请稍后重试");
        } catch (Exception e) {
            logger.error("保存申请草稿失败", e);
            return AjaxResult.error("草稿保存失败，请稍后重试");
        }
    }

    /**
     * 获取申请详情（用于继续填写）
     */
    @GetMapping("/{id}")
    public AjaxResult getApplication(@PathVariable Long id)
    {
        try {
            MembershipApplicationDetailed application = membershipApplicationDetailedService.selectMembershipApplicationDetailedById(id);
            if (application != null) {
                return AjaxResult.success(application);
            } else {
                return AjaxResult.error("申请不存在");
            }
        } catch (Exception e) {
            logger.error("获取申请详情失败", e);
            return AjaxResult.error("获取申请详情失败");
        }
    }

    /**
     * 更新申请步骤（前端分步提交）
     */
    @PutMapping("/updateStep")
    public AjaxResult updateStep(@RequestParam Long id,
                               @RequestParam Integer currentStep,
                               @RequestBody MembershipApplicationDetailed membershipApplicationDetailed)
    {
        try {
            // 更新申请数据
            membershipApplicationDetailed.setId(id);
            membershipApplicationDetailed.setCurrentStep(currentStep);
            membershipApplicationDetailed.setUpdateTime(new Date());

            int result = membershipApplicationDetailedService.updateMembershipApplicationDetailed(membershipApplicationDetailed);
            if (result > 0) {
                return AjaxResult.success("步骤更新成功");
            } else {
                return AjaxResult.error("步骤更新失败");
            }
        } catch (Exception e) {
            logger.error("更新申请步骤失败", e);
            return AjaxResult.error("步骤更新失败");
        }
    }

    /**
     * 提交到下一步
     */
    @PutMapping("/nextStep/{id}")
    public AjaxResult nextStep(@PathVariable Long id, @RequestBody MembershipApplicationDetailed membershipApplicationDetailed)
    {
        try {
            // 先更新当前步骤的数据
            membershipApplicationDetailed.setId(id);
            membershipApplicationDetailed.setUpdateTime(new Date());
            membershipApplicationDetailedService.updateMembershipApplicationDetailed(membershipApplicationDetailed);

            // 然后提交到下一步
            int result = membershipApplicationDetailedService.submitToNextStep(id);
            if (result > 0) {
                // 获取更新后的申请信息
                MembershipApplicationDetailed updatedApplication = membershipApplicationDetailedService.selectMembershipApplicationDetailedById(id);
                return AjaxResult.success("已进入下一步", updatedApplication.getCurrentStep());
            } else {
                return AjaxResult.error("提交失败");
            }
        } catch (Exception e) {
            logger.error("提交到下一步失败", e);
            return AjaxResult.error("提交失败");
        }
    }

    /**
     * 完成申请提交
     */
    @PutMapping("/complete/{id}")
    public AjaxResult complete(@PathVariable Long id, @RequestBody MembershipApplicationDetailed membershipApplicationDetailed)
    {
        try {
            // 先更新最后一步的数据
            membershipApplicationDetailed.setId(id);
            membershipApplicationDetailed.setUpdateTime(new Date());
            membershipApplicationDetailedService.updateMembershipApplicationDetailed(membershipApplicationDetailed);

            // 然后标记为完成
            int result = membershipApplicationDetailedService.completeApplication(id);
            if (result > 0) {
                return AjaxResult.success("申请提交成功，我们将在5个工作日内与您联系");
            } else {
                return AjaxResult.error("申请提交失败");
            }
        } catch (Exception e) {
            logger.error("完成申请提交失败", e);
            return AjaxResult.error("申请提交失败");
        }
    }

    /**
     * 检查申请状态
     */
    @GetMapping("/status/{id}")
    public AjaxResult checkStatus(@PathVariable Long id)
    {
        try {
            MembershipApplicationDetailed application = membershipApplicationDetailedService.selectMembershipApplicationDetailedById(id);
            if (application != null) {
                return AjaxResult.success("查询成功", application);
            } else {
                return AjaxResult.error("申请不存在");
            }
        } catch (Exception e) {
            logger.error("查询申请状态失败", e);
            return AjaxResult.error("查询失败");
        }
    }
}
