<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ContactInfoMapper">
    
    <resultMap type="ContactInfo" id="ContactInfoResult">
        <result property="id"    column="id"    />
        <result property="companyName"    column="company_name"    />
        <result property="address"    column="address"    />
        <result property="phone"    column="phone"    />
        <result property="email"    column="email"    />
        <result property="website"    column="website"    />
        <result property="businessHours"    column="business_hours"    />
        <result property="description"    column="description"    />
        <result property="icon"    column="icon"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectContactInfoVo">
        select id, company_name, address, phone, email, website, business_hours, description, icon, status, create_by, create_time, update_by, update_time, remark from contact_info
    </sql>

    <select id="selectContactInfoList" parameterType="ContactInfo" resultMap="ContactInfoResult">
        <include refid="selectContactInfoVo"/>
        <where>  
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
            <if test="email != null  and email != ''"> and email like concat('%', #{email}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectContactInfoById" parameterType="Long" resultMap="ContactInfoResult">
        <include refid="selectContactInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertContactInfo" parameterType="ContactInfo" useGeneratedKeys="true" keyProperty="id">
        insert into contact_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="address != null">address,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="website != null">website,</if>
            <if test="businessHours != null">business_hours,</if>
            <if test="description != null">description,</if>
            <if test="icon != null">icon,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="address != null">#{address},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="website != null">#{website},</if>
            <if test="businessHours != null">#{businessHours},</if>
            <if test="description != null">#{description},</if>
            <if test="icon != null">#{icon},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateContactInfo" parameterType="ContactInfo">
        update contact_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="address != null">address = #{address},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="website != null">website = #{website},</if>
            <if test="businessHours != null">business_hours = #{businessHours},</if>
            <if test="description != null">description = #{description},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteContactInfoById" parameterType="Long">
        delete from contact_info where id = #{id}
    </delete>

    <delete id="deleteContactInfoByIds" parameterType="String">
        delete from contact_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 