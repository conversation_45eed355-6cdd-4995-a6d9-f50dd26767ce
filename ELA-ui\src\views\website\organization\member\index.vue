<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="nameZh">
        <el-input
          v-model="queryParams.nameZh"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分类" prop="categoryId">
        <el-select v-model="queryParams.categoryId" placeholder="请选择分类" clearable @change="handleCategoryChange">
          <el-option
            v-for="item in categoryOptions"
            :key="item.id"
            :label="item.nameZh"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="成员类型" prop="memberType">
        <el-select v-model="queryParams.memberType" placeholder="请选择成员类型" clearable>
          <el-option
            v-for="item in memberTypeOptions"
            :key="item.id"
            :label="item.nameZh"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['website:organization:member:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['website:organization:member:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['website:organization:member:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['website:organization:member:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="memberList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="头像" align="center" prop="avatarUrl" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.avatarUrl" :width="50" :height="50" v-if="scope.row.avatarUrl"/>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名（中文）" align="center" prop="nameZh" />
      <el-table-column label="姓名（英文）" align="center" prop="nameEn" />
      <el-table-column label="职位（中文）" align="center" prop="positionZh" />
      <el-table-column label="成员类型" align="center" prop="memberType">
        <template slot-scope="scope">
          <span v-if="scope.row.memberTypeInfo">{{ scope.row.memberTypeInfo.nameZh }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="是否领导层" align="center" prop="isLeadership" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.isLeadership"/>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['website:organization:member:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['website:organization:member:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改组织架构成员对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="分类" prop="categoryId">
              <el-select v-model="form.categoryId" placeholder="请选择分类" @change="handleFormCategoryChange">
                <el-option
                  v-for="item in categoryOptions"
                  :key="item.id"
                  :label="item.nameZh"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成员类型" prop="memberType">
              <el-select v-model="form.memberType" placeholder="请选择成员类型">
                <el-option
                  v-for="item in memberTypeOptions"
                  :key="item.id"
                  :label="item.nameZh"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="姓名（中文）" prop="nameZh">
              <el-input v-model="form.nameZh" placeholder="请输入姓名（中文）" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名（英文）" prop="nameEn">
              <el-input v-model="form.nameEn" placeholder="请输入姓名（英文）" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="职位（中文）" prop="positionZh">
              <el-input v-model="form.positionZh" placeholder="请输入职位（中文）" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位（英文）" prop="positionEn">
              <el-input v-model="form.positionEn" placeholder="请输入职位（英文）" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="部门（中文）" prop="departmentZh">
              <el-input v-model="form.departmentZh" placeholder="请输入部门（中文）" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门（英文）" prop="departmentEn">
              <el-input v-model="form.departmentEn" placeholder="请输入部门（英文）" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="公司（中文）" prop="companyZh">
              <el-input v-model="form.companyZh" placeholder="请输入公司（中文）" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司（英文）" prop="companyEn">
              <el-input v-model="form.companyEn" placeholder="请输入公司（英文）" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="个人简介（中文）" prop="bioZh">
          <el-input v-model="form.bioZh" type="textarea" placeholder="请输入个人简介（中文）" />
        </el-form-item>
        <el-form-item label="个人简介（英文）" prop="bioEn">
          <el-input v-model="form.bioEn" type="textarea" placeholder="请输入个人简介（英文）" />
        </el-form-item>
        <el-form-item label="头像" prop="avatarUrl">
          <image-upload 
            v-model="form.avatarUrl" 
            :limit="1" 
            :fileSize="5"
            :fileType="['png', 'jpg', 'jpeg', 'gif']"
          />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="LinkedIn链接" prop="linkedinUrl">
              <el-input v-model="form.linkedinUrl" placeholder="请输入LinkedIn链接" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Facebook链接" prop="facebookUrl">
              <el-input v-model="form.facebookUrl" placeholder="请输入Facebook链接" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="是否领导层" prop="isLeadership">
              <el-radio-group v-model="form.isLeadership">
                <el-radio
                  v-for="dict in dict.type.sys_yes_no"
                  :key="dict.value"
                  :label="parseInt(dict.value)"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOrganizationMember, getOrganizationMember, delOrganizationMember, addOrganizationMember, updateOrganizationMember, getOrganizationCategoryOptions, getMemberTypeOptions, getMemberTypeOptionsByCategoryId } from "@/api/website/organizationMember";
import ImageUpload from "@/components/ImageUpload";

export default {
  name: "OrganizationMember",
  dicts: ['sys_normal_disable', 'sys_yes_no'],
  components: {
    ImageUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 组织架构成员表格数据
      memberList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 分类选项
      categoryOptions: [],
      // 成员类型选项
      memberTypeOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nameZh: null,
        nameEn: null,
        categoryId: null,
        memberType: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        categoryId: [
          { required: true, message: "分类不能为空", trigger: "change" }
        ],
        nameZh: [
          { required: true, message: "姓名（中文）不能为空", trigger: "blur" }
        ],
        nameEn: [
          { required: true, message: "姓名（英文）不能为空", trigger: "blur" }
        ],
        positionZh: [
          { required: true, message: "职位（中文）不能为空", trigger: "blur" }
        ],
        positionEn: [
          { required: true, message: "职位（英文）不能为空", trigger: "blur" }
        ],
        memberType: [
          { required: true, message: "成员类型不能为空", trigger: "change" }
        ],
        sortOrder: [
          { required: true, message: "排序不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getCategoryOptions();
    this.getMemberTypeOptions();
  },
  methods: {
    /** 查询组织架构成员列表 */
    getList() {
      this.loading = true;
      listOrganizationMember(this.queryParams).then(response => {
        this.memberList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取分类选项 */
    getCategoryOptions() {
      getOrganizationCategoryOptions().then(response => {
        this.categoryOptions = response.data;
      });
    },
    /** 获取成员类型选项 */
    getMemberTypeOptions() {
      getMemberTypeOptions().then(response => {
        this.memberTypeOptions = response.data;
      });
    },
    /** 分类change事件 */
    handleCategoryChange(categoryId) {
      if (categoryId) {
        getMemberTypeOptionsByCategoryId(categoryId).then(response => {
          this.memberTypeOptions = response.data;
        });
      } else {
        this.memberTypeOptions = []; // 清空成员类型选项
      }
    },
    /** 表单分类change事件 */
    handleFormCategoryChange(categoryId) {
      if (categoryId) {
        getMemberTypeOptionsByCategoryId(categoryId).then(response => {
          this.memberTypeOptions = response.data;
        });
        // 清空成员类型选择
        this.form.memberType = null;
      } else {
        this.memberTypeOptions = [];
        this.form.memberType = null;
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        categoryId: null,
        nameZh: null,
        nameEn: null,
        positionZh: null,
        positionEn: null,
        departmentZh: null,
        departmentEn: null,
        companyZh: null,
        companyEn: null,
        bioZh: null,
        bioEn: null,
        avatarUrl: null,
        email: null,
        phone: null,
        linkedinUrl: null,
        facebookUrl: null,
        memberType: null,
        isLeadership: 0,
        sortOrder: 0,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加组织架构成员";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getOrganizationMember(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改组织架构成员";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateOrganizationMember(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOrganizationMember(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除组织架构成员编号为"' + ids + '"的数据项？').then(function() {
        return delOrganizationMember(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/organization/member/export', {
        ...this.queryParams
      }, `member_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>