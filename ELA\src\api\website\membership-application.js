import request from '@/utils/request'

// ==================== 旧版本API（保持兼容） ====================
// 提交会员申请表（旧版本，保持兼容）
export function submitMembershipApplication(data) {
  return request({
    url: '/public/membership-application/submit',
    method: 'post',
    data: data
  })
}

// 保存会员申请步骤数据
export function saveMembershipApplicationStep(data) {
  return request({
    url: '/public/membership-application/save-step',
    method: 'post',
    data: data
  })
}

// 获取会员申请数据
export function getMembershipApplication(id) {
  return request({
    url: `/public/membership-application/${id}`,
    method: 'get'
  })
}

// 提交完整的会员申请表
export function submitDetailedMembershipApplication(data) {
  return request({
    url: '/public/membership-application/submit-detailed',
    method: 'post',
    data: data
  })
}

// 根据邮箱获取未完成的申请
export function getIncompleteApplicationByEmail(email) {
  return request({
    url: `/public/membership-application/incomplete/${email}`,
    method: 'get'
  })
}

// ==================== 新版本详细申请API ====================
// 提交详细会员申请表
export function submitDetailedApplication(data) {
  return request({
    url: '/public/membership-application-detailed/submit',
    method: 'post',
    data: data
  })
}

// 保存申请草稿
export function saveDraftApplication(data) {
  return request({
    url: '/public/membership-application-detailed/saveDraft',
    method: 'post',
    data: data
  })
}

// 获取申请详情
export function getDetailedApplication(id) {
  return request({
    url: `/public/membership-application-detailed/${id}`,
    method: 'get'
  })
}

// 更新申请步骤
export function updateApplicationStep(id, currentStep, data) {
  return request({
    url: `/public/membership-application-detailed/updateStep?id=${id}&currentStep=${currentStep}`,
    method: 'put',
    data: data
  })
}

// 提交到下一步
export function submitToNextStep(id, data) {
  return request({
    url: `/public/membership-application-detailed/nextStep/${id}`,
    method: 'put',
    data: data
  })
}

// 完成申请提交
export function completeApplication(id, data) {
  return request({
    url: `/public/membership-application-detailed/complete/${id}`,
    method: 'put',
    data: data
  })
}

// 检查申请状态
export function checkApplicationStatus(id) {
  return request({
    url: `/public/membership-application-detailed/status/${id}`,
    method: 'get'
  })
}