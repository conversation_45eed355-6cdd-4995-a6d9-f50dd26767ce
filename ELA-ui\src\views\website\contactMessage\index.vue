<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input
          v-model="queryParams.email"
          placeholder="请输入邮箱"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="主题" prop="subject">
        <el-input
          v-model="queryParams.subject"
          placeholder="请输入主题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="处理状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="处理状态" clearable>
          <el-option label="未处理" value="0" />
          <el-option label="已处理" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['website:contactMessage:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['website:contactMessage:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="contactMessageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="消息ID" align="center" prop="id" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="邮箱" align="center" prop="email" />
      <el-table-column label="主题" align="center" prop="subject" :show-overflow-tooltip="true" />
      <el-table-column label="消息内容" align="center" prop="message" :show-overflow-tooltip="true" />
      <el-table-column label="处理状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === '0' ? 'danger' : 'success'">
            {{ scope.row.status === '0' ? '未处理' : '已处理' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="处理人" align="center" prop="processedBy" />
      <el-table-column label="处理时间" align="center" prop="processedTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.processedTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['website:contactMessage:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleProcess(scope.row)"
            v-if="scope.row.status === '0'"
            v-hasPermi="['website:contactMessage:edit']"
          >标记处理</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['website:contactMessage:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="form.name" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="主题" prop="subject">
          <el-input v-model="form.subject" readonly />
        </el-form-item>
        <el-form-item label="消息内容" prop="message">
          <el-input v-model="form.message" type="textarea" :rows="6" readonly />
        </el-form-item>
        <el-form-item label="处理状态" prop="status">
          <el-tag :type="form.status === '0' ? 'danger' : 'success'">
            {{ form.status === '0' ? '未处理' : '已处理' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="处理人" prop="processedBy" v-if="form.processedBy">
          <el-input v-model="form.processedBy" readonly />
        </el-form-item>
        <el-form-item label="处理时间" prop="processedTime" v-if="form.processedTime">
          <el-input v-model="form.processedTime" readonly />
        </el-form-item>
        <el-form-item label="处理备注" prop="processedNote" v-if="form.processedNote">
          <el-input v-model="form.processedNote" type="textarea" :rows="3" readonly />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 处理对话框 -->
    <el-dialog title="标记为已处理" :visible.sync="processOpen" width="500px" append-to-body>
      <el-form ref="processForm" :model="processForm" :rules="processRules" label-width="100px">
        <el-form-item label="处理备注" prop="processedNote">
          <el-input v-model="processForm.processedNote" type="textarea" :rows="4" placeholder="请输入处理备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitProcess">确 定</el-button>
        <el-button @click="cancelProcess">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listContactMessage, getContactMessage, delContactMessage, updateContactMessage, exportContactMessage } from "@/api/website/contactMessage";

export default {
  name: "ContactMessage",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 联系消息表格数据
      contactMessageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示处理弹出层
      processOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        email: null,
        subject: null,
        status: null
      },
      // 表单参数
      form: {},
      // 处理表单参数
      processForm: {
        id: null,
        processedNote: ""
      },
      // 处理表单校验
      processRules: {
        processedNote: [
          { required: true, message: "处理备注不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询联系消息列表 */
    getList() {
      this.loading = true;
      listContactMessage(this.queryParams).then(response => {
        this.contactMessageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消处理按钮
    cancelProcess() {
      this.processOpen = false;
      this.resetProcess();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        email: null,
        subject: null,
        message: null,
        status: null,
        processedBy: null,
        processedTime: null,
        processedNote: null
      };
    },
    // 处理表单重置
    resetProcess() {
      this.processForm = {
        id: null,
        processedNote: ""
      };
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id || this.ids
      getContactMessage(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "查看联系消息";
      });
    },
    /** 处理按钮操作 */
    handleProcess(row) {
      this.resetProcess();
      this.processForm.id = row.id;
      this.processOpen = true;
    },
    /** 提交处理 */
    submitProcess() {
      this.$refs["processForm"].validate(valid => {
        if (valid) {
          // 格式化日期为后端期望的格式 yyyy-MM-dd HH:mm:ss
          const now = new Date();
          const processedTime = now.getFullYear() + '-' + 
            String(now.getMonth() + 1).padStart(2, '0') + '-' + 
            String(now.getDate()).padStart(2, '0') + ' ' + 
            String(now.getHours()).padStart(2, '0') + ':' + 
            String(now.getMinutes()).padStart(2, '0') + ':' + 
            String(now.getSeconds()).padStart(2, '0');
          
          const data = {
            id: this.processForm.id,
            status: "1",
            processedBy: this.$store.getters.name,
            processedTime: processedTime,
            processedNote: this.processForm.processedNote
          };
          updateContactMessage(data).then(response => {
            this.$modal.msgSuccess("处理成功");
            this.processOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除联系消息编号为"' + ids + '"的数据项？').then(function() {
        return delContactMessage(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('website/contactMessage/export', {
        ...this.queryParams
      }, `contactMessage_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>