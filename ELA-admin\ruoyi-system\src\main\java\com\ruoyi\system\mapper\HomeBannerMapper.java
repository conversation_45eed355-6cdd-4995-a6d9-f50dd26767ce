package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.HomeBanner;

/**
 * 首页BannerMapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface HomeBannerMapper 
{
    /**
     * 查询首页Banner
     * 
     * @param id 首页Banner主键
     * @return 首页Banner
     */
    public HomeBanner selectHomeBannerById(Long id);

    /**
     * 查询首页Banner列表
     * 
     * @param homeBanner 首页Banner
     * @return 首页Banner集合
     */
    public List<HomeBanner> selectHomeBannerList(HomeBanner homeBanner);

    /**
     * 查询启用的首页Banner列表（用于前端显示）
     * 
     * @return 首页Banner集合
     */
    public List<HomeBanner> selectEnabledHomeBannerList();

    /**
     * 新增首页Banner
     * 
     * @param homeBanner 首页Banner
     * @return 结果
     */
    public int insertHomeBanner(HomeBanner homeBanner);

    /**
     * 修改首页Banner
     * 
     * @param homeBanner 首页Banner
     * @return 结果
     */
    public int updateHomeBanner(HomeBanner homeBanner);

    /**
     * 删除首页Banner
     * 
     * @param id 首页Banner主键
     * @return 结果
     */
    public int deleteHomeBannerById(Long id);

    /**
     * 批量删除首页Banner
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeBannerByIds(Long[] ids);
} 