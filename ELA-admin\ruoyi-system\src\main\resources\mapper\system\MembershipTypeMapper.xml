<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MembershipTypeMapper">
    
    <resultMap type="MembershipType" id="MembershipTypeResult">
        <result property="id"    column="id"    />
        <result property="typeCode"    column="type_code"    />
        <result property="nameZh"    column="name_zh"    />
        <result property="nameEn"    column="name_en"    />
        <result property="descriptionZh"    column="description_zh"    />
        <result property="descriptionEn"    column="description_en"    />
        <result property="annualFee"    column="annual_fee"    />
        <result property="icon"    column="icon"    />
        <result property="benefitsZh"    column="benefits_zh"    typeHandler="com.ruoyi.framework.config.JsonListTypeHandler"/>
        <result property="benefitsEn"    column="benefits_en"    typeHandler="com.ruoyi.framework.config.JsonListTypeHandler"/>
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMembershipTypeVo">
        select id, type_code, name_zh, name_en, description_zh, description_en, annual_fee, icon, benefits_zh, benefits_en, sort_order, status, create_by, create_time, update_by, update_time, remark from membership_types
    </sql>

    <select id="selectMembershipTypeList" parameterType="MembershipType" resultMap="MembershipTypeResult">
        <include refid="selectMembershipTypeVo"/>
        <where>  
            <if test="typeCode != null  and typeCode != ''"> and type_code = #{typeCode}</if>
            <if test="nameZh != null  and nameZh != ''"> and name_zh like concat('%', #{nameZh}, '%')</if>
            <if test="nameEn != null  and nameEn != ''"> and name_en like concat('%', #{nameEn}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMembershipTypeById" parameterType="Long" resultMap="MembershipTypeResult">
        <include refid="selectMembershipTypeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMembershipType" parameterType="MembershipType" useGeneratedKeys="true" keyProperty="id">
        insert into membership_types
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="typeCode != null and typeCode != ''">type_code,</if>
            <if test="nameZh != null and nameZh != ''">name_zh,</if>
            <if test="nameEn != null and nameEn != ''">name_en,</if>
            <if test="descriptionZh != null">description_zh,</if>
            <if test="descriptionEn != null">description_en,</if>
            <if test="annualFee != null">annual_fee,</if>
            <if test="icon != null">icon,</if>
            <if test="benefitsZh != null">benefits_zh,</if>
            <if test="benefitsEn != null">benefits_en,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="typeCode != null and typeCode != ''">#{typeCode},</if>
            <if test="nameZh != null and nameZh != ''">#{nameZh},</if>
            <if test="nameEn != null and nameEn != ''">#{nameEn},</if>
            <if test="descriptionZh != null">#{descriptionZh},</if>
            <if test="descriptionEn != null">#{descriptionEn},</if>
            <if test="annualFee != null">#{annualFee},</if>
            <if test="icon != null">#{icon},</if>
            <if test="benefitsZh != null">#{benefitsZh,typeHandler=com.ruoyi.framework.config.JsonListTypeHandler},</if>
            <if test="benefitsEn != null">#{benefitsEn,typeHandler=com.ruoyi.framework.config.JsonListTypeHandler},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMembershipType" parameterType="MembershipType">
        update membership_types
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeCode != null and typeCode != ''">type_code = #{typeCode},</if>
            <if test="nameZh != null and nameZh != ''">name_zh = #{nameZh},</if>
            <if test="nameEn != null and nameEn != ''">name_en = #{nameEn},</if>
            <if test="descriptionZh != null">description_zh = #{descriptionZh},</if>
            <if test="descriptionEn != null">description_en = #{descriptionEn},</if>
            <if test="annualFee != null">annual_fee = #{annualFee},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="benefitsZh != null">benefits_zh = #{benefitsZh,typeHandler=com.ruoyi.framework.config.JsonListTypeHandler},</if>
            <if test="benefitsEn != null">benefits_en = #{benefitsEn,typeHandler=com.ruoyi.framework.config.JsonListTypeHandler},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMembershipTypeById" parameterType="Long">
        delete from membership_types where id = #{id}
    </delete>

    <delete id="deleteMembershipTypeByIds" parameterType="String">
        delete from membership_types where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 