package com.ruoyi.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.LeadershipMessage;
import com.ruoyi.system.service.ILeadershipMessageService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 领导留言Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/website/leadershipMessage")
public class LeadershipMessageController extends BaseController
{
    @Autowired
    private ILeadershipMessageService leadershipMessageService;

    /**
     * 查询领导留言列表
     */
    @PreAuthorize("@ss.hasPermi('system:leadershipMessage:list')")
    @GetMapping("/list")
    public TableDataInfo list(LeadershipMessage leadershipMessage)
    {
        startPage();
        List<LeadershipMessage> list = leadershipMessageService.selectLeadershipMessageList(leadershipMessage);
        return getDataTable(list);
    }

    /**
     * 导出领导留言列表
     */
    @PreAuthorize("@ss.hasPermi('system:leadershipMessage:export')")
    @Log(title = "领导留言", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LeadershipMessage leadershipMessage)
    {
        List<LeadershipMessage> list = leadershipMessageService.selectLeadershipMessageList(leadershipMessage);
        ExcelUtil<LeadershipMessage> util = new ExcelUtil<LeadershipMessage>(LeadershipMessage.class);
        util.exportExcel(response, list, "领导留言数据");
    }

    /**
     * 获取领导留言详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:leadershipMessage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leadershipMessageService.selectLeadershipMessageById(id));
    }

    /**
     * 新增领导留言
     */
    @PreAuthorize("@ss.hasPermi('system:leadershipMessage:add')")
    @Log(title = "领导留言", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeadershipMessage leadershipMessage)
    {
        return toAjax(leadershipMessageService.insertLeadershipMessage(leadershipMessage));
    }

    /**
     * 修改领导留言
     */
    @PreAuthorize("@ss.hasPermi('system:leadershipMessage:edit')")
    @Log(title = "领导留言", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeadershipMessage leadershipMessage)
    {
        return toAjax(leadershipMessageService.updateLeadershipMessage(leadershipMessage));
    }

    /**
     * 删除领导留言
     */
    @PreAuthorize("@ss.hasPermi('system:leadershipMessage:remove')")
    @Log(title = "领导留言", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leadershipMessageService.deleteLeadershipMessageByIds(ids));
    }
} 