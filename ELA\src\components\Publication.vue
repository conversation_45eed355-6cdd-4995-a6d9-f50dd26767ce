<template>
  <div class="publication-page">
    <!-- Hero Banner -->
    <section class="hero-banner">
      <div class="hero-overlay"></div>
      <div class="hero-content">
        <div class="container">
          <h1>{{ currentLang === 'zh' ? '資訊' : 'Publication' }}</h1>
          <p class="hero-subtitle">
            {{ currentLang === 'zh'
              ? '瀏覽我們的最新資訊，掌握行業動態和發展趨勢'
              : 'Browse our latest information, stay updated with industry trends and developments'
            }}
          </p>
        </div>
      </div>
    </section>

    <div class="main-container">
      <!-- Publications Section -->
      <section class="section">
        <div class="section-header">
          <h2>{{ currentLang === 'zh' ? '最新資訊' : 'Latest Publications' }}</h2>
        </div>

        <div class="publications-list" v-loading="loading">
          <div
            v-for="publication in publications"
            :key="publication.id"
            class="publication-card"
            @click="viewPublication(publication)"
          >
            <div class="publication-image">
              <img :src="publication.coverImageUrl" :alt="publication.title" />
            </div>
            <div class="publication-content">
              <div class="publication-meta">
                <span class="publication-date">{{ formatDate(publication.publishTime) }}</span>
                <span class="publication-status" :class="publication.status === '0' ? 'active' : 'inactive'">
                  {{ publication.status === '0' ? (currentLang === 'zh' ? '已發布' : 'Published') : (currentLang === 'zh' ? '草稿' : 'Draft') }}
                </span>
              </div>
              <h3 class="publication-title">{{ publication.title }}</h3>
              <p class="publication-summary">{{ publication.summary || publication.content }}</p>
              <div class="publication-footer">
                <span class="read-more">
                  {{ currentLang === 'zh' ? '閱讀更多' : 'Read More' }}
                  <i class="fas fa-arrow-right"></i>
                </span>
              </div>
            </div>
          </div>

          <!-- 如果没有数据，显示空状态 -->
          <div v-if="!loading && publications.length === 0" class="empty-state">
            <p>{{ currentLang === 'zh' ? '暫無資訊' : 'No publications' }}</p>
          </div>
        </div>
      </section>
    </div>

    <!-- Publication Detail Modal -->
    <div v-if="selectedPublication" class="publication-modal" @click="closeModal">
      <div class="modal-content" @click.stop>
        <button class="modal-close" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
        <div class="modal-header">
          <img v-if="selectedPublication.contentImageUrl" :src="selectedPublication.contentImageUrl" :alt="selectedPublication.title" class="modal-image" />
          <div class="modal-meta">
            <span class="modal-date">{{ formatDate(selectedPublication.publishTime) }}</span>
            <h2 class="modal-title">{{ selectedPublication.title }}</h2>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-content-text" v-html="selectedPublication.content"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getPublicPublications } from '@/api/website/publications'

export default {
  name: 'Publication',
  props: {
    currentLang: {
      type: String,
      default: 'zh'
    }
  },
  data() {
    return {
      publications: [],
      loading: false,
      selectedPublication: null
    }
  },
  mounted() {
    this.loadPublications()
  },
  methods: {
    async loadPublications() {
      try {
        this.loading = true
        const response = await getPublicPublications()

        if (response.code === 200) {
          // 按排序值和发布时间排序
          this.publications = (response.data || []).sort((a, b) => {
            // 首先按排序值排序（升序）
            if (a.sortOrder !== b.sortOrder) {
              return (a.sortOrder || 999) - (b.sortOrder || 999)
            }
            // 然后按发布时间排序（降序，最新的在前）
            return new Date(b.publishTime) - new Date(a.publishTime)
          })
        }
      } catch (error) {
        console.error('加载资讯数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      if (this.currentLang === 'zh') {
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      } else {
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      }
    },

    viewPublication(publication) {
      this.selectedPublication = publication
      document.body.style.overflow = 'hidden'
    },

    closeModal() {
      this.selectedPublication = null
      document.body.style.overflow = 'auto'
    }
  }
}
</script>

<style scoped>
* {
  box-sizing: border-box;
}

.publication-page {
  background-color: #fff;
}

.hero-banner {
  height: 500px;
  background-image: url('~@/assets/banner/publication.png');
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0;
  overflow: hidden;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.hero-content h1 {
  font-size: 4rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.2rem;
  font-weight: 300;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.6;
  opacity: 0.9;
  max-width: 700px;
  margin: 0 auto 2rem auto;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.section {
  margin-bottom: 5rem;
  padding: 3rem 0;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 3rem;
  gap: 1rem;
}

.section-header h2 {
  font-size: 2rem;
  font-weight: 800;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.02em;
}

/* Publications List */
.publications-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.publication-card {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #e2e8f0;
}

.publication-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.publication-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.publication-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.publication-card:hover .publication-image img {
  transform: scale(1.05);
}

.publication-content {
  padding: 1.5rem;
}

.publication-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.publication-date {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.publication-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.publication-status.active {
  background: #dcfce7;
  color: #166534;
}

.publication-status.inactive {
  background: #fef3c7;
  color: #92400e;
}

.publication-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.publication-summary {
  font-size: 0.95rem;
  color: #64748b;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.publication-footer {
  display: flex;
  justify-content: flex-end;
}

.read-more {
  color: #0056b3;
  font-weight: 600;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.3s ease;
}

.publication-card:hover .read-more {
  color: #004494;
}

.read-more i {
  transition: transform 0.3s ease;
}

.publication-card:hover .read-more i {
  transform: translateX(3px);
}

.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 5rem;
  color: #64748b;
  font-size: 1.3rem;
}

.empty-state p {
  margin: 0;
}

/* Publication Modal */
.publication-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: background 0.3s ease;
}

.modal-close:hover {
  background: rgba(0, 0, 0, 0.7);
}

.modal-header {
  position: relative;
}

.modal-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 12px 12px 0 0;
}

.modal-meta {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 2rem;
}

.modal-date {
  font-size: 0.875rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
  display: block;
}

.modal-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.3;
}

.modal-body {
  padding: 2rem;
}

.modal-content-text {
  font-size: 1rem;
  line-height: 1.8;
  color: #374151;
}

.modal-content-text h1,
.modal-content-text h2,
.modal-content-text h3,
.modal-content-text h4,
.modal-content-text h5,
.modal-content-text h6 {
  color: #1e293b;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.modal-content-text p {
  margin-bottom: 1rem;
}

.modal-content-text img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .publications-list {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .publication-card {
    min-width: unset;
  }

  .modal-content {
    margin: 1rem;
    max-height: 85vh;
  }

  .modal-meta {
    padding: 1.5rem;
  }

  .modal-title {
    font-size: 1.5rem;
  }

  .modal-body {
    padding: 1.5rem;
  }
}
</style>
