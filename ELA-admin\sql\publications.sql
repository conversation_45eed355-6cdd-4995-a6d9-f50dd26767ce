-- 创建资讯表
CREATE TABLE `publications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '资讯ID',
  `title` varchar(255) NOT NULL COMMENT '资讯标题',
  `cover_image_url` varchar(500) DEFAULT NULL COMMENT '封面图片URL',
  `content_image_url` varchar(500) DEFAULT NULL COMMENT '内容图片URL',
  `content` longtext COMMENT '资讯详情内容',
  `summary` text COMMENT '资讯摘要',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序号',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='资讯表';

-- 插入示例数据
INSERT INTO `publications` (`title`, `cover_image_url`, `content_image_url`, `content`, `summary`, `publish_time`, `sort_order`, `status`, `create_by`, `create_time`, `remark`) VALUES
('香港物流协会2024年度报告发布', 'https://example.com/cover1.jpg', 'https://example.com/content1.jpg', '<p>香港物流协会2024年度报告正式发布，报告详细总结了协会在过去一年的工作成果和发展成就...</p>', '香港物流协会2024年度报告正式发布，总结协会年度工作成果', '2024-12-01 10:00:00', 1, '0', 'admin', '2024-12-01 10:00:00', '年度报告'),
('物流行业数字化转型趋势分析', 'https://example.com/cover2.jpg', 'https://example.com/content2.jpg', '<p>随着科技的快速发展，物流行业正在经历前所未有的数字化转型...</p>', '深度分析物流行业数字化转型的发展趋势和机遇', '2024-11-28 14:30:00', 2, '0', 'admin', '2024-11-28 14:30:00', '行业分析'),
('绿色物流发展新机遇', 'https://example.com/cover3.jpg', 'https://example.com/content3.jpg', '<p>在全球可持续发展的大背景下，绿色物流成为行业发展的重要方向...</p>', '探讨绿色物流在可持续发展中的重要作用和发展机遇', '2024-11-25 09:15:00', 3, '0', 'admin', '2024-11-25 09:15:00', '绿色发展');