-- 申请流程表
CREATE TABLE `application_process` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '流程ID',
  `step_number` int(11) NOT NULL COMMENT '步骤编号',
  `title_zh` varchar(100) NOT NULL COMMENT '中文标题',
  `title_en` varchar(100) NOT NULL COMMENT '英文标题',
  `description_zh` text COMMENT '中文描述',
  `description_en` text COMMENT '英文描述',
  `icon` varchar(100) DEFAULT NULL COMMENT '图标类名',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='申请流程表';

-- 插入默认数据
INSERT INTO `application_process` (`step_number`, `title_zh`, `title_en`, `description_zh`, `description_en`, `icon`, `sort_order`, `status`, `create_by`, `create_time`) VALUES
(1, '提交申請', 'Submit Application', '填寫完整的會員申請表並提交所需文件', 'Complete membership application form and submit required documents', 'fas fa-file-alt', 1, '0', 'admin', NOW()),
(2, '資格審核', 'Qualification Review', '協會秘書處進行初步資格審核', 'Preliminary qualification review by association secretariat', 'fas fa-search', 2, '0', 'admin', NOW()),
(3, '理事會審批', 'Council Approval', '理事會會議討論並投票決定', 'Council meeting discussion and voting decision', 'fas fa-users', 3, '0', 'admin', NOW()),
(4, '繳費確認', 'Payment Confirmation', '繳納會員費用並完成入會手續', 'Pay membership fee and complete enrollment procedures', 'fas fa-credit-card', 4, '0', 'admin', NOW());

-- 申请流程菜单 SQL
-- 获取网站管理菜单ID
SELECT @websiteMenuId := menu_id FROM sys_menu WHERE menu_name = '网站管理' AND parent_id = '0' LIMIT 1;

-- 添加申请流程菜单（网站管理的子菜单）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('申请流程', @websiteMenuId, '8', 'application-process', 'website/application-process/index', 1, 0, 'C', '0', '0', 'website:application-process:list', 'fa fa-list-ol', 'admin', sysdate(), '', null, '申请流程菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('申请流程查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'website:application-process:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('申请流程新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'website:application-process:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('申请流程修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'website:application-process:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('申请流程删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'website:application-process:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('申请流程导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'website:application-process:export',       '#', 'admin', sysdate(), '', null, ''); 