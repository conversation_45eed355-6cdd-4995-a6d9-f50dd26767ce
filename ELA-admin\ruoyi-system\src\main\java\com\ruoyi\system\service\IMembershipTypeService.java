package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.MembershipType;

/**
 * 会员类型Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
public interface IMembershipTypeService 
{
    /**
     * 查询会员类型
     * 
     * @param id 会员类型主键
     * @return 会员类型
     */
    public MembershipType selectMembershipTypeById(Long id);

    /**
     * 查询会员类型列表
     * 
     * @param membershipType 会员类型
     * @return 会员类型集合
     */
    public List<MembershipType> selectMembershipTypeList(MembershipType membershipType);

    /**
     * 新增会员类型
     * 
     * @param membershipType 会员类型
     * @return 结果
     */
    public int insertMembershipType(MembershipType membershipType);

    /**
     * 修改会员类型
     * 
     * @param membershipType 会员类型
     * @return 结果
     */
    public int updateMembershipType(MembershipType membershipType);

    /**
     * 批量删除会员类型
     * 
     * @param ids 需要删除的会员类型主键集合
     * @return 结果
     */
    public int deleteMembershipTypeByIds(Long[] ids);

    /**
     * 删除会员类型信息
     * 
     * @param id 会员类型主键
     * @return 结果
     */
    public int deleteMembershipTypeById(Long id);
} 