import request from '@/utils/request'

// 查询首页Hero内容列表
export function listHeroContent(query) {
  return request({
    url: '/website/hero-content/list',
    method: 'get',
    params: query
  })
}

// 查询首页Hero内容详细
export function getHeroContent(id) {
  return request({
    url: '/website/hero-content/' + id,
    method: 'get'
  })
}

// 新增首页Hero内容
export function addHeroContent(data) {
  return request({
    url: '/website/hero-content',
    method: 'post',
    data: data
  })
}

// 修改首页Hero内容
export function updateHeroContent(data) {
  return request({
    url: '/website/hero-content',
    method: 'put',
    data: data
  })
}

// 删除首页Hero内容
export function delHeroContent(id) {
  return request({
    url: '/website/hero-content/' + id,
    method: 'delete'
  })
} 