<template>
  <section class="full-width-banner" :style="{ backgroundImage: 'url(' + backgroundImage + ')' }">
    <div class="banner-content">
      <h2>致力于成为<br><strong>“行业领跑者”</strong></h2>
      <p>为行业发展、社会进步贡献力量</p>
    </div>
  </section>
</template>

<script>
export default {
  name: 'FullWidthBanner',
  data() {
    return {
      backgroundImage: 'https://www.sinochemlt.com/Portals/290/Skins/sinochem-about/images/img1.jpg'
    }
  }
}
</script>

<style scoped>
.full-width-banner {
  height: 400px;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  padding: 20px;
  position: relative;
}

.full-width-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.banner-content {
  position: relative;
  z-index: 1;
}

.banner-content h2 {
  font-size: 3rem;
  font-weight: 300;
  line-height: 1.3;
}

.banner-content h2 strong {
  font-weight: 700;
}

.banner-content p {
  font-size: 1.25rem;
  margin-top: 1rem;
}
</style> 