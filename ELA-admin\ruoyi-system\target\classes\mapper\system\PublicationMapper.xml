<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.PublicationMapper">
    
    <resultMap type="Publication" id="PublicationResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="coverImageUrl"    column="cover_image_url"    />
        <result property="contentImageUrl"    column="content_image_url"    />
        <result property="content"    column="content"    />
        <result property="summary"    column="summary"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPublicationVo">
        select id, title, cover_image_url, content_image_url, content, summary, publish_time, sort_order, status, create_by, create_time, update_by, update_time, remark from publications
    </sql>

    <select id="selectPublicationList" parameterType="Publication" resultMap="PublicationResult">
        <include refid="selectPublicationVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="publishTime != null "> and publish_time &gt;= #{publishTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, publish_time desc
    </select>
    
    <select id="selectPublicationById" parameterType="Long" resultMap="PublicationResult">
        <include refid="selectPublicationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPublication" parameterType="Publication" useGeneratedKeys="true" keyProperty="id">
        insert into publications
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="coverImageUrl != null">cover_image_url,</if>
            <if test="contentImageUrl != null">content_image_url,</if>
            <if test="content != null">content,</if>
            <if test="summary != null">summary,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="coverImageUrl != null">#{coverImageUrl},</if>
            <if test="contentImageUrl != null">#{contentImageUrl},</if>
            <if test="content != null">#{content},</if>
            <if test="summary != null">#{summary},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePublication" parameterType="Publication">
        update publications
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="coverImageUrl != null">cover_image_url = #{coverImageUrl},</if>
            <if test="contentImageUrl != null">content_image_url = #{contentImageUrl},</if>
            <if test="content != null">content = #{content},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePublicationById" parameterType="Long">
        delete from publications where id = #{id}
    </delete>

    <delete id="deletePublicationByIds" parameterType="String">
        delete from publications where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>