<template>
  <section class="team-grid section">
    <div class="container">
      <div class="section-title">
        <h2 v-if="section.config.title">{{ section.config.title }}</h2>
        <p v-if="section.config.subtitle">{{ section.config.subtitle }}</p>
      </div>
      
      <div class="team-members">
        <div v-for="member in section.config.members" :key="member.id" class="member-card">
          <div class="member-avatar">
            <img :src="member.avatar" :alt="member.name" />
          </div>
          <div class="member-info">
            <h3 class="member-name">{{ member.name }}</h3>
            <p class="member-position">{{ member.position }}</p>
            <p class="member-description">{{ member.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'TeamGrid',
  props: {
    section: {
      type: Object,
      required: true
    }
  }
}
</script>

<style scoped>
.team-grid {
  /* uses global section padding */
}

.team-members {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.member-card {
  background: rgba(18, 18, 36, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: 30px;
  text-align: center;
  transition: all 0.3s ease-out;
  backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}

.member-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(0, 240, 255, 0.15) 0%, rgba(0, 240, 255, 0) 60%);
  transform: scale(0);
  transition: transform 0.5s ease-out;
  z-index: 0;
}

.member-card:hover::before {
  transform: scale(1);
}

.member-card:hover {
  transform: translateY(-8px);
  border-color: var(--primary-color);
  box-shadow: 0 10px 30px rgba(0, 240, 255, 0.15);
}

.member-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  margin: 0 auto 24px auto;
  position: relative;
  z-index: 1;
  border: 4px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease-out;
}

.member-card:hover .member-avatar {
  border-color: var(--primary-color);
  box-shadow: 0 0 20px var(--primary-color);
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.member-info {
  position: relative;
  z-index: 1;
}

.member-name {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-heading);
  margin-bottom: 4px;
}

.member-position {
  font-size: 16px;
  color: var(--primary-color);
  margin-bottom: 16px;
  font-weight: 500;
}

.member-description {
  font-size: 14px;
  color: var(--text-dark);
  line-height: 1.7;
}

@media (max-width: 768px) {
  .team-members {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

</style> 