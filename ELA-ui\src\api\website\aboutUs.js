import request from '@/utils/request'

// 查询关于我们列表
export function listAboutUs(query) {
  return request({
    url: '/website/aboutUs/list',
    method: 'get',
    params: query
  })
}

// 查询关于我们详细
export function getAboutUs(id) {
  return request({
    url: '/website/aboutUs/' + id,
    method: 'get'
  })
}

// 新增关于我们
export function addAboutUs(data) {
  return request({
    url: '/website/aboutUs',
    method: 'post',
    data: data
  })
}

// 修改关于我们
export function updateAboutUs(data) {
  return request({
    url: '/website/aboutUs',
    method: 'put',
    data: data
  })
}

// 删除关于我们
export function delAboutUs(id) {
  return request({
    url: '/website/aboutUs/' + id,
    method: 'delete'
  })
} 