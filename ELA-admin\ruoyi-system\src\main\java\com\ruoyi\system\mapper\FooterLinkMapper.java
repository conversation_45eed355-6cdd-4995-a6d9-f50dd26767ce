package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.FooterLink;

/**
 * 页脚链接Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface FooterLinkMapper 
{
    /**
     * 查询页脚链接
     * 
     * @param id 页脚链接主键
     * @return 页脚链接
     */
    public FooterLink selectFooterLinkById(Long id);

    /**
     * 查询页脚链接列表
     * 
     * @param footerLink 页脚链接
     * @return 页脚链接集合
     */
    public List<FooterLink> selectFooterLinkList(FooterLink footerLink);

    /**
     * 新增页脚链接
     * 
     * @param footerLink 页脚链接
     * @return 结果
     */
    public int insertFooterLink(FooterLink footerLink);

    /**
     * 修改页脚链接
     * 
     * @param footerLink 页脚链接
     * @return 结果
     */
    public int updateFooterLink(FooterLink footerLink);

    /**
     * 删除页脚链接
     * 
     * @param id 页脚链接主键
     * @return 结果
     */
    public int deleteFooterLinkById(Long id);

    /**
     * 批量删除页脚链接
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFooterLinkByIds(Long[] ids);
} 