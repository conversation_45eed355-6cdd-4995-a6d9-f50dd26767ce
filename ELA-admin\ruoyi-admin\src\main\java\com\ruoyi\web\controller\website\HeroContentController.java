package com.ruoyi.web.controller.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HeroContent;
import com.ruoyi.system.service.IHeroContentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 首页Hero内容Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/website/hero-content")
public class HeroContentController extends BaseController
{
    @Autowired
    private IHeroContentService heroContentService;

    /**
     * 查询首页Hero内容列表
     */
    @PreAuthorize("@ss.hasPermi('website:hero-content:list')")
    @GetMapping("/list")
    public TableDataInfo list(HeroContent heroContent)
    {
        startPage();
        List<HeroContent> list = heroContentService.selectHeroContentList(heroContent);
        return getDataTable(list);
    }

    /**
     * 导出首页Hero内容列表
     */
    @PreAuthorize("@ss.hasPermi('website:hero-content:export')")
    @Log(title = "首页Hero内容", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HeroContent heroContent)
    {
        List<HeroContent> list = heroContentService.selectHeroContentList(heroContent);
        ExcelUtil<HeroContent> util = new ExcelUtil<HeroContent>(HeroContent.class);
        util.exportExcel(response, list, "首页Hero内容数据");
    }

    /**
     * 获取首页Hero内容详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:hero-content:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(heroContentService.selectHeroContentById(id));
    }

    /**
     * 新增首页Hero内容
     */
    @PreAuthorize("@ss.hasPermi('website:hero-content:add')")
    @Log(title = "首页Hero内容", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HeroContent heroContent)
    {
        return toAjax(heroContentService.insertHeroContent(heroContent));
    }

    /**
     * 修改首页Hero内容
     */
    @PreAuthorize("@ss.hasPermi('website:hero-content:edit')")
    @Log(title = "首页Hero内容", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HeroContent heroContent)
    {
        return toAjax(heroContentService.updateHeroContent(heroContent));
    }

    /**
     * 删除首页Hero内容
     */
    @PreAuthorize("@ss.hasPermi('website:hero-content:remove')")
    @Log(title = "首页Hero内容", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(heroContentService.deleteHeroContentByIds(ids));
    }
} 