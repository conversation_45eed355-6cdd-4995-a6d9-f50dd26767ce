<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.OrganizationCategoryMapper">
    
    <resultMap type="OrganizationCategory" id="OrganizationCategoryResult">
        <result property="id"    column="id"    />
        <result property="nameZh"    column="name_zh"    />
        <result property="nameEn"    column="name_en"    />
        <result property="descriptionZh"    column="description_zh"    />
        <result property="descriptionEn"    column="description_en"    />
        <result property="heroTitleZh"    column="hero_title_zh"    />
        <result property="heroTitleEn"    column="hero_title_en"    />
        <result property="heroSubtitleZh"    column="hero_subtitle_zh"    />
        <result property="heroSubtitleEn"    column="hero_subtitle_en"    />
        <result property="heroImageUrl"    column="hero_image_url"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOrganizationCategoryVo">
        select id, name_zh, name_en, description_zh, description_en, hero_title_zh, hero_title_en, hero_subtitle_zh, hero_subtitle_en, hero_image_url, sort_order, status, create_by, create_time, update_by, update_time, remark from organization_categories
    </sql>

    <select id="selectOrganizationCategoryList" parameterType="OrganizationCategory" resultMap="OrganizationCategoryResult">
        <include refid="selectOrganizationCategoryVo"/>
        <where>  
            <if test="nameZh != null  and nameZh != ''"> and name_zh like concat('%', #{nameZh}, '%')</if>
            <if test="nameEn != null  and nameEn != ''"> and name_en like concat('%', #{nameEn}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectOrganizationCategoryById" parameterType="Long" resultMap="OrganizationCategoryResult">
        <include refid="selectOrganizationCategoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOrganizationCategory" parameterType="OrganizationCategory" useGeneratedKeys="true" keyProperty="id">
        insert into organization_categories
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nameZh != null and nameZh != ''">name_zh,</if>
            <if test="nameEn != null and nameEn != ''">name_en,</if>
            <if test="descriptionZh != null">description_zh,</if>
            <if test="descriptionEn != null">description_en,</if>
            <if test="heroTitleZh != null">hero_title_zh,</if>
            <if test="heroTitleEn != null">hero_title_en,</if>
            <if test="heroSubtitleZh != null">hero_subtitle_zh,</if>
            <if test="heroSubtitleEn != null">hero_subtitle_en,</if>
            <if test="heroImageUrl != null">hero_image_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nameZh != null and nameZh != ''">#{nameZh},</if>
            <if test="nameEn != null and nameEn != ''">#{nameEn},</if>
            <if test="descriptionZh != null">#{descriptionZh},</if>
            <if test="descriptionEn != null">#{descriptionEn},</if>
            <if test="heroTitleZh != null">#{heroTitleZh},</if>
            <if test="heroTitleEn != null">#{heroTitleEn},</if>
            <if test="heroSubtitleZh != null">#{heroSubtitleZh},</if>
            <if test="heroSubtitleEn != null">#{heroSubtitleEn},</if>
            <if test="heroImageUrl != null">#{heroImageUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateOrganizationCategory" parameterType="OrganizationCategory">
        update organization_categories
        <trim prefix="SET" suffixOverrides=",">
            <if test="nameZh != null and nameZh != ''">name_zh = #{nameZh},</if>
            <if test="nameEn != null and nameEn != ''">name_en = #{nameEn},</if>
            <if test="descriptionZh != null">description_zh = #{descriptionZh},</if>
            <if test="descriptionEn != null">description_en = #{descriptionEn},</if>
            <if test="heroTitleZh != null">hero_title_zh = #{heroTitleZh},</if>
            <if test="heroTitleEn != null">hero_title_en = #{heroTitleEn},</if>
            <if test="heroSubtitleZh != null">hero_subtitle_zh = #{heroSubtitleZh},</if>
            <if test="heroSubtitleEn != null">hero_subtitle_en = #{heroSubtitleEn},</if>
            <if test="heroImageUrl != null">hero_image_url = #{heroImageUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOrganizationCategoryById" parameterType="Long">
        delete from organization_categories where id = #{id}
    </delete>

    <delete id="deleteOrganizationCategoryByIds" parameterType="String">
        delete from organization_categories where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 