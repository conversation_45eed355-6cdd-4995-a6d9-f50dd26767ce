<template>
  <div class="press-releases-page">
    <div class="page-header">
      <div class="container">
        <h1>{{ currentLang === 'zh' ? '新聞稿' : 'Press Releases' }}</h1>
        <p class="page-description">
          {{ currentLang === 'zh' 
            ? '獲取我們的最新新聞和公告' 
            : 'Get our latest news and announcements' 
          }}
        </p>
      </div>
    </div>
    
    <div class="container">
      <div class="content-section">
        <h2>{{ currentLang === 'zh' ? '最新新聞' : 'Latest News' }}</h2>
        <!-- 新闻列表 -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PressReleases',
  props: {
    currentLang: {
      type: String,
      default: 'zh'
    }
  }
}
</script>

<style scoped>
.press-releases-page {
  padding-top: 80px;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #0056b3 0%, #004494 100%);
  color: white;
  padding: 60px 0;
  text-align: center;
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.page-description {
  font-size: 1.2rem;
  opacity: 0.9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.content-section {
  padding: 60px 0;
}

.content-section h2 {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: var(--text-primary);
}
</style> 