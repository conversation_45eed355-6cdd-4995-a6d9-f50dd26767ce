<template>
  <section class="testimonials-section" :style="section.config.style">
    <div class="container">
      <div class="section-title">
        <h2>{{ section.config.title }}</h2>
        <p>{{ section.config.subtitle }}</p>
      </div>
      
      <div class="testimonials-grid" :class="[`layout-${section.config.layout}`]">
        <div v-for="testimonial in section.config.testimonials" :key="testimonial.id" class="testimonial-card">
          <div class="testimonial-content">
            <p class="testimonial-text">"{{ testimonial.content }}"</p>
            <div class="testimonial-rating" v-if="testimonial.rating">
              <span v-for="n in testimonial.rating" :key="n" class="star">⭐</span>
            </div>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar">{{ testimonial.avatar }}</div>
            <div class="author-info">
              <div class="author-name">{{ testimonial.author }}</div>
              <div class="author-company">{{ testimonial.company }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'TestimonialsSection',
  props: {
    section: {
      type: Object,
      required: true
    }
  }
}
</script>

<style scoped>
.testimonials-section {
  padding: 80px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.5rem;
}

.section-title p {
  font-size: 1.125rem;
  color: var(--gray-600);
}

.testimonials-grid {
  display: grid;
  gap: 2rem;
}

.layout-grid {
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.layout-carousel {
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  overflow-x: auto;
  scroll-snap-type: x mandatory;
}

.testimonial-card {
  background: white;
  padding: 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-200);
  position: relative;
  transition: all 0.3s ease;
}

.testimonial-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.testimonial-content {
  margin-bottom: 1.5rem;
}

.testimonial-text {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--gray-700);
  margin-bottom: 1rem;
  font-style: italic;
}

.testimonial-rating {
  display: flex;
  gap: 0.25rem;
}

.star {
  font-size: 1rem;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  width: 3rem;
  height: 3rem;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.author-name {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 0.25rem;
}

.author-company {
  font-size: 0.875rem;
  color: var(--gray-600);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-grid,
  .layout-carousel {
    grid-template-columns: 1fr;
  }
  
  .testimonial-card {
    padding: 1.5rem;
  }
}
</style> 