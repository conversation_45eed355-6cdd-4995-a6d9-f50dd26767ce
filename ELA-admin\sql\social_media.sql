-- 社交媒体信息表
CREATE TABLE `social_media` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `platform` varchar(50) NOT NULL COMMENT '平台类型',
  `platform_name` varchar(100) NOT NULL COMMENT '平台显示名称',
  `url` varchar(500) NOT NULL COMMENT '社交媒体链接',
  `icon` varchar(500) COMMENT '图标图片路径',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='社交媒体信息表';

-- 插入默认社交媒体数据
INSERT INTO `social_media` (`platform`, `platform_name`, `url`, `icon`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('facebook', 'Facebook', 'https://www.facebook.com/hkela', '/profile/upload/2024/01/01/facebook-icon.png', 1, '0', 'admin', NOW(), 'admin', NOW(), 'Facebook官方账号'),
('instagram', 'Instagram', 'https://www.instagram.com/hkela', '/profile/upload/2024/01/01/instagram-icon.png', 2, '0', 'admin', NOW(), 'admin', NOW(), 'Instagram官方账号'),
('linkedin', 'LinkedIn', 'https://www.linkedin.com/company/hkela', '/profile/upload/2024/01/01/linkedin-icon.png', 3, '0', 'admin', NOW(), 'admin', NOW(), 'LinkedIn官方账号'),
('twitter', 'Twitter', 'https://twitter.com/hkela', '/profile/upload/2024/01/01/twitter-icon.png', 4, '0', 'admin', NOW(), 'admin', NOW(), 'Twitter官方账号'),
('youtube', 'YouTube', 'https://www.youtube.com/channel/hkela', '/profile/upload/2024/01/01/youtube-icon.png', 5, '0', 'admin', NOW(), 'admin', NOW(), 'YouTube官方频道'); 