import request from '@/utils/request'

// 查询领导留言列表
export function listLeadershipMessage(query) {
  return request({
    url: '/website/leadershipMessage/list',
    method: 'get',
    params: query
  })
}

// 查询领导留言详细
export function getLeadershipMessage(id) {
  return request({
    url: '/website/leadershipMessage/' + id,
    method: 'get'
  })
}

// 新增领导留言
export function addLeadershipMessage(data) {
  return request({
    url: '/website/leadershipMessage',
    method: 'post',
    data: data
  })
}

// 修改领导留言
export function updateLeadershipMessage(data) {
  return request({
    url: '/website/leadershipMessage',
    method: 'put',
    data: data
  })
}

// 删除领导留言
export function delLeadershipMessage(id) {
  return request({
    url: '/website/leadershipMessage/' + id,
    method: 'delete'
  })
} 