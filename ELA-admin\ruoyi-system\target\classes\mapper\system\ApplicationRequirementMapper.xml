<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ApplicationRequirementMapper">
    
    <resultMap type="ApplicationRequirement" id="ApplicationRequirementResult">
        <result property="id"    column="id"    />
        <result property="titleZh"    column="title_zh"    />
        <result property="titleEn"    column="title_en"    />
        <result property="requirementsZh"    column="requirements_zh"    typeHandler="com.ruoyi.framework.config.JsonListTypeHandler"    />
        <result property="requirementsEn"    column="requirements_en"    typeHandler="com.ruoyi.framework.config.JsonListTypeHandler"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="created_time"    />
        <result property="updateTime"    column="updated_time"    />
    </resultMap>

    <sql id="selectApplicationRequirementVo">
        select id, title_zh, title_en, requirements_zh, requirements_en, sort_order, status, created_time, updated_time from application_requirements
    </sql>

    <select id="selectApplicationRequirementList" parameterType="ApplicationRequirement" resultMap="ApplicationRequirementResult">
        <include refid="selectApplicationRequirementVo"/>
        <where>  
            <if test="titleZh != null  and titleZh != ''"> and title_zh like concat('%', #{titleZh}, '%')</if>
            <if test="titleEn != null  and titleEn != ''"> and title_en like concat('%', #{titleEn}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by sort_order asc, created_time desc
    </select>
    
    <select id="selectEnabledApplicationRequirementList" resultMap="ApplicationRequirementResult">
        <include refid="selectApplicationRequirementVo"/>
        where status = 1
        order by sort_order asc, created_time desc
    </select>
    
    <select id="selectApplicationRequirementById" parameterType="Long" resultMap="ApplicationRequirementResult">
        <include refid="selectApplicationRequirementVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertApplicationRequirement" parameterType="ApplicationRequirement" useGeneratedKeys="true" keyProperty="id">
        insert into application_requirements
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="titleZh != null and titleZh != ''">title_zh,</if>
            <if test="titleEn != null and titleEn != ''">title_en,</if>
            <if test="requirementsZh != null">requirements_zh,</if>
            <if test="requirementsEn != null">requirements_en,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            created_time,
            updated_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="titleZh != null and titleZh != ''">#{titleZh},</if>
            <if test="titleEn != null and titleEn != ''">#{titleEn},</if>
            <if test="requirementsZh != null">#{requirementsZh,typeHandler=com.ruoyi.framework.config.JsonListTypeHandler},</if>
            <if test="requirementsEn != null">#{requirementsEn,typeHandler=com.ruoyi.framework.config.JsonListTypeHandler},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            now(),
            now()
         </trim>
    </insert>

    <update id="updateApplicationRequirement" parameterType="ApplicationRequirement">
        update application_requirements
        <trim prefix="SET" suffixOverrides=",">
            <if test="titleZh != null and titleZh != ''">title_zh = #{titleZh},</if>
            <if test="titleEn != null and titleEn != ''">title_en = #{titleEn},</if>
            <if test="requirementsZh != null">requirements_zh = #{requirementsZh,typeHandler=com.ruoyi.framework.config.JsonListTypeHandler},</if>
            <if test="requirementsEn != null">requirements_en = #{requirementsEn,typeHandler=com.ruoyi.framework.config.JsonListTypeHandler},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            updated_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteApplicationRequirementById" parameterType="Long">
        delete from application_requirements where id = #{id}
    </delete>

    <delete id="deleteApplicationRequirementByIds" parameterType="String">
        delete from application_requirements where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 