package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.OrganizationCategory;
import org.apache.ibatis.annotations.Param;

/**
 * 组织架构分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface OrganizationCategoryMapper 
{
    /**
     * 查询组织架构分类
     * 
     * @param id 组织架构分类主键
     * @return 组织架构分类
     */
    public OrganizationCategory selectOrganizationCategoryById(Long id);

    /**
     * 查询组织架构分类列表
     * 
     * @param organizationCategory 组织架构分类
     * @return 组织架构分类集合
     */
    public List<OrganizationCategory> selectOrganizationCategoryList(OrganizationCategory organizationCategory);

    /**
     * 新增组织架构分类
     * 
     * @param organizationCategory 组织架构分类
     * @return 结果
     */
    public int insertOrganizationCategory(OrganizationCategory organizationCategory);

    /**
     * 修改组织架构分类
     * 
     * @param organizationCategory 组织架构分类
     * @return 结果
     */
    public int updateOrganizationCategory(OrganizationCategory organizationCategory);

    /**
     * 删除组织架构分类
     * 
     * @param id 组织架构分类主键
     * @return 结果
     */
    public int deleteOrganizationCategoryById(Long id);

    /**
     * 批量删除组织架构分类
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrganizationCategoryByIds(Long[] ids);
} 