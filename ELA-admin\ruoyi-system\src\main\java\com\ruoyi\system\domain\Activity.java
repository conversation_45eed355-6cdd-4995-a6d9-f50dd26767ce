package com.ruoyi.system.domain;

import java.util.Date;
import java.util.List;
import java.util.Arrays;
import java.util.ArrayList;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonSetter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 活动管理对象 activities
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
public class Activity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 活动ID */
    private Long id;

    /** 活动名称 */
    @Excel(name = "活动名称")
    private String title;

    /** 活动名称(英文) */
    @Excel(name = "活动名称(英文)")
    private String titleEn;

    /** 活动封面图URL */
    @Excel(name = "活动封面图URL")
    private String coverImageUrl;

    /** 活动内容图片URLs */
    private List<String> contentImages;

    /** 活动详情内容(富文本) */
    @Excel(name = "活动详情内容")
    private String content;

    /** 活动详情内容(英文富文本) */
    @Excel(name = "活动详情内容(英文)")
    private String contentEn;

    /** 活动开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "活动开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 活动结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "活动结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 活动外部链接 */
    @Excel(name = "活动外部链接")
    private String externalUrl;

    /** 排序号 */
    @Excel(name = "排序号")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 是否显示在首页（0否 1是） */
    @Excel(name = "是否显示在首页", readConverterExp = "0=否,1=是")
    private String showOnHomepage;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setTitleEn(String titleEn) 
    {
        this.titleEn = titleEn;
    }

    public String getTitleEn() 
    {
        return titleEn;
    }
    public void setCoverImageUrl(String coverImageUrl) 
    {
        this.coverImageUrl = coverImageUrl;
    }

    public String getCoverImageUrl() 
    {
        return coverImageUrl;
    }
    public void setContentImages(List<String> contentImages) 
    {
        this.contentImages = contentImages;
    }

    /**
     * 处理前端传递的逗号分隔的字符串
     */
    @JsonSetter("contentImages")
    public void setContentImagesFromString(Object contentImages) 
    {
        if (contentImages == null) {
            this.contentImages = new ArrayList<>();
        } else if (contentImages instanceof String) {
            String str = (String) contentImages;
            if (str.trim().isEmpty()) {
                this.contentImages = new ArrayList<>();
            } else {
                this.contentImages = Arrays.asList(str.split(","));
            }
        } else if (contentImages instanceof List) {
            this.contentImages = (List<String>) contentImages;
        } else {
            this.contentImages = new ArrayList<>();
        }
    }

    public List<String> getContentImages() 
    {
        return contentImages;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setContentEn(String contentEn) 
    {
        this.contentEn = contentEn;
    }

    public String getContentEn() 
    {
        return contentEn;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public void setExternalUrl(String externalUrl) 
    {
        this.externalUrl = externalUrl;
    }

    public String getExternalUrl() 
    {
        return externalUrl;
    }
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setShowOnHomepage(String showOnHomepage) 
    {
        this.showOnHomepage = showOnHomepage;
    }

    public String getShowOnHomepage() 
    {
        return showOnHomepage;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("titleEn", getTitleEn())
            .append("coverImageUrl", getCoverImageUrl())
            .append("contentImages", getContentImages())
            .append("content", getContent())
            .append("contentEn", getContentEn())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("externalUrl", getExternalUrl())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("showOnHomepage", getShowOnHomepage())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}