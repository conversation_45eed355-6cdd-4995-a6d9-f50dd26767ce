package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.OrganizationCategory;
import com.ruoyi.system.service.IOrganizationCategoryService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 组织架构分类Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/system/organization/category")
public class OrganizationCategoryController extends BaseController
{
    @Autowired
    private IOrganizationCategoryService organizationCategoryService;

    /**
     * 查询组织架构分类列表
     */
    @PreAuthorize("@ss.hasPermi('website:organization:category:list')")
    @GetMapping("/list")
    public TableDataInfo list(OrganizationCategory organizationCategory)
    {
        startPage();
        List<OrganizationCategory> list = organizationCategoryService.selectOrganizationCategoryList(organizationCategory);
        return getDataTable(list);
    }

    /**
     * 导出组织架构分类列表
     */
    @PreAuthorize("@ss.hasPermi('website:organization:category:export')")
    @Log(title = "组织架构分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrganizationCategory organizationCategory)
    {
        List<OrganizationCategory> list = organizationCategoryService.selectOrganizationCategoryList(organizationCategory);
        ExcelUtil<OrganizationCategory> util = new ExcelUtil<OrganizationCategory>(OrganizationCategory.class);
        util.exportExcel(response, list, "组织架构分类数据");
    }

    /**
     * 获取组织架构分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:organization:category:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(organizationCategoryService.selectOrganizationCategoryById(id));
    }

    /**
     * 新增组织架构分类
     */
    @PreAuthorize("@ss.hasPermi('website:organization:category:add')")
    @Log(title = "组织架构分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OrganizationCategory organizationCategory)
    {
        return toAjax(organizationCategoryService.insertOrganizationCategory(organizationCategory));
    }

    /**
     * 修改组织架构分类
     */
    @PreAuthorize("@ss.hasPermi('website:organization:category:edit')")
    @Log(title = "组织架构分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrganizationCategory organizationCategory)
    {
        return toAjax(organizationCategoryService.updateOrganizationCategory(organizationCategory));
    }

    /**
     * 删除组织架构分类
     */
    @PreAuthorize("@ss.hasPermi('website:organization:category:remove')")
    @Log(title = "组织架构分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(organizationCategoryService.deleteOrganizationCategoryByIds(ids));
    }

    /**
     * 获取组织架构分类选项
     */
    @GetMapping("/options")
    public AjaxResult getOptions()
    {
        OrganizationCategory query = new OrganizationCategory();
        query.setStatus("0");
        List<OrganizationCategory> list = organizationCategoryService.selectOrganizationCategoryList(query);
        return AjaxResult.success(list);
    }
} 