-- ----------------------------
-- 首页Banner表
-- ----------------------------
DROP TABLE IF EXISTS `home_banner`;
CREATE TABLE `home_banner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `image_url` varchar(500) NOT NULL COMMENT '图片URL',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='首页Banner表';

-- ----------------------------
-- 初始化首页Banner数据
-- ----------------------------
INSERT INTO `home_banner` VALUES 
(1, '/assets/<EMAIL>', 1, '0', 'admin', NOW(), '', NULL, '首页轮播图1'),
(2, '/assets/index/active.jpg', 2, '0', 'admin', NOW(), '', NULL, '首页轮播图2'),
(3, '/assets/index/1Mission-background.png', 3, '0', 'admin', NOW(), '', NULL, '首页轮播图3'); 