package com.ruoyi.web.controller.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.MemberBenefits;
import com.ruoyi.system.service.IMemberBenefitsService;

/**
 * 会员权益Controller
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
@RestController
@RequestMapping("/website/member-benefits")
public class MemberBenefitsController extends BaseController
{
    @Autowired
    private IMemberBenefitsService memberBenefitsService;

    /**
     * 查询会员权益列表
     */
    @PreAuthorize("@ss.hasPermi('website:member-benefits:list')")
    @GetMapping("/list")
    public TableDataInfo list(MemberBenefits memberBenefits)
    {
        startPage();
        List<MemberBenefits> list = memberBenefitsService.selectMemberBenefitsList(memberBenefits);
        return getDataTable(list);
    }

    /**
     * 导出会员权益列表
     */
    @PreAuthorize("@ss.hasPermi('website:member-benefits:export')")
    @Log(title = "会员权益", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MemberBenefits memberBenefits)
    {
        List<MemberBenefits> list = memberBenefitsService.selectMemberBenefitsList(memberBenefits);
        ExcelUtil<MemberBenefits> util = new ExcelUtil<MemberBenefits>(MemberBenefits.class);
        util.exportExcel(response, list, "会员权益数据");
    }

    /**
     * 获取会员权益详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:member-benefits:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(memberBenefitsService.selectMemberBenefitsById(id));
    }

    /**
     * 新增会员权益
     */
    @PreAuthorize("@ss.hasPermi('website:member-benefits:add')")
    @Log(title = "会员权益", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MemberBenefits memberBenefits)
    {
        memberBenefits.setCreateBy(getUsername());
        return toAjax(memberBenefitsService.insertMemberBenefits(memberBenefits));
    }

    /**
     * 修改会员权益
     */
    @PreAuthorize("@ss.hasPermi('website:member-benefits:edit')")
    @Log(title = "会员权益", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MemberBenefits memberBenefits)
    {
        memberBenefits.setUpdateBy(getUsername());
        return toAjax(memberBenefitsService.updateMemberBenefits(memberBenefits));
    }

    /**
     * 删除会员权益
     */
    @PreAuthorize("@ss.hasPermi('website:member-benefits:remove')")
    @Log(title = "会员权益", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(memberBenefitsService.deleteMemberBenefitsByIds(ids));
    }

    /**
     * 获取启用的会员权益列表(供前端使用)
     */
    @GetMapping("/public")
    public AjaxResult getPublicMemberBenefits()
    {
        MemberBenefits memberBenefits = new MemberBenefits();
        memberBenefits.setStatus("0"); // 只获取启用的会员权益
        List<MemberBenefits> list = memberBenefitsService.selectMemberBenefitsList(memberBenefits);
        return AjaxResult.success(list);
    }
} 