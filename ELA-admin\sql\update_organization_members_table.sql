-- 更新 organization_members 表，将 member_type 改为外键
-- 第一步：添加新的 member_type_id 字段
ALTER TABLE organization_members ADD COLUMN member_type_id BIGINT COMMENT '成员类型ID';

-- 第二步：更新数据，将字符串类型转换为对应的ID
UPDATE organization_members om 
SET member_type_id = (
    SELECT mt.id 
    FROM member_types mt 
    WHERE mt.type_code = om.member_type
);

-- 第三步：删除旧的 member_type 字段
ALTER TABLE organization_members DROP COLUMN member_type;

-- 第四步：重命名 member_type_id 为 member_type
ALTER TABLE organization_members CHANGE COLUMN member_type_id member_type BIGINT NOT NULL COMMENT '成员类型ID';

-- 第五步：添加外键约束
ALTER TABLE organization_members ADD CONSTRAINT fk_member_type 
FOREIGN KEY (member_type) REFERENCES member_types(id);

-- 第六步：添加索引
ALTER TABLE organization_members ADD INDEX idx_member_type (member_type);

-- 更新表注释
ALTER TABLE organization_members COMMENT='组织架构成员表'; 