<template>
  <header class="header" :class="{ 'scrolled': scrolled, 'hovered': hovered }" @mouseenter="hovered = true" @mouseleave="hovered = false">
    <div class="container">
      <div class="navbar">
        <!-- Logo区域 -->
        <div class="navbar-brand">
          <router-link to="/" class="logo">
            <img v-if="scrolled || hovered" src="@/assets/logo.png" alt="百度 Baidu Logo" class="logo-img" />
            <img v-else src="@/assets/logo.png" alt="百度 Baidu Logo" class="logo-img" />
          </router-link>
        </div>

        <!-- 导航菜单 -->
        <nav class="navbar-nav" :class="{ 'active': menuOpen }">
          <template v-for="(item, index) in computedMenuItems">
            <router-link 
              v-if="!item.children"
              :key="`nav-link-${index}`"
              :to="item.path" 
              class="nav-link" 
              @click.native="closeMenu"
            >
              <i v-if="item.icon" :class="item.icon"></i>
              {{ currentLang === 'zh' ? item.nameZh : item.nameEn }}
            </router-link>
            
            <div 
              v-else
              :key="`nav-dropdown-${index}`"
              class="nav-dropdown"
              @mouseenter="!isMobile && showDropdown(item.path)"
              @mouseleave="!isMobile && hideDropdown(item.path)"
            >
              <a 
                href="javascript:void(0)" 
                class="nav-link dropdown-toggle"
                @click.prevent="!isMobile && toggleDropdown(item.path)"
              >
                {{ currentLang === 'zh' ? item.nameZh : item.nameEn }}
                <i class="fas fa-chevron-down dropdown-icon"></i>
              </a>
              <div class="dropdown-menu" :class="{ 'show': activeDropdown === item.path }">
                <router-link 
                  v-for="child in item.children"
                  :key="child.path"
                  :to="child.path"
                  class="dropdown-item"
                  @click.native="closeMenu"
                >
                  {{ currentLang === 'zh' ? child.nameZh : child.nameEn }}
                </router-link>
              </div>
            </div>
          </template>
        </nav>

        <!-- 语言切换和搜索按钮 -->
        <div class="navbar-controls">
          <div class="language-switch">
            <a 
              href="#" 
              class="lang-link" 
              :class="{ 'active': currentLang === 'zh' }"
              @click.prevent="switchLanguage('zh')"
            >
              中文
            </a>
            <span class="separator">|</span>
            <a 
              href="#" 
              class="lang-link" 
              :class="{ 'active': currentLang === 'en' }"
              @click.prevent="switchLanguage('en')"
            >
              EN
            </a>
          </div>

          <!-- 移动端菜单按钮 -->
          <button class="mobile-menu-btn" @click="toggleMenu" aria-label="菜单">
            <span class="hamburger-line" :class="{ 'active': menuOpen }"></span>
            <span class="hamburger-line" :class="{ 'active': menuOpen }"></span>
            <span class="hamburger-line" :class="{ 'active': menuOpen }"></span>
          </button>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { getOrganizationCategories } from '@/api/organization'

export default {
  name: 'Header',
  data() {
    return {
      scrolled: false,
      hovered: false,
      menuOpen: false,
      activeDropdown: null,
      currentLang: 'zh',
      isMobile: false,
      // 动态获取的关于我们子菜单
      aboutMenuItems: [],
      menuItems: [
        {
          path: '/',
          nameZh: '首頁',
          nameEn: 'Home'
        },
        {
          path: '/about',
          nameZh: '關於我們',
          nameEn: 'About Us',
          children: []
        },
        {
          path: '/pr-events',
          nameZh: '公關及活動',
          nameEn: 'PR & Events'
        },
        // {
        //   path: '/publication',
        //   nameZh: '資訊',
        //   nameEn: 'Publication'
        // },
        {
          path: '/join-us',
          nameZh: '加入我們',
          nameEn: 'Join Us'
        },
        {
          path: '/contact',
          nameZh: '聯繫我們',
          nameEn: 'Contact Us',
          icon: 'fas fa-phone-alt'
        }
      ]
    }
  },
  computed: {
    computedMenuItems() {
      // 动态更新关于我们子菜单
      const menuItems = [...this.menuItems]
      const aboutIndex = menuItems.findIndex(item => item.path === '/about')
      if (aboutIndex !== -1) {
        menuItems[aboutIndex].children = this.aboutMenuItems
      }
      return menuItems
    }
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll)
    window.addEventListener('resize', this.handleResize)
    this.handleScroll() // Initial check
    this.handleResize() // Initial mobile check
    
    // 从localStorage恢复语言设置
    const savedLang = localStorage.getItem('language')
    if (savedLang) {
      this.currentLang = savedLang
    }
    
    // 加载组织架构分类数据
    this.loadOrganizationCategories()
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll)
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    /** 加载组织架构分类数据 */
    async loadOrganizationCategories() {
      try {
        const response = await getOrganizationCategories()
        
        // 将分类数据转换为菜单项格式
        this.aboutMenuItems = response.data
          .filter(category => category.status === '0') // 只获取启用的分类
          .sort((a, b) => a.sortOrder - b.sortOrder) // 按排序排列
          .map(category => {
            const slug = this.generateSlug(category.nameEn)
            const menuItem = {
              path: `/about/${slug}?categoryId=${category.id}`,
              nameZh: category.nameZh,
              nameEn: category.nameEn,
              categoryId: category.id,
              categoryName: category.nameZh
            }
            console.log('Debug - Generated menu item:', category.nameEn, '-> slug:', slug, '-> path:', menuItem.path)
            return menuItem
          })
      } catch (error) {
        console.error('加载组织架构分类失败:', error)
        // 如果API失败，使用默认的硬编码数据作为备选
        console.log('Debug - Using fallback menu items')
        this.aboutMenuItems = [
          {
            path: '/about/council?categoryId=1',
            nameZh: '理事会',
            nameEn: 'Board of Directors',
            categoryId: 1,
            categoryName: '理事会'
          },
          {
            path: '/about/advisors?categoryId=2',
            nameZh: '顧問, 創會及永久會員',
            nameEn: 'Advisors, Founding & Permanent Members',
            categoryId: 2,
            categoryName: '顧問, 創會及永久會員'
          },
          {
            path: '/about/youth-committee?categoryId=3',
            nameZh: '青年委員會',
            nameEn: 'Youth Committee',
            categoryId: 3,
            categoryName: '青年委員會'
          }
        ]
      }
    },
    
    /** 生成URL友好的slug */
    generateSlug(text) {
      // 对特定的分类名称进行特殊处理，生成简化的slug
      const categorySlugMap = {
        'Board of Directors': 'council',
        'Advisors, Founding & Permanent Members': 'advisors', 
        'Youth Committee': 'youth-committee'
      }
      
      // 如果是预定义的分类，返回简化的slug
      if (categorySlugMap[text]) {
        return categorySlugMap[text]
      }
      
      // 其他情况使用通用的slug生成逻辑
      return text
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '')
    },
    
    toggleMenu() {
      this.menuOpen = !this.menuOpen
    },
    closeMenu() {
      this.menuOpen = false
      // 延迟重置下拉菜单状态，避免立即关闭
      setTimeout(() => {
        this.activeDropdown = null
      }, 100)
    },
    handleScroll() {
      this.scrolled = window.scrollY > 50
    },
    handleResize() {
      this.isMobile = window.innerWidth <= 768
    },
    switchLanguage(lang) {
      this.currentLang = lang
      localStorage.setItem('language', lang)
      // 可以在这里触发全局语言切换事件
      this.$emit('language-changed', lang)
    },
    showDropdown(path) {
      if (!this.isMobile) {
        this.activeDropdown = path
      }
    },
    hideDropdown(path) {
      if (!this.isMobile) {
        this.activeDropdown = null
      }
    },
    toggleDropdown(path) {
      if (this.activeDropdown === path) {
        this.activeDropdown = null
      } else {
        this.activeDropdown = path
      }
    }
  }
}
</script>

<style scoped>
.header {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: background-color 0.4s ease, border-color 0.4s ease, box-shadow 0.4s ease;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header.scrolled,
.header.hovered {
  background: #ffffff;
  border-bottom-color: #eee;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
  transition: padding 0.3s ease;
}

.header.scrolled .navbar,
.header.hovered .navbar {
  padding: 0.5rem 0;
}

/* Logo */
.navbar-brand .logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo-img {
  height: 80px;
  transition: height 0.3s ease;
}

.header.scrolled .logo-img,
.header.hovered .logo-img {
  height: 70px;
}

/* Navigation */
.navbar-nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  color: #ffffff;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  padding: 1.5rem 0;
  position: relative;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header.scrolled .nav-link,
.header.hovered .nav-link {
  color: #333;
}

.nav-link:hover,
.router-link-exact-active,
.dropdown-toggle:hover {
  color: #0056b3;
}

.header.scrolled .router-link-exact-active,
.header.hovered .router-link-exact-active,
.header.scrolled .dropdown-toggle:hover,
.header.hovered .dropdown-toggle:hover {
  color: #0056b3;
}

.header:not(.scrolled):not(.hovered) .router-link-exact-active,
.header:not(.scrolled):not(.hovered) .dropdown-toggle:hover {
  color: #ffffff;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: #0056b3;
  transition: width 0.3s ease;
}

.header:not(.scrolled):not(.hovered) .router-link-exact-active::after,
.header:not(.scrolled):not(.hovered) .dropdown-toggle:hover::after {
  background: #ffffff;
  width: 100%;
}

.nav-link:hover::after,
.header.scrolled .router-link-exact-active::after,
.header.hovered .router-link-exact-active::after,
.header.scrolled .dropdown-toggle:hover::after,
.header.hovered .dropdown-toggle:hover::after {
  width: 100%;
}

/* Dropdown */
.nav-dropdown {
  position: relative;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.dropdown-icon {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.nav-dropdown:hover .dropdown-icon {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #ffffff;
  min-width: 220px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-radius: 8px;
  padding: 0.5rem 0;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%) translateY(-10px);
  transition: all 0.3s ease;
  margin-top: 0.5rem;
  z-index: 1001;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
  display: block;
}

.dropdown-item {
  display: block;
  padding: 0.75rem 1.5rem;
  color: #333;
  text-decoration: none;
  font-size: 15px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.dropdown-item:hover {
  background: #f8f9fa;
  color: #0056b3;
  padding-left: 2rem;
}

/* Controls */
.navbar-controls {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.language-switch {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ffffff;
  font-size: 14px;
}

.header.scrolled .language-switch,
.header.hovered .language-switch {
  color: #333;
}

.lang-link {
  color: inherit;
  text-decoration: none;
  opacity: 0.7;
  transition: opacity 0.3s ease;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
}

.lang-link.active {
  opacity: 1;
  font-weight: 600;
}

.lang-link:hover:not(.active) {
  opacity: 0.9;
  background: rgba(0, 86, 179, 0.1);
}

.separator {
  opacity: 0.5;
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 1002;
}

.hamburger-line {
  width: 25px;
  height: 3px;
  background-color: #ffffff;
  transition: all 0.3s ease;
  transform-origin: center;
}

.header.scrolled .hamburger-line,
.header.hovered .hamburger-line {
  background-color: #333;
}

.hamburger-line.active:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-line.active:nth-child(2) {
  opacity: 0;
}

.hamburger-line.active:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .navbar-nav {
    gap: 1.5rem;
  }
  
  .nav-link {
    font-size: 15px;
  }
}

@media (max-width: 768px) {
  .mobile-menu-btn {
    display: flex;
  }

  .navbar-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1.5rem;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 1001;
    padding: 2rem;
  }
  
  .navbar-nav.active {
    transform: translateX(0);
  }
  
  .nav-dropdown {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }
  
  .nav-link {
    color: #333;
    font-size: 1.1rem;
    padding: 0.8rem 1rem;
    text-align: center;
    border-radius: 8px;
    transition: all 0.3s ease;
    width: 85%;
    max-width: 280px;
    min-width: 200px;
    background: rgba(0, 86, 179, 0.05);
    margin: 0.2rem 0;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .nav-link:hover {
    color: #0056b3;
    background: rgba(0, 86, 179, 0.1);
    transform: translateY(-2px);
  }
  
  .dropdown-toggle {
    position: relative;
  }
  
  .dropdown-icon {
    margin-left: 0.5rem;
  }
  
  .dropdown-menu {
    position: static;
    transform: none;
    box-shadow: none;
    background: transparent;
    margin-top: 0.5rem;
    opacity: 1;
    visibility: visible;
    max-height: 500px;
    overflow: visible;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s ease;
  }
  
  .dropdown-item {
    color: #666;
    font-size: 0.95rem;
    padding: 0.6rem 0.8rem;
    text-align: center;
    border-radius: 6px;
    transition: all 0.3s ease;
    width: 85%;
    max-width: 280px;
    min-width: 200px;
    background: rgba(0, 86, 179, 0.03);
    margin: 0.1rem 0;
    border-left: 3px solid transparent;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .dropdown-item:hover {
    color: #0056b3;
    background: rgba(0, 86, 179, 0.08);
    border-left-color: #0056b3;
  }
  
  .language-switch {
    font-size: 16px;
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background: rgba(0, 86, 179, 0.1);
    border-radius: 20px;
  }
}
</style>
