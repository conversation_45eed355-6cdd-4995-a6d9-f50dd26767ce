package com.ruoyi.website;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.Publication;
import com.ruoyi.system.service.IPublicationService;

/**
 * 前端网站资讯查询Controller
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
@RestController
@RequestMapping("/api/publications")
public class WebPublicationController extends BaseController
{
    @Autowired
    private IPublicationService publicationService;

    /**
     * 查询公开的资讯列表（供前端网站使用）
     */
    @GetMapping("/public")
    public AjaxResult getPublicPublications()
    {
        Publication publication = new Publication();
        publication.setStatus("0"); // 只查询正常状态的资讯
        List<Publication> list = publicationService.selectPublicationList(publication);
        return AjaxResult.success(list);
    }

    /**
     * 查询最新资讯列表
     */
    @GetMapping("/latest")
    public AjaxResult getLatestPublications()
    {
        Publication publication = new Publication();
        publication.setStatus("0"); // 只查询正常状态的资讯
        List<Publication> list = publicationService.selectPublicationList(publication);
        
        // 可以在这里添加过滤逻辑，比如只返回最新的资讯
        return AjaxResult.success(list);
    }

    /**
     * 查询首页显示的资讯列表
     */
    @GetMapping("/homepage")
    public AjaxResult getHomepagePublications()
    {
        Publication publication = new Publication();
        publication.setStatus("0"); // 只查询正常状态的资讯
        List<Publication> list = publicationService.selectPublicationList(publication);
        return AjaxResult.success(list);
    }

    /**
     * 根据ID查询资讯详情
     */
    @GetMapping("/{id}")
    public AjaxResult getPublicationById(@PathVariable Long id)
    {
        Publication publication = publicationService.selectPublicationById(id);
        if (publication != null && "0".equals(publication.getStatus())) {
            return AjaxResult.success(publication);
        }
        return AjaxResult.error("资讯不存在或已下线");
    }
}