-- ----------------------------
-- 首页Hero内容表
-- ----------------------------
drop table if exists hero_content;
create table hero_content (
  id           bigint(20)      not null auto_increment    comment '主键ID',
  badge_zh     varchar(200)    default null               comment '徽章文字中文',
  badge_en     varchar(200)    default null               comment '徽章文字英文',
  title_zh     text            default null               comment '标题中文',
  title_en     text            default null               comment '标题英文',
  subtitle_zh  text            default null               comment '副标题中文',
  subtitle_en  text            default null               comment '副标题英文',
  buttons_config text          default null               comment '按钮配置JSON',
  stats_config text            default null               comment '统计数据配置JSON',
  status       char(1)         default '0'                comment '状态（0正常 1停用）',
  sort         int(4)          default 0                  comment '排序',
  create_by    varchar(64)     default ''                 comment '创建者',
  create_time  datetime                                    comment '创建时间',
  update_by    varchar(64)     default ''                 comment '更新者',
  update_time  datetime                                    comment '更新时间',
  remark       varchar(500)    default null               comment '备注',
  primary key (id)
) engine=innodb comment = '首页Hero内容表';

-- ----------------------------
-- 初始化-首页Hero内容表数据
-- ----------------------------
INSERT INTO hero_content VALUES (1, '香港電商物流協會', 'Hong Kong E-commerce Logistics Association', 
'推動香港成為<br><span class=\"highlight\">全球電商物流樞紐</span>', 
'Driving Hong Kong as<br><span class=\"highlight\">Global E-commerce Hub</span>',
'匯聚業界精英，推動行業發展，培育專業人才，建設可持續的電商物流生態系統',
'Uniting industry leaders, driving sector growth, nurturing professionals, building sustainable e-commerce logistics ecosystem',
'[{\"text\":{\"zh\":\"加入我們\",\"en\":\"Join Us\"},\"url\":\"/join-us\",\"type\":\"primary\",\"icon\":\"fas fa-arrow-right\"},{\"text\":{\"zh\":\"了解更多\",\"en\":\"Learn More\"},\"url\":\"/about\",\"type\":\"secondary\"}]',
'[{\"number\":\"200+\",\"label\":{\"zh\":\"會員企業\",\"en\":\"Member Companies\"}},{\"number\":\"50+\",\"label\":{\"zh\":\"行業活動\",\"en\":\"Industry Events\"}},{\"number\":\"100+\",\"label\":{\"zh\":\"專業培訓\",\"en\":\"Training Programs\"}},{\"number\":\"5+\",\"label\":{\"zh\":\"合作年份\",\"en\":\"Years of Excellence\"}}]',
'0', 1, 'admin', sysdate(), '', null, '首页Hero内容'); 