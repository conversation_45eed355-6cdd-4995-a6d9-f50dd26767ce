<template>
  <div class="about-page">
    <!-- 如果有子路由，显示子路由内容；否则显示默认内容 -->
    <router-view v-if="$route.matched.length > 1" :current-lang="currentLang"/>

    <!-- 默认的关于我们页面内容 -->
    <div v-else>
      <!-- 大背景Banner -->
      <section class="hero-banner">
        <div class="hero-overlay"></div>
        <div class="hero-content">
          <h1 class="hero-title">{{ currentLang === 'zh' ? '關於我們' : 'About Us' }}</h1>
          <p class="hero-subtitle">
            {{ currentLang === 'zh'
              ? '以科技創新引領可持續未來，塑造美好生活'
              : 'Leading a sustainable future through technological innovation'
            }}
          </p>
        </div>
      </section>

      <div class="about-container">
        <AboutSubnav :current-lang="currentLang" />
        <main class="about-content">
          <CompanyProfile id="profile" :current-lang="currentLang" />
          <CompanyStats id="stats" :current-lang="currentLang" />
          <FullWidthBanner id="banner" :current-lang="currentLang" />
          <!-- Other sections will be added here -->
        </main>
      </div>
    </div>
  </div>
</template>

<script>
// 导入所有可用的区块组件
import PageHero from './sections/PageHero.vue'
import ContentIntro from './sections/ContentIntro.vue'
import GridCards from './sections/GridCards.vue'
import TeamGrid from './sections/TeamGrid.vue'
import Timeline from './sections/Timeline.vue'
import StatsSection from './sections/StatsSection.vue'
import TestimonialsSection from './sections/TestimonialsSection.vue'
import ContactCTA from './sections/ContactCTA.vue'
import AboutSubnav from './sections/AboutSubnav.vue'
import CompanyProfile from './sections/CompanyProfile.vue'
import CompanyStats from './sections/CompanyStats.vue'
import FullWidthBanner from './sections/FullWidthBanner.vue'

export default {
  name: 'About',
  components: {
    PageHero,
    ContentIntro,
    GridCards,
    TeamGrid,
    Timeline,
    StatsSection,
    TestimonialsSection,
    ContactCTA,
    AboutSubnav,
    CompanyProfile,
    CompanyStats,
    FullWidthBanner
  },
  props: {
    currentLang: {
      type: String,
      default: 'zh'
    }
  },

  data () {
    return {
      // 完全灵活的页面配置
      pageConfig: {
        // 页面基础信息
        meta: {
          title: '关于我们 - 香港电子商务物流协会',
          description: '了解香港电子商务物流协会的使命、愿景、发展历程和核心团队。'
        },

        // 动态区块配置
        sections: [
          {
            id: 'hero',
            type: 'hero',
            enabled: true,
            config: {
              title: '关于我们',
              subtitle: '以科技创新引领可持续未来，塑造美好生活。'
              // 组件内部将处理背景和样式
            }
          },

          {
            id: 'company-intro',
            type: 'content-intro',
            enabled: true,
            config: {
              layout: 'split',
              title: '我们的使命与愿景',
              content: [
                '香港电子商务物流协会致力于成为全球领先的电子商务物流服务平台，我们秉持"创新、高效、可持续"的理念，将科技创新作为发展的核心驱动力。',
                '我们通过持续的技术突破和行业整合，为电商企业提供更加智能、高效、环保的物流解决方案，驱动电商物流产业升级，创造经济、社会和环境的综合价值，共创数字时代的和谐未来。'
              ],
              features: [
                { id: 1, title: '创新驱动', description: '以前沿科技引领物流行业变革', icon: 'fas fa-lightbulb' },
                { id: 2, title: '绿色发展', description: '坚持可持续发展，推动绿色物流', icon: 'fas fa-leaf' },
                { id: 3, title: '合作共赢', description: '与全球伙伴共创价值', icon: 'fas fa-handshake' }
              ],
              mediaConfig: {
                type: 'image',
                content: '/static/images/company-intro.jpg', // 请替换为真实图片
                position: 'right'
              }
            }
          },

          {
            id: 'company-stats',
            type: 'stats',
            enabled: true,
            config: {
              title: '关键数据',
              subtitle: '数字见证我们的实力与承诺',
              stats: [
                { id: 1, number: '500+', label: '合作电商平台', icon: 'fas fa-shopping-cart' },
                { id: 2, number: '1000+', label: '物流服务商', icon: 'fas fa-truck' },
                { id: 3, number: '50+', label: '行业标准制定', icon: 'fas fa-scroll' },
                { id: 4, number: '全球30+', label: '服务国家和地区', icon: 'fas fa-globe-asia' }
              ]
            }
          },

          {
            id: 'history-timeline',
            type: 'timeline',
            enabled: true,
            config: {
              title: '发展历程',
              subtitle: '每一步都凝聚着我们的汗水与智慧',
              events: [
                { year: '2010', title: '创立之始', description: '香港电子商务物流协会成立，开启电商物流服务新篇章。' },
                { year: '2015', title: '数字化转型', description: '全面拥抱数字化技术，构建智能物流平台。' },
                { year: '2018', title: '国际化布局', description: '正式开启国际化战略，服务全球电商市场。' },
                { year: '2020', title: '疫情应对', description: '在疫情期间为电商企业提供稳定可靠的物流保障。' },
                { year: '至今', title: '引领未来', description: '成为亚洲领先的电子商务物流服务平台。' }
              ]
            }
          },

          {
            id: 'team',
            type: 'team-grid',
            enabled: true,
            config: {
              title: '核心团队',
              subtitle: '经验丰富的专家团队是我们成功的基石',
              members: [
                { id: 1, name: '陈总', position: '首席执行官', avatar: '/static/images/team-1.jpg', description: '拥有超过15年的物流行业管理经验，致力于推动协会战略转型与全球化布局。' },
                { id: 2, name: '林博士', position: '首席技术官', avatar: '/static/images/team-2.jpg', description: '物流科技博士，领导协会的技术创新，拥抱最前沿的物流科技。' },
                { id: 3, name: '黄总', position: '首席运营官', avatar: '/static/images/team-3.jpg', description: '负责协会全球运营，优化物流网络体系，确保高效、安全的物流服务。' },
                { id: 4, name: '刘总', position: '市场总监', avatar: '/static/images/team-4.jpg', description: '深谙全球电商市场动态，为协会的服务开拓更广阔的国际市场。' }
              ]
            }
          },

          {
            id: 'cta',
            type: 'contact-cta',
            enabled: true,
            config: {
              title: '准备好与我们共创未来了吗？',
              subtitle: '我们期待与您建立联系，探讨合作机会，共同推动电商物流行业进步。',
              buttonText: '立即联系',
              buttonLink: '/contact'
            }
          }
        ]
      }
    }
  },

  // 生命周期：模拟从CMS/API获取页面配置
  async created () {
    console.log('🚀 开始加载关于我们页面配置...')

    // 模拟API调用延迟
    await this.loadPageConfig()

    console.log('✅ 页面配置加载完成:', this.pageConfig)
  },

  methods: {
    // 根据区块类型返回对应的组件名
    getSectionComponent (type) {
      const componentMap = {
        'hero': 'PageHero',
        'content-intro': 'ContentIntro',
        'grid-cards': 'GridCards',
        'team-grid': 'TeamGrid',
        'timeline': 'Timeline',
        'stats': 'StatsSection',
        'testimonials': 'TestimonialsSection',
        'contact-cta': 'ContactCTA'
      }

      return componentMap[type] || null
    },

    // 模拟从API/CMS加载页面配置
    async loadPageConfig () {
      // 这里可以调用真实的API
      // const response = await api.getPageConfig('about')
      // this.pageConfig = response.data

      // 模拟网络延迟
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('📊 页面数据加载完成')
          resolve()
        }, 300)
      })
    },

    // 动态更新页面配置（用于CMS管理后台）
    updatePageConfig (newConfig) {
      this.pageConfig = {
        ...this.pageConfig,
        ...newConfig
      }
      console.log('🔄 页面配置已更新:', this.pageConfig)
    },

    // 添加新区块
    addSection (sectionConfig) {
      const newSection = {
        id: 'section_' + Date.now(),
        order: this.pageConfig.sections.length + 1,
        enabled: true,
        ...sectionConfig
      }

      this.pageConfig.sections.push(newSection)
      this.sortSections()
      console.log('➕ 已添加新区块:', newSection)
    },

    // 删除区块
    removeSection (sectionId) {
      this.pageConfig.sections = this.pageConfig.sections.filter(
        section => section.id !== sectionId
      )
      console.log('❌ 已删除区块:', sectionId)
    },

    // 启用/禁用区块
    toggleSection (sectionId, enabled) {
      const section = this.pageConfig.sections.find(s => s.id === sectionId)
      if (section) {
        section.enabled = enabled
        console.log('🔄 区块 ' + sectionId + ' ' + (enabled ? '已启用' : '已禁用'))
      }
    },

    // 重新排序区块
    reorderSection (sectionId, newOrder) {
      const section = this.pageConfig.sections.find(s => s.id === sectionId)
      if (section) {
        section.order = newOrder
        this.sortSections()
        console.log('📋 区块顺序已更新')
      }
    },

    // 按order字段排序区块
    sortSections () {
      this.pageConfig.sections.sort((a, b) => a.order - b.order)
    },

    // 导出页面配置（用于备份或迁移）
    exportConfig () {
      const config = JSON.stringify(this.pageConfig, null, 2)
      console.log('📄 页面配置导出:', config)
      return config
    },

    // 导入页面配置
    importConfig (configJson) {
      try {
        const config = JSON.parse(configJson)
        this.pageConfig = config
        console.log('📥 页面配置导入成功')
      } catch (error) {
        console.error('❌ 配置导入失败:', error)
      }
    }
  },

  head () {
    return {
      title: this.pageConfig.meta.title,
      meta: [
        { hid: 'description', name: 'description', content: this.pageConfig.meta.description }
      ]
    }
  }
}
</script>

<style scoped>
.about-page {
  background-color: #fff;
}

/* Hero Banner */
.hero-banner {
  height: 500px;
  background-image: url('~@/assets/banner/aboutUs.png');
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.2rem;
  font-weight: 300;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.6;
  opacity: 0.9;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.about-content {
  margin-top: 30px;
}

/* 全局区块样式 */
.about :deep(.section) {
  position: relative;
  overflow: hidden;
}

.about :deep(.container) {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.about :deep(.section-title) {
  text-align: center;
  margin-bottom: 3rem;
}

.about :deep(.section-title h2) {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.5rem;
}

.about :deep(.section-title p) {
  font-size: 1.125rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-banner {
    height: 400px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .about-container {
    padding: 20px 10px;
  }

  .about :deep(.section-title h2) {
    font-size: 2rem;
  }

  .about :deep(.section-title p) {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .hero-banner {
    height: 300px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 0.9rem;
  }

  .hero-content {
    padding: 0 1rem;
  }
}
</style>
