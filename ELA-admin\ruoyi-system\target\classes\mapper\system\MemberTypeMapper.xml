<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MemberTypeMapper">
    
    <resultMap type="MemberType" id="MemberTypeResult">
        <result property="id"    column="id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="typeCode"    column="type_code"    />
        <result property="nameZh"    column="name_zh"    />
        <result property="nameEn"    column="name_en"    />
        <result property="descriptionZh"    column="description_zh"    />
        <result property="descriptionEn"    column="description_en"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="categoryName"    column="category_name"    />
    </resultMap>

    <sql id="selectMemberTypeVo">
        select id, category_id, type_code, name_zh, name_en, description_zh, description_en, sort_order, status, create_by, create_time, update_by, update_time, remark from member_types
    </sql>

    <select id="selectMemberTypeList" parameterType="MemberType" resultMap="MemberTypeResult">
        select mt.id, mt.category_id, mt.type_code, mt.name_zh, mt.name_en, mt.description_zh, mt.description_en, 
               mt.sort_order, mt.status, mt.create_by, mt.create_time, mt.update_by, mt.update_time, mt.remark,
               oc.name_zh as category_name
        from member_types mt
        left join organization_categories oc on mt.category_id = oc.id
        <where>  
            <if test="categoryId != null">and mt.category_id = #{categoryId}</if>
            <if test="typeCode != null  and typeCode != ''"> and mt.type_code like concat('%', #{typeCode}, '%')</if>
            <if test="nameZh != null  and nameZh != ''"> and mt.name_zh like concat('%', #{nameZh}, '%')</if>
            <if test="nameEn != null  and nameEn != ''"> and mt.name_en like concat('%', #{nameEn}, '%')</if>
            <if test="status != null  and status != ''"> and mt.status = #{status}</if>
        </where>
        order by mt.sort_order asc, mt.create_time desc
    </select>
    
    <select id="selectMemberTypeById" parameterType="Long" resultMap="MemberTypeResult">
        <include refid="selectMemberTypeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMemberType" parameterType="MemberType" useGeneratedKeys="true" keyProperty="id">
        insert into member_types
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="typeCode != null and typeCode != ''">type_code,</if>
            <if test="nameZh != null and nameZh != ''">name_zh,</if>
            <if test="nameEn != null and nameEn != ''">name_en,</if>
            <if test="descriptionZh != null">description_zh,</if>
            <if test="descriptionEn != null">description_en,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="typeCode != null and typeCode != ''">#{typeCode},</if>
            <if test="nameZh != null and nameZh != ''">#{nameZh},</if>
            <if test="nameEn != null and nameEn != ''">#{nameEn},</if>
            <if test="descriptionZh != null">#{descriptionZh},</if>
            <if test="descriptionEn != null">#{descriptionEn},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateMemberType" parameterType="MemberType">
        update member_types
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="typeCode != null and typeCode != ''">type_code = #{typeCode},</if>
            <if test="nameZh != null and nameZh != ''">name_zh = #{nameZh},</if>
            <if test="nameEn != null and nameEn != ''">name_en = #{nameEn},</if>
            <if test="descriptionZh != null">description_zh = #{descriptionZh},</if>
            <if test="descriptionEn != null">description_en = #{descriptionEn},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMemberTypeById" parameterType="Long">
        delete from member_types where id = #{id}
    </delete>

    <delete id="deleteMemberTypeByIds" parameterType="String">
        delete from member_types where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectMemberTypeByCode" parameterType="String" resultMap="MemberTypeResult">
        <include refid="selectMemberTypeVo"/>
        where type_code = #{typeCode}
    </select>

    <select id="checkTypeCodeUnique" parameterType="String" resultType="int">
        select count(1) from member_types where type_code = #{typeCode}
    </select>

    <select id="selectMemberTypeOptions" resultMap="MemberTypeResult">
        <include refid="selectMemberTypeVo"/>
        where status = '0'
        order by sort_order asc
    </select>

    <select id="selectMemberTypeOptionsByCategoryId" parameterType="Long" resultMap="MemberTypeResult">
        <include refid="selectMemberTypeVo"/>
        where category_id = #{categoryId} and status = '0'
        order by sort_order asc
    </select>
</mapper> 