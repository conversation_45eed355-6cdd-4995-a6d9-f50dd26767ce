package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.OrganizationCategoryMapper;
import com.ruoyi.system.domain.OrganizationCategory;
import com.ruoyi.system.service.IOrganizationCategoryService;

/**
 * 组织架构分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class OrganizationCategoryServiceImpl implements IOrganizationCategoryService 
{
    @Autowired
    private OrganizationCategoryMapper organizationCategoryMapper;

    /**
     * 查询组织架构分类
     * 
     * @param id 组织架构分类主键
     * @return 组织架构分类
     */
    @Override
    public OrganizationCategory selectOrganizationCategoryById(Long id)
    {
        return organizationCategoryMapper.selectOrganizationCategoryById(id);
    }

    /**
     * 查询组织架构分类列表
     * 
     * @param organizationCategory 组织架构分类
     * @return 组织架构分类
     */
    @Override
    public List<OrganizationCategory> selectOrganizationCategoryList(OrganizationCategory organizationCategory)
    {
        return organizationCategoryMapper.selectOrganizationCategoryList(organizationCategory);
    }

    /**
     * 新增组织架构分类
     * 
     * @param organizationCategory 组织架构分类
     * @return 结果
     */
    @Override
    public int insertOrganizationCategory(OrganizationCategory organizationCategory)
    {
        organizationCategory.setCreateTime(DateUtils.getNowDate());
        return organizationCategoryMapper.insertOrganizationCategory(organizationCategory);
    }

    /**
     * 修改组织架构分类
     * 
     * @param organizationCategory 组织架构分类
     * @return 结果
     */
    @Override
    public int updateOrganizationCategory(OrganizationCategory organizationCategory)
    {
        organizationCategory.setUpdateTime(DateUtils.getNowDate());
        return organizationCategoryMapper.updateOrganizationCategory(organizationCategory);
    }

    /**
     * 批量删除组织架构分类
     * 
     * @param ids 需要删除的组织架构分类主键
     * @return 结果
     */
    @Override
    public int deleteOrganizationCategoryByIds(Long[] ids)
    {
        return organizationCategoryMapper.deleteOrganizationCategoryByIds(ids);
    }

    /**
     * 删除组织架构分类信息
     * 
     * @param id 组织架构分类主键
     * @return 结果
     */
    @Override
    public int deleteOrganizationCategoryById(Long id)
    {
        return organizationCategoryMapper.deleteOrganizationCategoryById(id);
    }
} 