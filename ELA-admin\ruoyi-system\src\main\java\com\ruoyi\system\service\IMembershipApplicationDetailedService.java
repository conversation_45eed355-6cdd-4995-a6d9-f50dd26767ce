package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.MembershipApplicationDetailed;

/**
 * 详细会员申请表Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IMembershipApplicationDetailedService 
{
    /**
     * 查询详细会员申请表
     * 
     * @param id 详细会员申请表主键
     * @return 详细会员申请表
     */
    public MembershipApplicationDetailed selectMembershipApplicationDetailedById(Long id);

    /**
     * 查询详细会员申请表列表
     * 
     * @param membershipApplicationDetailed 详细会员申请表
     * @return 详细会员申请表集合
     */
    public List<MembershipApplicationDetailed> selectMembershipApplicationDetailedList(MembershipApplicationDetailed membershipApplicationDetailed);

    /**
     * 新增详细会员申请表
     * 
     * @param membershipApplicationDetailed 详细会员申请表
     * @return 结果
     */
    public int insertMembershipApplicationDetailed(MembershipApplicationDetailed membershipApplicationDetailed);

    /**
     * 修改详细会员申请表
     * 
     * @param membershipApplicationDetailed 详细会员申请表
     * @return 结果
     */
    public int updateMembershipApplicationDetailed(MembershipApplicationDetailed membershipApplicationDetailed);

    /**
     * 批量删除详细会员申请表
     * 
     * @param ids 需要删除的详细会员申请表主键集合
     * @return 结果
     */
    public int deleteMembershipApplicationDetailedByIds(Long[] ids);

    /**
     * 删除详细会员申请表信息
     * 
     * @param id 详细会员申请表主键
     * @return 结果
     */
    public int deleteMembershipApplicationDetailedById(Long id);

    /**
     * 根据当前步骤查询申请列表
     * 
     * @param currentStep 当前步骤
     * @return 申请列表
     */
    public List<MembershipApplicationDetailed> selectByCurrentStep(Integer currentStep);

    /**
     * 根据处理状态查询申请列表
     * 
     * @param status 处理状态
     * @return 申请列表
     */
    public List<MembershipApplicationDetailed> selectByStatus(Integer status);

    /**
     * 更新申请步骤
     * 
     * @param id 申请ID
     * @param currentStep 当前步骤
     * @return 结果
     */
    public int updateCurrentStep(Long id, Integer currentStep);

    /**
     * 更新申请状态
     * 
     * @param id 申请ID
     * @param status 状态
     * @param processNotes 处理备注
     * @param processedBy 处理人
     * @return 结果
     */
    public int updateStatus(Long id, Integer status, String processNotes, String processedBy);

    /**
     * 提交申请到下一步
     * 
     * @param id 申请ID
     * @return 结果
     */
    public int submitToNextStep(Long id);

    /**
     * 完成申请
     * 
     * @param id 申请ID
     * @return 结果
     */
    public int completeApplication(Long id);

    /**
     * 审核申请
     * 
     * @param id 申请ID
     * @param approved 是否通过
     * @param processNotes 处理备注
     * @param processedBy 处理人
     * @return 结果
     */
    public int reviewApplication(Long id, boolean approved, String processNotes, String processedBy);
}