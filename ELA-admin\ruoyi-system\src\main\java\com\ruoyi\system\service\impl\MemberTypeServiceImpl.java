package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.MemberTypeMapper;
import com.ruoyi.system.domain.MemberType;
import com.ruoyi.system.service.IMemberTypeService;

/**
 * 成员类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-01
 */
@Service
public class MemberTypeServiceImpl implements IMemberTypeService 
{
    @Autowired
    private MemberTypeMapper memberTypeMapper;

    /**
     * 查询成员类型
     * 
     * @param id 成员类型主键
     * @return 成员类型
     */
    @Override
    public MemberType selectMemberTypeById(Long id)
    {
        return memberTypeMapper.selectMemberTypeById(id);
    }

    /**
     * 查询成员类型列表
     * 
     * @param memberType 成员类型
     * @return 成员类型
     */
    @Override
    public List<MemberType> selectMemberTypeList(MemberType memberType)
    {
        return memberTypeMapper.selectMemberTypeList(memberType);
    }

    /**
     * 新增成员类型
     * 
     * @param memberType 成员类型
     * @return 结果
     */
    @Override
    public int insertMemberType(MemberType memberType)
    {
        memberType.setCreateTime(DateUtils.getNowDate());
        return memberTypeMapper.insertMemberType(memberType);
    }

    /**
     * 修改成员类型
     * 
     * @param memberType 成员类型
     * @return 结果
     */
    @Override
    public int updateMemberType(MemberType memberType)
    {
        memberType.setUpdateTime(DateUtils.getNowDate());
        return memberTypeMapper.updateMemberType(memberType);
    }

    /**
     * 批量删除成员类型
     * 
     * @param ids 需要删除的成员类型主键
     * @return 结果
     */
    @Override
    public int deleteMemberTypeByIds(Long[] ids)
    {
        return memberTypeMapper.deleteMemberTypeByIds(ids);
    }

    /**
     * 删除成员类型信息
     * 
     * @param id 成员类型主键
     * @return 结果
     */
    @Override
    public int deleteMemberTypeById(Long id)
    {
        return memberTypeMapper.deleteMemberTypeById(id);
    }

    /**
     * 根据类型代码查询成员类型
     * 
     * @param typeCode 类型代码
     * @return 成员类型
     */
    @Override
    public MemberType selectMemberTypeByCode(String typeCode)
    {
        return memberTypeMapper.selectMemberTypeByCode(typeCode);
    }

    /**
     * 检查类型代码是否唯一
     * 
     * @param typeCode 类型代码
     * @return 结果
     */
    @Override
    public String checkTypeCodeUnique(String typeCode)
    {
        int count = memberTypeMapper.checkTypeCodeUnique(typeCode);
        if (count > 0)
        {
            return "1";
        }
        return "0";
    }

    /**
     * 获取成员类型选项列表（用于下拉框）
     * 
     * @return 成员类型选项列表
     */
    @Override
    public List<MemberType> selectMemberTypeOptions()
    {
        return memberTypeMapper.selectMemberTypeOptions();
    }

    /**
     * 根据组织架构分类ID获取成员类型选项列表
     * 
     * @param categoryId 组织架构分类ID
     * @return 成员类型选项列表
     */
    @Override
    public List<MemberType> selectMemberTypeOptionsByCategoryId(Long categoryId)
    {
        return memberTypeMapper.selectMemberTypeOptionsByCategoryId(categoryId);
    }
} 