import request from '@/utils/request'

// 获取首页数据
export function getHomeData() {
  return request({
    url: '/web/home/<USER>',
    method: 'get'
  })
}

// 获取首页统计数据
export function getHomeStats() {
  return request({
    url: '/web/home/<USER>',
    method: 'get'
  })
}

// 获取首页活动列表
export function getHomeEvents(params) {
  return request({
    url: '/web/home/<USER>',
    method: 'get',
    params
  })
}

// 获取首页轮播图
export function getHomeBanners() {
  return request({
    url: '/web/home/<USER>',
    method: 'get'
  })
}

// 获取首页新闻列表
export function getHomeNews(params) {
  return request({
    url: '/web/home/<USER>',
    method: 'get',
    params
  })
} 