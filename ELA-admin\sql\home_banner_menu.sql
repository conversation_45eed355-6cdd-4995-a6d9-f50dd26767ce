-- 首页Banner管理菜单 SQL
-- 1. 先添加网站管理一级菜单（如果不存在）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('网站管理', '0', '5', 'website', '', 1, 0, 'M', '0', '0', '', 'website', 'admin', sysdate(), '', null, '网站管理目录');

-- 获取网站管理菜单ID
SELECT @websiteMenuId := LAST_INSERT_ID();

-- 2. 添加首页管理菜单（网站管理的子菜单）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('首页管理', @websiteMenuId, '1', 'home', '', 1, 0, 'M', '0', '0', '', 'home', 'admin', sysdate(), '', null, '首页管理菜单');

-- 获取首页管理菜单ID
SELECT @homeMenuId := LAST_INSERT_ID();

-- 3. 添加首页Banner管理菜单（首页管理的子菜单）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('首页Banner', @homeMenuId, '1', 'banner', 'website/home/<USER>/index', 1, 0, 'C', '0', '0', 'website:home:banner:list', 'picture', 'admin', sysdate(), '', null, '首页Banner菜单');

-- 获取首页Banner菜单ID
SELECT @bannerMenuId := LAST_INSERT_ID();

-- 4. 添加首页Banner管理的按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('首页Banner查询', @bannerMenuId, '1', '#', '', 1, 0, 'F', '0', '0', 'website:home:banner:query', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('首页Banner新增', @bannerMenuId, '2', '#', '', 1, 0, 'F', '0', '0', 'website:home:banner:add', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('首页Banner修改', @bannerMenuId, '3', '#', '', 1, 0, 'F', '0', '0', 'website:home:banner:edit', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('首页Banner删除', @bannerMenuId, '4', '#', '', 1, 0, 'F', '0', '0', 'website:home:banner:remove', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('首页Banner导出', @bannerMenuId, '5', '#', '', 1, 0, 'F', '0', '0', 'website:home:banner:export', '#', 'admin', sysdate(), '', null, '');

-- 说明：
-- 1. 此SQL会创建完整的菜单结构：网站管理 > 首页管理 > 首页Banner
-- 2. 包含了所有必要的按钮权限：查询、新增、修改、删除、导出
-- 3. 权限标识符为：website:home:banner:*
-- 4. 组件路径为：website/home/<USER>/index
-- 5. 执行此SQL后，管理员用户就可以在后台看到并访问首页Banner管理页面 