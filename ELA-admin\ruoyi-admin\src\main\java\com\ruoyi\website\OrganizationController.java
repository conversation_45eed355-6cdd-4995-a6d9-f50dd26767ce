package com.ruoyi.website;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.OrganizationCategory;
import com.ruoyi.system.domain.OrganizationMember;
import com.ruoyi.system.service.IOrganizationCategoryService;
import com.ruoyi.system.service.IOrganizationMemberService;
import java.util.stream.Collectors;
import java.util.LinkedHashMap;

/**
 * 组织架构前端Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/web/organization")
public class OrganizationController extends BaseController
{
    @Autowired
    private IOrganizationCategoryService organizationCategoryService;

    @Autowired
    private IOrganizationMemberService organizationMemberService;

    /**
     * 获取组织架构分类列表
     */
    @GetMapping("/categories")
    public AjaxResult getCategories() {
        List<OrganizationCategory> categories = organizationCategoryService.selectOrganizationCategoryList(new OrganizationCategory());
        return AjaxResult.success(categories);
    }

    /**
     * 根据分类ID获取组织架构数据
     */
    @GetMapping("/data/{categoryId}")
    public AjaxResult getOrganizationData(@PathVariable Long categoryId) {
        try {
            // 获取分类信息
            OrganizationCategory category = organizationCategoryService.selectOrganizationCategoryById(categoryId);
            if (category == null) {
                return AjaxResult.error("分类不存在");
            }

            // 获取该分类下的所有成员
            OrganizationMember queryMember = new OrganizationMember();
            queryMember.setCategoryId(categoryId);
            queryMember.setStatus("0");
            List<OrganizationMember> members = organizationMemberService.selectOrganizationMemberList(queryMember);

            // 按成员类型分组（使用LinkedHashMap保持排序）
            Map<String, List<OrganizationMember>> groupedMembers = members.stream()
                    .filter(member -> member.getMemberTypeInfo() != null)
                    .sorted((a, b) -> {
                        // 首先按成员类型的排序顺序排序
                        int typeSort = Integer.compare(
                            a.getMemberTypeInfo().getSortOrder() != null ? a.getMemberTypeInfo().getSortOrder() : 999,
                            b.getMemberTypeInfo().getSortOrder() != null ? b.getMemberTypeInfo().getSortOrder() : 999
                        );
                        if (typeSort != 0) return typeSort;
                        
                        // 同一类型内按成员的排序顺序排序
                        return Integer.compare(
                            a.getSortOrder() != null ? a.getSortOrder() : 999,
                            b.getSortOrder() != null ? b.getSortOrder() : 999
                        );
                    })
                    .collect(Collectors.groupingBy(
                        member -> member.getMemberTypeInfo().getTypeCode(),
                        LinkedHashMap::new,
                        Collectors.toList()
                    ));

            // 构造返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("category", category);
            data.put("members", members);
            data.put("groupedMembers", groupedMembers);

            return AjaxResult.success(data);
        } catch (Exception e) {
            return AjaxResult.error("获取数据失败");
        }
    }

    /**
     * 根据分类名称获取组织架构数据
     */
    @GetMapping("/data/name/{categoryName}")
    public AjaxResult getOrganizationDataByName(@PathVariable String categoryName) {
        try {
            // 根据中文名称查找分类
            OrganizationCategory queryCategory = new OrganizationCategory();
            queryCategory.setNameZh(categoryName);
            List<OrganizationCategory> categories = organizationCategoryService.selectOrganizationCategoryList(queryCategory);
            
            if (categories.isEmpty()) {
                return AjaxResult.error("分类不存在");
            }
            
            OrganizationCategory category = categories.get(0);
            
            // 获取该分类下的所有成员
            OrganizationMember queryMember = new OrganizationMember();
            queryMember.setCategoryId(category.getId());
            queryMember.setStatus("0");
            List<OrganizationMember> members = organizationMemberService.selectOrganizationMemberList(queryMember);

            // 按成员类型分组（使用LinkedHashMap保持排序）
            Map<String, List<OrganizationMember>> groupedMembers = members.stream()
                    .filter(member -> member.getMemberTypeInfo() != null)
                    .sorted((a, b) -> {
                        // 首先按成员类型的排序顺序排序
                        int typeSort = Integer.compare(
                            a.getMemberTypeInfo().getSortOrder() != null ? a.getMemberTypeInfo().getSortOrder() : 999,
                            b.getMemberTypeInfo().getSortOrder() != null ? b.getMemberTypeInfo().getSortOrder() : 999
                        );
                        if (typeSort != 0) return typeSort;
                        
                        // 同一类型内按成员的排序顺序排序
                        return Integer.compare(
                            a.getSortOrder() != null ? a.getSortOrder() : 999,
                            b.getSortOrder() != null ? b.getSortOrder() : 999
                        );
                    })
                    .collect(Collectors.groupingBy(
                        member -> member.getMemberTypeInfo().getTypeCode(),
                        LinkedHashMap::new,
                        Collectors.toList()
                    ));

            // 构造返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("category", category);
            data.put("members", members);
            data.put("groupedMembers", groupedMembers);

            return AjaxResult.success(data);
        } catch (Exception e) {
            return AjaxResult.error("获取数据失败");
        }
    }

    /**
     * 获取特定类型的成员列表
     */
    @GetMapping("/members/{categoryId}/{memberType}")
    public AjaxResult getMembersByType(@PathVariable Long categoryId, 
                                      @PathVariable Long memberType) {
        try {
            // 先获取分类信息
            OrganizationCategory category = organizationCategoryService.selectOrganizationCategoryById(categoryId);
            if (category == null) {
                return AjaxResult.error("分类不存在");
            }

            // 获取特定类型的成员
            OrganizationMember memberQuery = new OrganizationMember();
            memberQuery.setCategoryId(categoryId);
            memberQuery.setMemberType(memberType);
            memberQuery.setStatus("0");
            List<OrganizationMember> members = organizationMemberService.selectOrganizationMemberList(memberQuery);

            return AjaxResult.success(members);
        } catch (Exception e) {
            return AjaxResult.error("获取数据失败");
        }
    }

    /**
     * 获取领导层成员
     */
    @GetMapping("/leadership/{categoryId}")
    public AjaxResult getLeadershipMembers(@PathVariable Long categoryId) {
        try {
            // 先获取分类信息
            OrganizationCategory category = organizationCategoryService.selectOrganizationCategoryById(categoryId);
            if (category == null) {
                return AjaxResult.error("分类不存在");
            }

            // 获取领导层成员
            OrganizationMember memberQuery = new OrganizationMember();
            memberQuery.setCategoryId(categoryId);
            memberQuery.setIsLeadership(1);
            memberQuery.setStatus("0");
            List<OrganizationMember> members = organizationMemberService.selectOrganizationMemberList(memberQuery);

            return AjaxResult.success(members);
        } catch (Exception e) {
            return AjaxResult.error("获取数据失败");
        }
    }

    /**
     * 获取特定类型的成员列表（按类型名称）
     */
    @GetMapping("/members/{categoryId}/type/{memberTypeName}")
    public AjaxResult getMembersByTypeName(@PathVariable Long categoryId, 
                                          @PathVariable String memberTypeName) {
        try {
            // 先获取分类信息
            OrganizationCategory category = organizationCategoryService.selectOrganizationCategoryById(categoryId);
            if (category == null) {
                return AjaxResult.error("分类不存在");
            }

            // 获取该分类下的所有成员
            OrganizationMember memberQuery = new OrganizationMember();
            memberQuery.setCategoryId(categoryId);
            memberQuery.setStatus("0");
            List<OrganizationMember> allMembers = organizationMemberService.selectOrganizationMemberList(memberQuery);

            // 过滤出指定类型的成员
            List<OrganizationMember> members = allMembers.stream()
                    .filter(member -> member.getMemberTypeInfo() != null && 
                            memberTypeName.equals(member.getMemberTypeInfo().getNameZh()))
                    .collect(Collectors.toList());

            return AjaxResult.success(members);
        } catch (Exception e) {
            return AjaxResult.error("获取数据失败");
        }
    }
} 