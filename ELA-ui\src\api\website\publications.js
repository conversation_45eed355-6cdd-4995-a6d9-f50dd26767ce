import request from '@/utils/request'

// 查询资讯管理列表
export function listPublications(query) {
  return request({
    url: '/website/publications/list',
    method: 'get',
    params: query
  })
}

// 查询资讯管理详细
export function getPublication(id) {
  return request({
    url: '/website/publications/' + id,
    method: 'get'
  })
}

// 新增资讯管理
export function addPublication(data) {
  return request({
    url: '/website/publications',
    method: 'post',
    data: data
  })
}

// 修改资讯管理
export function updatePublication(data) {
  return request({
    url: '/website/publications',
    method: 'put',
    data: data
  })
}

// 删除资讯管理
export function delPublication(id) {
  return request({
    url: '/website/publications/' + id,
    method: 'delete'
  })
}

// 查询最新资讯列表
export function listLatestPublications() {
  return request({
    url: '/api/publications/latest',
    method: 'get'
  })
}

// 查询公开资讯列表
export function listPublicPublications() {
  return request({
    url: '/api/publications/public',
    method: 'get'
  })
}