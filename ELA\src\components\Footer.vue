<template>
  <footer class="site-footer">
    <!-- 主要内容区域 -->
    <div class="footer-main" :style="{ backgroundColor: '#d86337', color: '#ffffff' }">
      <div class="container">
        <div class="footer-content">
          <!-- Logo区域 -->
          <div class="footer-logo">
            <router-link to="/">
              <img src="@/assets/logo.png" alt="HKELA Logo" class="logo-img" />
            </router-link>
          </div>
          
          <!-- 菜单区域 -->
          <div class="footer-menu">
            <nav class="footer-nav">
              <template v-for="(item, index) in computedMenuItems">
                <router-link 
                  v-if="!item.children || item.children.length === 0"
                  :key="`link-${index}`"
                  :to="item.path" 
                  class="footer-nav-link"
                >
                  {{ currentLang === 'zh' ? item.nameZh : item.nameEn }}
                </router-link>
                
                <div 
                  v-else
                  :key="`dropdown-${index}`"
                  class="footer-nav-dropdown"
                >
                  <span class="footer-nav-link dropdown-toggle">
                    {{ currentLang === 'zh' ? item.nameZh : item.nameEn }}
                  </span>
                  <div class="footer-dropdown-items">
                    <router-link 
                      v-for="child in item.children"
                      :key="child.path"
                      :to="child.path"
                      class="footer-dropdown-item"
                    >
                      {{ currentLang === 'zh' ? child.nameZh : child.nameEn }}
                    </router-link>
                  </div>
                </div>
              </template>
            </nav>
          </div>
        </div>
        
        <!-- 版权信息区域 -->
        <div class="footer-copyright-inline">
          <!-- <p class="copyright-text">{{ copyrightText || '©2023香港电子商务物流协会集团有限公司 浙ICP备17038086号-2' }}</p> -->
          <p class="association-name">The Hong Kong E-commerce Logistics Association (HKELA)</p>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
import { getFooterConfig, getFooterLinks } from '@/api/website/footerConfig'
import { getOrganizationCategories } from '@/api/organization'

export default {
  name: 'Footer',
  data() {
    return {
      footerConfigs: [],
      footerLinks: [],
      currentLang: 'zh',
      // 菜单项
      menuItems: [
        {
          path: '/',
          nameZh: '首頁',
          nameEn: 'Home'
        },
        {
          path: '/about',
          nameZh: '關於我們',
          nameEn: 'About Us',
          children: [
            {
              path: '/about/council',
              nameZh: '理事會',
              nameEn: 'Council'
            },
            {
              path: '/about/advisors',
              nameZh: '顧問',
              nameEn: 'Advisors'
            },
            {
              path: '/about/youth-committee',
              nameZh: '青年委員會',
              nameEn: 'Youth Committee'
            }
          ]
        },
        {
          path: '/pr-events',
          nameZh: '公關及活動',
          nameEn: 'PR & Events'
        },
        // {
        //   path: '/publication',
        //   nameZh: '資訊',
        //   nameEn: 'Publication'
        // },
        {
          path: '/join-us',
          nameZh: '加入我們',
          nameEn: 'Join Us'
        },
        {
          path: '/contact',
          nameZh: '聯繫我們',
          nameEn: 'Contact Us'
        }
      ],
      // 关于我们子菜单
      aboutMenuItems: []
    }
  },
  computed: {
    // 获取二维码配置
    qrCodeConfig() {
      return this.footerConfigs.find(config => config.sectionType === 'qr_code')
    },
    // 获取版权信息
    copyrightText() {
      const copyrightConfig = this.footerConfigs.find(config => config.sectionType === 'copyright')
      return copyrightConfig ? this.parseContent(copyrightConfig.content).copyright_text : null
    },
    // 计算菜单项
    computedMenuItems() {
      // 动态更新关于我们子菜单
      const menuItems = [...this.menuItems]
      const aboutIndex = menuItems.findIndex(item => item.path === '/about')
      if (aboutIndex !== -1) {
        menuItems[aboutIndex].children = this.aboutMenuItems
      }
      return menuItems
    }
  },
  created() {
    // 从localStorage恢复语言设置
    const savedLang = localStorage.getItem('language')
    if (savedLang) {
      this.currentLang = savedLang
    }
    
    this.loadFooterData()
    this.loadOrganizationCategories()
  },
  methods: {
    async loadFooterData() {
      try {
        const [configResponse, linksResponse] = await Promise.all([
          getFooterConfig(),
          getFooterLinks()
        ])

        if (configResponse.data) {
          this.footerConfigs = configResponse.data.sort((a, b) => a.sortOrder - b.sortOrder)
        }

        if (linksResponse.data) {
          this.footerLinks = linksResponse.data
            .filter(link => link.linkType === 'bottom')
            .sort((a, b) => a.sortOrder - b.sortOrder)
        }
      } catch (error) {
        console.error('Failed to load footer data:', error)
        // 设置默认配置
        this.setDefaultFooter()
      }
    },
    parseContent(content) {
      try {
        return JSON.parse(content)
      } catch (error) {
        console.error('Failed to parse footer content:', error)
        return {}
      }
    },
    setDefaultFooter() {
      // 设置默认页脚配置
      this.footerConfigs = [
        {
          id: 3,
          sectionType: 'qr_code',
          title: '微信公众号',
          content: '{"qr_image":"https://www.sinochemlt.com/portals/290/images/erweima.png","label":"微信公众号"}',
          backgroundColor: '#004a9b',
          textColor: '#ffffff',
          sortOrder: 3
        },
        {
          id: 4,
          sectionType: 'copyright',
          title: '版权信息',
          content: '{"copyright_text":"版权所有©2023香港电子商务物流协会集团有限公司 浙ICP备17038086号-2","tech_support":" "}',
          backgroundColor: '#ffffff',
          textColor: '#666666',
          sortOrder: 4
        }
      ]
    },
    /** 加载组织架构分类数据 */
    async loadOrganizationCategories() {
      try {
        const response = await getOrganizationCategories()
        
        // 将分类数据转换为菜单项格式
        this.aboutMenuItems = response.data
          .filter(category => category.status === '0') // 只获取启用的分类
          .sort((a, b) => a.sortOrder - b.sortOrder) // 按排序排列
          .map(category => {
            const slug = this.generateSlug(category.nameEn)
            return {
              path: `/about/${slug}`,
              nameZh: category.nameZh,
              nameEn: category.nameEn,
              categoryId: category.id,
              categoryName: category.nameZh
            }
          })
      } catch (error) {
        console.error('加载组织架构分类失败:', error)
        // 如果API失败，使用默认的硬编码数据作为备选
        this.aboutMenuItems = [
          {
            path: '/about/council',
            nameZh: '理事会',
            nameEn: 'Board of Directors',
            categoryName: '理事会'
          },
          {
            path: '/about/secretariat',
            nameZh: '秘书处',
            nameEn: 'Secretariat',
            categoryName: '秘书处'
          }
        ]
      }
    },
    /** 生成URL友好的slug */
    generateSlug(text) {
      if (!text) return ''
      return text
        .toLowerCase()
        .replace(/[^\w\s-]/g, '') // 移除非单词/空格/连字符字符
        .replace(/[\s_]+/g, '-') // 将空格和下划线替换为连字符
        .replace(/^-+|-+$/g, '') // 移除开头和结尾的连字符
    }
  }
}
</script>

<style scoped>
/* 确保 FontAwesome 被引入 */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

.site-footer {
  font-size: 14px;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 主要内容区域 */
.footer-main {
  padding: 40px 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
}

/* Logo区域 */
.footer-logo {
  margin-right: 40px;
}

.logo-img {
  height: 60px;
  width: auto;
}

/* 菜单区域 */
.footer-menu {
  flex: 1;
}

.footer-nav {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  gap: 30px;
}

.footer-nav-link {
  color: #ffffff;
  text-decoration: none;
  font-size: 15px;
  font-weight: 500;
  position: relative;
  transition: opacity 0.3s ease;
  white-space: nowrap;
}

.footer-nav-link:hover {
  opacity: 0.8;
}

.footer-nav-dropdown {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.dropdown-toggle {
  cursor: pointer;
  white-space: nowrap;
  margin-bottom: 10px;
}

.footer-dropdown-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.footer-dropdown-item {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 13px;
  transition: opacity 0.3s ease;
  white-space: nowrap;
  padding-left: 15px;
  position: relative;
}

.footer-dropdown-item:before {
  content: '•';
  position: absolute;
  left: 0;
  color: rgba(255, 255, 255, 0.6);
}

.footer-dropdown-item:hover {
  opacity: 1;
  color: #ffffff;
}

/* 版权信息区域 */
.footer-copyright-inline {
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 20px;
}

.copyright-text {
  margin: 0;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
}

.association-name {
  margin: 8px 0 0 0;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    gap: 20px;
  }
  
  .footer-logo {
    margin-right: 0;
    text-align: center;
  }
  
  .footer-nav {
    flex-wrap: wrap;
    gap: 10px;
    justify-content: flex-start;
  }
  
  .footer-nav-link {
    font-size: 13px;
  }
  
  .footer-dropdown-item {
    font-size: 12px;
  }
}
</style>
