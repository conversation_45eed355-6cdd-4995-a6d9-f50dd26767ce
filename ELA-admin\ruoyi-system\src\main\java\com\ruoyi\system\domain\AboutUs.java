package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 关于我们对象 about_us
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class AboutUs extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 标题（中文） */
    @Excel(name = "标题（中文）")
    private String titleZh;

    /** 标题（英文） */
    @Excel(name = "标题（英文）")
    private String titleEn;

    /** 副标题（中文） */
    @Excel(name = "副标题（中文）")
    private String subtitleZh;

    /** 副标题（英文） */
    @Excel(name = "副标题（英文）")
    private String subtitleEn;

    /** 公司名称（中文） */
    @Excel(name = "公司名称（中文）")
    private String companyNameZh;

    /** 公司名称（英文） */
    @Excel(name = "公司名称（英文）")
    private String companyNameEn;

    /** 公司描述（中文） */
    @Excel(name = "公司描述（中文）")
    private String descriptionZh;

    /** 公司描述（英文） */
    @Excel(name = "公司描述（英文）")
    private String descriptionEn;

    /** 统计数据配置（JSON格式） */
    @Excel(name = "统计数据配置")
    private String statisticsConfig;

    /** 使命卡片配置（JSON格式） */
    @Excel(name = "使命卡片配置")
    private String missionCardsConfig;

    /** 右侧图片URL */
    @Excel(name = "右侧图片URL")
    private String imageUrl;

    /** 图片描述（中文） */
    @Excel(name = "图片描述（中文）")
    private String imageDescZh;

    /** 图片描述（英文） */
    @Excel(name = "图片描述（英文）")
    private String imageDescEn;

    /** 背景图片URL */
    @Excel(name = "背景图片URL")
    private String backgroundImageUrl;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sort;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 愿景标题（中文） */
    @Excel(name = "愿景标题（中文）")
    private String visionTitleZh;

    /** 愿景标题（英文） */
    @Excel(name = "愿景标题（英文）")
    private String visionTitleEn;

    /** 愿景内容（中文） */
    @Excel(name = "愿景内容（中文）")
    private String visionContentZh;

    /** 愿景内容（英文） */
    @Excel(name = "愿景内容（英文）")
    private String visionContentEn;

    /** 愿景按钮文字（中文） */
    @Excel(name = "愿景按钮文字（中文）")
    private String visionButtonZh;

    /** 愿景按钮文字（英文） */
    @Excel(name = "愿景按钮文字（英文）")
    private String visionButtonEn;

    /** 愿景按钮链接 */
    @Excel(name = "愿景按钮链接")
    private String visionButtonUrl;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setTitleZh(String titleZh)
    {
        this.titleZh = titleZh;
    }

    public String getTitleZh()
    {
        return titleZh;
    }

    public void setTitleEn(String titleEn)
    {
        this.titleEn = titleEn;
    }

    public String getTitleEn()
    {
        return titleEn;
    }

    public void setSubtitleZh(String subtitleZh)
    {
        this.subtitleZh = subtitleZh;
    }

    public String getSubtitleZh()
    {
        return subtitleZh;
    }

    public void setSubtitleEn(String subtitleEn)
    {
        this.subtitleEn = subtitleEn;
    }

    public String getSubtitleEn()
    {
        return subtitleEn;
    }

    public void setCompanyNameZh(String companyNameZh)
    {
        this.companyNameZh = companyNameZh;
    }

    public String getCompanyNameZh()
    {
        return companyNameZh;
    }

    public void setCompanyNameEn(String companyNameEn)
    {
        this.companyNameEn = companyNameEn;
    }

    public String getCompanyNameEn()
    {
        return companyNameEn;
    }

    public void setDescriptionZh(String descriptionZh)
    {
        this.descriptionZh = descriptionZh;
    }

    public String getDescriptionZh()
    {
        return descriptionZh;
    }

    public void setDescriptionEn(String descriptionEn)
    {
        this.descriptionEn = descriptionEn;
    }

    public String getDescriptionEn()
    {
        return descriptionEn;
    }

    public void setStatisticsConfig(String statisticsConfig)
    {
        this.statisticsConfig = statisticsConfig;
    }

    public String getStatisticsConfig()
    {
        return statisticsConfig;
    }

    public void setMissionCardsConfig(String missionCardsConfig)
    {
        this.missionCardsConfig = missionCardsConfig;
    }

    public String getMissionCardsConfig()
    {
        return missionCardsConfig;
    }

    public void setImageUrl(String imageUrl)
    {
        this.imageUrl = imageUrl;
    }

    public String getImageUrl()
    {
        return imageUrl;
    }

    public void setImageDescZh(String imageDescZh)
    {
        this.imageDescZh = imageDescZh;
    }

    public String getImageDescZh()
    {
        return imageDescZh;
    }

    public void setImageDescEn(String imageDescEn)
    {
        this.imageDescEn = imageDescEn;
    }

    public String getImageDescEn()
    {
        return imageDescEn;
    }

    public void setBackgroundImageUrl(String backgroundImageUrl)
    {
        this.backgroundImageUrl = backgroundImageUrl;
    }

    public String getBackgroundImageUrl()
    {
        return backgroundImageUrl;
    }

    public void setSort(Integer sort)
    {
        this.sort = sort;
    }

    public Integer getSort()
    {
        return sort;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setVisionTitleZh(String visionTitleZh)
    {
        this.visionTitleZh = visionTitleZh;
    }

    public String getVisionTitleZh()
    {
        return visionTitleZh;
    }

    public void setVisionTitleEn(String visionTitleEn)
    {
        this.visionTitleEn = visionTitleEn;
    }

    public String getVisionTitleEn()
    {
        return visionTitleEn;
    }

    public void setVisionContentZh(String visionContentZh)
    {
        this.visionContentZh = visionContentZh;
    }

    public String getVisionContentZh()
    {
        return visionContentZh;
    }

    public void setVisionContentEn(String visionContentEn)
    {
        this.visionContentEn = visionContentEn;
    }

    public String getVisionContentEn()
    {
        return visionContentEn;
    }

    public void setVisionButtonZh(String visionButtonZh)
    {
        this.visionButtonZh = visionButtonZh;
    }

    public String getVisionButtonZh()
    {
        return visionButtonZh;
    }

    public void setVisionButtonEn(String visionButtonEn)
    {
        this.visionButtonEn = visionButtonEn;
    }

    public String getVisionButtonEn()
    {
        return visionButtonEn;
    }

    public void setVisionButtonUrl(String visionButtonUrl)
    {
        this.visionButtonUrl = visionButtonUrl;
    }

    public String getVisionButtonUrl()
    {
        return visionButtonUrl;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("titleZh", getTitleZh())
            .append("titleEn", getTitleEn())
            .append("subtitleZh", getSubtitleZh())
            .append("subtitleEn", getSubtitleEn())
            .append("companyNameZh", getCompanyNameZh())
            .append("companyNameEn", getCompanyNameEn())
            .append("descriptionZh", getDescriptionZh())
            .append("descriptionEn", getDescriptionEn())
            .append("statisticsConfig", getStatisticsConfig())
            .append("missionCardsConfig", getMissionCardsConfig())
            .append("imageUrl", getImageUrl())
            .append("imageDescZh", getImageDescZh())
            .append("imageDescEn", getImageDescEn())
            .append("backgroundImageUrl", getBackgroundImageUrl())
            .append("sort", getSort())
            .append("status", getStatus())
            .append("visionTitleZh", getVisionTitleZh())
            .append("visionTitleEn", getVisionTitleEn())
            .append("visionContentZh", getVisionContentZh())
            .append("visionContentEn", getVisionContentEn())
            .append("visionButtonZh", getVisionButtonZh())
            .append("visionButtonEn", getVisionButtonEn())
            .append("visionButtonUrl", getVisionButtonUrl())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 