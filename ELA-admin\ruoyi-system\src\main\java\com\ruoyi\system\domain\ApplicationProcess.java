package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 申请流程对象 application_process
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
public class ApplicationProcess extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 流程ID */
    private Long id;

    /** 步骤编号 */
    @Excel(name = "步骤编号")
    private Integer stepNumber;

    /** 中文标题 */
    @Excel(name = "中文标题")
    private String titleZh;

    /** 英文标题 */
    @Excel(name = "英文标题")
    private String titleEn;

    /** 中文描述 */
    @Excel(name = "中文描述")
    private String descriptionZh;

    /** 英文描述 */
    @Excel(name = "英文描述")
    private String descriptionEn;

    /** 图标类名 */
    @Excel(name = "图标类名")
    private String icon;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setStepNumber(Integer stepNumber) 
    {
        this.stepNumber = stepNumber;
    }

    public Integer getStepNumber() 
    {
        return stepNumber;
    }
    public void setTitleZh(String titleZh) 
    {
        this.titleZh = titleZh;
    }

    public String getTitleZh() 
    {
        return titleZh;
    }
    public void setTitleEn(String titleEn) 
    {
        this.titleEn = titleEn;
    }

    public String getTitleEn() 
    {
        return titleEn;
    }
    public void setDescriptionZh(String descriptionZh) 
    {
        this.descriptionZh = descriptionZh;
    }

    public String getDescriptionZh() 
    {
        return descriptionZh;
    }
    public void setDescriptionEn(String descriptionEn) 
    {
        this.descriptionEn = descriptionEn;
    }

    public String getDescriptionEn() 
    {
        return descriptionEn;
    }
    public void setIcon(String icon) 
    {
        this.icon = icon;
    }

    public String getIcon() 
    {
        return icon;
    }
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("stepNumber", getStepNumber())
            .append("titleZh", getTitleZh())
            .append("titleEn", getTitleEn())
            .append("descriptionZh", getDescriptionZh())
            .append("descriptionEn", getDescriptionEn())
            .append("icon", getIcon())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 