package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 领导力消息对象 leadership_messages
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class LeadershipMessage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 消息类型（如：名譽顧問、會長、副會長等） */
    @Excel(name = "消息类型")
    private String type;

    /** 标题（中文） */
    @Excel(name = "标题（中文）")
    private String titleZh;

    /** 标题（英文） */
    @Excel(name = "标题（英文）")
    private String titleEn;

    /** 姓名（中文） */
    @Excel(name = "姓名（中文）")
    private String nameZh;

    /** 姓名（英文） */
    @Excel(name = "姓名（英文）")
    private String nameEn;

    /** 职位（中文） */
    @Excel(name = "职位（中文）")
    private String positionZh;

    /** 职位（英文） */
    @Excel(name = "职位（英文）")
    private String positionEn;

    /** 公司/机构（中文） */
    @Excel(name = "公司/机构（中文）")
    private String companyZh;

    /** 公司/机构（英文） */
    @Excel(name = "公司/机构（英文）")
    private String companyEn;

    /** 消息内容（中文） */
    @Excel(name = "消息内容（中文）")
    private String messageZh;

    /** 消息内容（英文） */
    @Excel(name = "消息内容（英文）")
    private String messageEn;

    /** 头像图片URL */
    @Excel(name = "头像图片URL")
    private String imageUrl;

    /** 背景图片URL */
    @Excel(name = "背景图片URL")
    private String backgroundImageUrl;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    public void setTitleZh(String titleZh) 
    {
        this.titleZh = titleZh;
    }

    public String getTitleZh() 
    {
        return titleZh;
    }

    public void setTitleEn(String titleEn) 
    {
        this.titleEn = titleEn;
    }

    public String getTitleEn() 
    {
        return titleEn;
    }

    public void setNameZh(String nameZh) 
    {
        this.nameZh = nameZh;
    }

    public String getNameZh() 
    {
        return nameZh;
    }

    public void setNameEn(String nameEn) 
    {
        this.nameEn = nameEn;
    }

    public String getNameEn() 
    {
        return nameEn;
    }

    public void setPositionZh(String positionZh) 
    {
        this.positionZh = positionZh;
    }

    public String getPositionZh() 
    {
        return positionZh;
    }

    public void setPositionEn(String positionEn) 
    {
        this.positionEn = positionEn;
    }

    public String getPositionEn() 
    {
        return positionEn;
    }

    public void setCompanyZh(String companyZh) 
    {
        this.companyZh = companyZh;
    }

    public String getCompanyZh() 
    {
        return companyZh;
    }

    public void setCompanyEn(String companyEn) 
    {
        this.companyEn = companyEn;
    }

    public String getCompanyEn() 
    {
        return companyEn;
    }

    public void setMessageZh(String messageZh) 
    {
        this.messageZh = messageZh;
    }

    public String getMessageZh() 
    {
        return messageZh;
    }

    public void setMessageEn(String messageEn) 
    {
        this.messageEn = messageEn;
    }

    public String getMessageEn() 
    {
        return messageEn;
    }

    public void setImageUrl(String imageUrl) 
    {
        this.imageUrl = imageUrl;
    }

    public String getImageUrl() 
    {
        return imageUrl;
    }

    public void setBackgroundImageUrl(String backgroundImageUrl) 
    {
        this.backgroundImageUrl = backgroundImageUrl;
    }

    public String getBackgroundImageUrl() 
    {
        return backgroundImageUrl;
    }

    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("type", getType())
            .append("titleZh", getTitleZh())
            .append("titleEn", getTitleEn())
            .append("nameZh", getNameZh())
            .append("nameEn", getNameEn())
            .append("positionZh", getPositionZh())
            .append("positionEn", getPositionEn())
            .append("companyZh", getCompanyZh())
            .append("companyEn", getCompanyEn())
            .append("messageZh", getMessageZh())
            .append("messageEn", getMessageEn())
            .append("imageUrl", getImageUrl())
            .append("backgroundImageUrl", getBackgroundImageUrl())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 