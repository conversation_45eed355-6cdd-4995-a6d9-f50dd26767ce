<template>
  <div class="news-page">
    <!-- 大背景Banner -->
    <section class="hero-banner">
      <div class="hero-overlay"></div>
      <div class="hero-content">
        <h1 class="hero-title">新闻中心</h1>
        <p class="hero-subtitle">了解香港电子商务物流协会最新动态与行业资讯</p>
      </div>
    </section>

    <!-- 新闻分类标签 -->
    <section class="news-tabs-section">
      <div class="container">
        <div class="news-tabs">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            class="news-tab"
            :class="{ active: activeTab === tab.id }"
            @click="switchTab(tab.id)"
          >
            {{ tab.name }}
          </button>
        </div>
      </div>
    </section>

    <!-- 新闻列表 -->
    <section class="news-list-section">
      <div class="container">
        <div class="news-grid">
          <div v-for="news in currentNewsList" :key="news.id" class="news-item">
            <div class="news-image">
              <img :src="news.image" :alt="news.title" />
            </div>
            <div class="news-content">
              <h3 class="news-title">{{ news.title }}</h3>
              <p class="news-date">{{ news.date }}</p>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination">
          <button
            v-for="page in totalPages"
            :key="page"
            class="page-btn"
            :class="{ active: currentPage === page }"
            @click="changePage(page)"
          >
            {{ page }}
          </button>
          <span class="pagination-info">下一页 末页 第 1 /16 总条数 190</span>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'News',
  data() {
    return {
      activeTab: 'enterprise',
      currentPage: 1,
      totalPages: 16,
      tabs: [
        { id: 'enterprise', name: '企业动态' },
        { id: 'media', name: '媒体聚焦' }
      ],
      enterpriseNews: [
        {
          id: 1,
          title: '国际领先！香港电子商务物流协会HFC-23资源化转化技术通过中国石油和化学工业联合会科技成果...',
          date: '2025-06-04',
          image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 2,
          title: '香港电子商务物流协会亮相CIBF2025',
          date: '2025-05-17',
          image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 3,
          title: '香港电子商务物流协会亮相2025中国制冷展',
          date: '2025-04-29',
          image: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 4,
          title: '中昊晨光年产2.6万吨高性能有机氟材料项目主体装置建成投产',
          date: '2025-03-28',
          image: 'https://images.unsplash.com/photo-1587293852726-70cdb56c2866?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 5,
          title: '香港电子商务物流协会氟橡胶产品荣获国家专利密集型产品认定',
          date: '2025-03-14',
          image: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 6,
          title: '香港电子商务物流协会三氟甲烷转化技术入选《国家重点推广的低碳技术目录(第四批)》',
          date: '2025-02-14',
          image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 7,
          title: '香港电子商务物流协会完成七款产品碳足迹认证',
          date: '2024-12-13',
          image: 'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 8,
          title: '香港电子商务物流协会荣获江苏自控供应链持续稳定奖',
          date: '2024-12-05',
          image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 9,
          title: '香港电子商务物流协会成功举办昊华科技投资者交流会',
          date: '2024-11-28',
          image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 10,
          title: '香港电子商务物流协会在MOP36中国履行《蒙特利尔议定书》网约进展边会上...',
          date: '2024-11-01',
          image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 11,
          title: '香港电子商务物流协会亮相ACE2024',
          date: '2024-10-16',
          image: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 12,
          title: '香港电子商务物流协会亮相德国制冷展',
          date: '2024-10-10',
          image: 'https://images.unsplash.com/photo-1556155092-490a1ba16284?w=400&h=300&fit=crop&q=80'
        }
      ],
      mediaNews: [
        {
          id: 1,
          title: '媒体聚焦：香港电子商务物流协会创新发展获行业认可',
          date: '2025-05-20',
          image: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 2,
          title: '权威报道：香港电子商务物流协会环保技术引领行业变革',
          date: '2025-04-15',
          image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 3,
          title: '央视专访：香港电子商务物流协会推动绿色化工发展',
          date: '2025-03-10',
          image: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 4,
          title: '人民日报：香港电子商务物流协会科技创新助力碳中和',
          date: '2025-02-25',
          image: 'https://images.unsplash.com/photo-1568952433726-3896e3881c65?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 5,
          title: '经济日报：香港电子商务物流协会数字化转型见成效',
          date: '2025-01-18',
          image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop&q=80'
        },
        {
          id: 6,
          title: '科技日报：香港电子商务物流协会氟化工技术获国际认可',
          date: '2024-12-20',
          image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop&q=80'
        }
      ]
    }
  },
  computed: {
    currentNewsList() {
      return this.activeTab === 'enterprise' ? this.enterpriseNews : this.mediaNews
    }
  },
  methods: {
    switchTab(tabId) {
      this.activeTab = tabId
      this.currentPage = 1
    },
    changePage(page) {
      this.currentPage = page
    }
  }
}
</script>

<style scoped>
.news-page {
  background-color: #fff;
}

/* Hero Banner */
.hero-banner {
  height: 500px;
  background-image: url('https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=1920&h=800&fit=crop&q=80');
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.2rem;
  font-weight: 300;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.6;
  opacity: 0.9;
}

/* 新闻分类标签 */
.news-tabs-section {
  background-color: #f8f9fa;
  padding: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.news-tabs {
  display: flex;
  justify-content: center;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.news-tab {
  background: none;
  border: none;
  padding: 20px 40px;
  font-size: 18px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  position: relative;
  transition: color 0.3s ease;
}

.news-tab:hover {
  color: #0056b3;
}

.news-tab.active {
  color: #0056b3;
}

.news-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background-color: #0056b3;
}

/* 新闻列表 */
.news-list-section {
  padding: 60px 0;
  background-color: #f8f9fa;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  margin-bottom: 60px;
}

.news-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.news-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.news-image {
  width: 200px;
  height: 150px;
  flex-shrink: 0;
  overflow: hidden;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.news-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 1.5;
  margin: 0 0 10px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-date {
  font-size: 14px;
  color: #888;
  margin: 0;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.page-btn {
  width: 40px;
  height: 40px;
  border: 1px solid #ddd;
  background: white;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-btn:hover {
  border-color: #0056b3;
  color: #0056b3;
}

.page-btn.active {
  background-color: #0056b3;
  border-color: #0056b3;
  color: white;
}

.pagination-info {
  margin-left: 20px;
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-banner {
    height: 400px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .news-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .news-item {
    flex-direction: column;
  }

  .news-image {
    width: 100%;
    height: 200px;
  }

  .news-tabs {
    flex-direction: column;
  }

  .news-tab {
    padding: 15px 20px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .hero-banner {
    height: 300px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 0.9rem;
  }

  .hero-content {
    padding: 0 1rem;
  }
}
</style>
