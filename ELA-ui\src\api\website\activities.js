import request from '@/utils/request'

// 查询活动管理列表
export function listActivities(query) {
  return request({
    url: '/website/activities/list',
    method: 'get',
    params: query
  })
}

// 查询活动管理详细
export function getActivity(id) {
  return request({
    url: '/website/activities/' + id,
    method: 'get'
  })
}

// 新增活动管理
export function addActivity(data) {
  return request({
    url: '/website/activities',
    method: 'post',
    data: data
  })
}

// 修改活动管理
export function updateActivity(data) {
  return request({
    url: '/website/activities',
    method: 'put',
    data: data
  })
}

// 删除活动管理
export function delActivity(id) {
  return request({
    url: '/website/activities/' + id,
    method: 'delete'
  })
}

// 查询首页显示的活动列表
export function listHomepageActivities() {
  return request({
    url: '/website/activities/homepage',
    method: 'get'
  })
} 