<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="类型代码" prop="typeCode">
        <el-input
          v-model="queryParams.typeCode"
          placeholder="请输入会员类型代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="中文名称" prop="nameZh">
        <el-input
          v-model="queryParams.nameZh"
          placeholder="请输入中文名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['website:membership-types:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['website:membership-types:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['website:membership-types:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['website:membership-types:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="membershipTypeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="类型代码" align="center" prop="typeCode" />
      <el-table-column label="中文名称" align="center" prop="nameZh" />
      <el-table-column label="英文名称" align="center" prop="nameEn" />
      <el-table-column label="年费" align="center" prop="annualFee">
        <template slot-scope="scope">
          HK${{ scope.row.annualFee }}
        </template>
      </el-table-column>
      <el-table-column label="图标" align="center" prop="icon">
        <template slot-scope="scope">
          <i :class="scope.row.icon" style="font-size: 16px;"></i>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['website:membership-types:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['website:membership-types:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会员类型对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="类型代码" prop="typeCode">
              <el-input v-model="form.typeCode" placeholder="请输入会员类型代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="中文名称" prop="nameZh">
              <el-input v-model="form.nameZh" placeholder="请输入中文名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="英文名称" prop="nameEn">
              <el-input v-model="form.nameEn" placeholder="请输入英文名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="年费" prop="annualFee">
              <el-input-number v-model="form.annualFee" controls-position="right" :min="0" :precision="2" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="图标" prop="icon">
              <el-input v-model="form.icon" placeholder="请输入图标类名" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="中文描述" prop="descriptionZh">
          <el-input v-model="form.descriptionZh" type="textarea" placeholder="请输入中文描述" />
        </el-form-item>
        <el-form-item label="英文描述" prop="descriptionEn">
          <el-input v-model="form.descriptionEn" type="textarea" placeholder="请输入英文描述" />
        </el-form-item>
        <el-form-item label="中文权益">
          <el-tag
            v-for="(benefit, index) in form.benefitsZh"
            :key="index"
            closable
            @close="removeBenefitZh(index)"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ benefit }}
          </el-tag>
          <el-input
            v-if="inputVisibleZh"
            v-model="inputValueZh"
            ref="saveTagInputZh"
            size="small"
            @keyup.enter.native="handleInputConfirmZh"
            @blur="handleInputConfirmZh"
            style="width: 200px;"
          />
          <el-button v-else size="small" @click="showInputZh">+ 添加权益</el-button>
        </el-form-item>
        <el-form-item label="英文权益">
          <el-tag
            v-for="(benefit, index) in form.benefitsEn"
            :key="index"
            closable
            @close="removeBenefitEn(index)"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ benefit }}
          </el-tag>
          <el-input
            v-if="inputVisibleEn"
            v-model="inputValueEn"
            ref="saveTagInputEn"
            size="small"
            @keyup.enter.native="handleInputConfirmEn"
            @blur="handleInputConfirmEn"
            style="width: 200px;"
          />
          <el-button v-else size="small" @click="showInputEn">+ 添加权益</el-button>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMembershipTypes, getMembershipType, delMembershipType, addMembershipType, updateMembershipType } from "@/api/website/membership-types";

export default {
  name: "MembershipTypes",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会员类型表格数据
      membershipTypeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        typeCode: null,
        nameZh: null,
        nameEn: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        typeCode: [
          { required: true, message: "会员类型代码不能为空", trigger: "blur" }
        ],
        nameZh: [
          { required: true, message: "中文名称不能为空", trigger: "blur" }
        ],
        nameEn: [
          { required: true, message: "英文名称不能为空", trigger: "blur" }
        ],
        annualFee: [
          { required: true, message: "年费不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      },
      // 中文权益输入
      inputVisibleZh: false,
      inputValueZh: '',
      // 英文权益输入
      inputVisibleEn: false,
      inputValueEn: ''
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询会员类型列表 */
    getList() {
      this.loading = true;
      listMembershipTypes(this.queryParams).then(response => {
        this.membershipTypeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        typeCode: null,
        nameZh: null,
        nameEn: null,
        descriptionZh: null,
        descriptionEn: null,
        annualFee: 0,
        icon: null,
        benefitsZh: [],
        benefitsEn: [],
        sortOrder: 0,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加会员类型";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMembershipType(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改会员类型";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateMembershipType(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMembershipType(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除会员类型编号为"' + ids + '"的数据项？').then(function() {
        return delMembershipType(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('website/membership-types/export', {
        ...this.queryParams
      }, `membership_types_${new Date().getTime()}.xlsx`)
    },
    // 中文权益管理
    removeBenefitZh(index) {
      this.form.benefitsZh.splice(index, 1);
    },
    showInputZh() {
      this.inputVisibleZh = true;
      this.$nextTick(() => {
        this.$refs.saveTagInputZh.$refs.input.focus();
      });
    },
    handleInputConfirmZh() {
      let inputValue = this.inputValueZh;
      if (inputValue) {
        this.form.benefitsZh.push(inputValue);
      }
      this.inputVisibleZh = false;
      this.inputValueZh = '';
    },
    // 英文权益管理
    removeBenefitEn(index) {
      this.form.benefitsEn.splice(index, 1);
    },
    showInputEn() {
      this.inputVisibleEn = true;
      this.$nextTick(() => {
        this.$refs.saveTagInputEn.$refs.input.focus();
      });
    },
    handleInputConfirmEn() {
      let inputValue = this.inputValueEn;
      if (inputValue) {
        this.form.benefitsEn.push(inputValue);
      }
      this.inputVisibleEn = false;
      this.inputValueEn = '';
    }
  }
};
</script> 