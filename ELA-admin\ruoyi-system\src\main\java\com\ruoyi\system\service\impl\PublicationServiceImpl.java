package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.PublicationMapper;
import com.ruoyi.system.domain.Publication;
import com.ruoyi.system.service.IPublicationService;

/**
 * 资讯管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
@Service
public class PublicationServiceImpl implements IPublicationService 
{
    @Autowired
    private PublicationMapper publicationMapper;

    /**
     * 查询资讯管理
     * 
     * @param id 资讯管理主键
     * @return 资讯管理
     */
    @Override
    public Publication selectPublicationById(Long id)
    {
        return publicationMapper.selectPublicationById(id);
    }

    /**
     * 查询资讯管理列表
     * 
     * @param publication 资讯管理
     * @return 资讯管理
     */
    @Override
    public List<Publication> selectPublicationList(Publication publication)
    {
        return publicationMapper.selectPublicationList(publication);
    }

    /**
     * 新增资讯管理
     * 
     * @param publication 资讯管理
     * @return 结果
     */
    @Override
    public int insertPublication(Publication publication)
    {
        publication.setCreateTime(DateUtils.getNowDate());
        return publicationMapper.insertPublication(publication);
    }

    /**
     * 修改资讯管理
     * 
     * @param publication 资讯管理
     * @return 结果
     */
    @Override
    public int updatePublication(Publication publication)
    {
        publication.setUpdateTime(DateUtils.getNowDate());
        return publicationMapper.updatePublication(publication);
    }

    /**
     * 批量删除资讯管理
     * 
     * @param ids 需要删除的资讯管理主键
     * @return 结果
     */
    @Override
    public int deletePublicationByIds(Long[] ids)
    {
        return publicationMapper.deletePublicationByIds(ids);
    }

    /**
     * 删除资讯管理信息
     * 
     * @param id 资讯管理主键
     * @return 结果
     */
    @Override
    public int deletePublicationById(Long id)
    {
        return publicationMapper.deletePublicationById(id);
    }
}