-- 会员权益表
CREATE TABLE `member_benefits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权益ID',
  `title_zh` varchar(100) NOT NULL COMMENT '中文标题',
  `title_en` varchar(100) NOT NULL COMMENT '英文标题',
  `description_zh` text COMMENT '中文描述',
  `description_en` text COMMENT '英文描述',
  `icon` varchar(100) DEFAULT NULL COMMENT '图标类名',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员权益表';

-- 插入默认数据
INSERT INTO `member_benefits` (`title_zh`, `title_en`, `description_zh`, `description_en`, `icon`, `sort_order`, `status`, `create_by`, `create_time`) VALUES
('網絡建設', 'Networking', '與業界領袖和專家建立聯繫，擴展商業網絡', 'Connect with industry leaders and experts, expand business networks', 'fas fa-users', 1, '0', 'admin', NOW()),
('專業培訓', 'Professional Training', '參加專業培訓課程，提升技能和知識水平', 'Attend professional training courses to enhance skills and knowledge', 'fas fa-graduation-cap', 2, '0', 'admin', NOW()),
('行業資訊', 'Industry Intelligence', '獲取最新行業趨勢和市場資訊', 'Access to latest industry trends and market intelligence', 'fas fa-chart-line', 3, '0', 'admin', NOW()),
('商務合作', 'Business Collaboration', '尋找合作夥伴，開拓新的商業機會', 'Find business partners and explore new opportunities', 'fas fa-handshake', 4, '0', 'admin', NOW()),
('政策倡導', 'Policy Advocacy', '參與政策討論，為行業發展發聲', 'Participate in policy discussions and advocate for industry development', 'fas fa-microphone', 5, '0', 'admin', NOW()),
('行業認可', 'Industry Recognition', '獲得行業認可，提升企業和個人聲譽', 'Gain industry recognition and enhance corporate/personal reputation', 'fas fa-award', 6, '0', 'admin', NOW());

-- 会员权益菜单 SQL
-- 获取网站管理菜单ID
SELECT @websiteMenuId := menu_id FROM sys_menu WHERE menu_name = '网站管理' AND parent_id = '0' LIMIT 1;

-- 添加会员权益菜单（网站管理的子菜单）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('会员权益', @websiteMenuId, '7', 'member-benefits', 'website/member-benefits/index', 1, 0, 'C', '0', '0', 'website:member-benefits:list', 'fa fa-gift', 'admin', sysdate(), '', null, '会员权益菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员权益查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'website:member-benefits:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员权益新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'website:member-benefits:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员权益修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'website:member-benefits:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员权益删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'website:member-benefits:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员权益导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'website:member-benefits:export',       '#', 'admin', sysdate(), '', null, ''); 