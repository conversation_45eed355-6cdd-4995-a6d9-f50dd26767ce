-- 创建成员类型表
CREATE TABLE member_types (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    type_code VARCHAR(50) NOT NULL UNIQUE COMMENT '类型代码',
    name_zh VARCHAR(100) NOT NULL COMMENT '类型名称（中文）',
    name_en VARCHAR(100) NOT NULL COMMENT '类型名称（英文）',
    description_zh TEXT COMMENT '描述（中文）',
    description_en TEXT COMMENT '描述（英文）',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    INDEX idx_type_code (type_code),
    INDEX idx_sort_order (sort_order),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成员类型表';

-- 插入初始数据
INSERT INTO member_types (type_code, name_zh, name_en, description_zh, description_en, sort_order, status, create_by, remark) VALUES
('president', '会长', 'President', '协会会长', 'Association President', 1, '0', 'admin', '协会最高领导职位'),
('vice_president', '副会长', 'Vice President', '协会副会长', 'Association Vice President', 2, '0', 'admin', '协会副领导职位'),
('chair', '主席', 'Chairman', '委员会主席', 'Committee Chairman', 3, '0', 'admin', '委员会领导职位'),
('vice_chair', '副主席', 'Vice Chairman', '委员会副主席', 'Committee Vice Chairman', 4, '0', 'admin', '委员会副领导职位'),
('secretary', '秘书', 'Secretary', '秘书职位', 'Secretary Position', 5, '0', 'admin', '负责会务工作'),
('treasurer', '司库', 'Treasurer', '司库职位', 'Treasurer Position', 6, '0', 'admin', '负责财务工作'),
('director', '理事', 'Director', '理事会成员', 'Board Director', 7, '0', 'admin', '理事会普通成员'),
('member', '成员', 'Member', '普通成员', 'Regular Member', 8, '0', 'admin', '普通成员'),
('advisor', '顾问', 'Advisor', '顾问成员', 'Advisory Member', 9, '0', 'admin', '顾问职位'),
('honorary_advisor', '名誉顾问', 'Honorary Advisor', '名誉顾问', 'Honorary Advisor', 10, '0', 'admin', '名誉顾问职位'); 