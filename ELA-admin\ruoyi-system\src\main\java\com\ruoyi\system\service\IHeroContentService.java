package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.HeroContent;

/**
 * 首页Hero内容Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IHeroContentService 
{
    /**
     * 查询首页Hero内容
     * 
     * @param id 首页Hero内容主键
     * @return 首页Hero内容
     */
    public HeroContent selectHeroContentById(Long id);

    /**
     * 查询首页Hero内容列表
     * 
     * @param heroContent 首页Hero内容
     * @return 首页Hero内容集合
     */
    public List<HeroContent> selectHeroContentList(HeroContent heroContent);

    /**
     * 查询启用的首页Hero内容
     * 
     * @return 首页Hero内容
     */
    public HeroContent selectEnabledHeroContent();

    /**
     * 新增首页Hero内容
     * 
     * @param heroContent 首页Hero内容
     * @return 结果
     */
    public int insertHeroContent(HeroContent heroContent);

    /**
     * 修改首页Hero内容
     * 
     * @param heroContent 首页Hero内容
     * @return 结果
     */
    public int updateHeroContent(HeroContent heroContent);

    /**
     * 批量删除首页Hero内容
     * 
     * @param ids 需要删除的首页Hero内容主键集合
     * @return 结果
     */
    public int deleteHeroContentByIds(Long[] ids);

    /**
     * 删除首页Hero内容信息
     * 
     * @param id 首页Hero内容主键
     * @return 结果
     */
    public int deleteHeroContentById(Long id);
} 