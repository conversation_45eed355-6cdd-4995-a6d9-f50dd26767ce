import request from '@/utils/request'

// 查询网站Banner列表
export function listBanner(query) {
  return request({
    url: '/website/banner/list',
    method: 'get',
    params: query
  })
}

// 查询网站Banner详细
export function getBanner(id) {
  return request({
    url: '/website/banner/' + id,
    method: 'get'
  })
}

// 新增网站Banner
export function addBanner(data) {
  return request({
    url: '/website/banner',
    method: 'post',
    data: data
  })
}

// 修改网站Banner
export function updateBanner(data) {
  return request({
    url: '/website/banner',
    method: 'put',
    data: data
  })
}

// 删除网站Banner
export function delBanner(id) {
  return request({
    url: '/website/banner/' + id,
    method: 'delete'
  })
} 