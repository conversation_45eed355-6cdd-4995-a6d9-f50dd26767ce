package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ApplicationRequirementMapper;
import com.ruoyi.system.domain.ApplicationRequirement;
import com.ruoyi.system.service.IApplicationRequirementService;

/**
 * 申请条件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
@Service
public class ApplicationRequirementServiceImpl implements IApplicationRequirementService 
{
    @Autowired
    private ApplicationRequirementMapper applicationRequirementMapper;

    /**
     * 查询申请条件
     * 
     * @param id 申请条件主键
     * @return 申请条件
     */
    @Override
    public ApplicationRequirement selectApplicationRequirementById(Long id)
    {
        return applicationRequirementMapper.selectApplicationRequirementById(id);
    }

    /**
     * 查询申请条件列表
     * 
     * @param applicationRequirement 申请条件
     * @return 申请条件
     */
    @Override
    public List<ApplicationRequirement> selectApplicationRequirementList(ApplicationRequirement applicationRequirement)
    {
        return applicationRequirementMapper.selectApplicationRequirementList(applicationRequirement);
    }

    /**
     * 查询启用的申请条件列表
     * 
     * @return 申请条件
     */
    @Override
    public List<ApplicationRequirement> selectEnabledApplicationRequirementList()
    {
        return applicationRequirementMapper.selectEnabledApplicationRequirementList();
    }

    /**
     * 新增申请条件
     * 
     * @param applicationRequirement 申请条件
     * @return 结果
     */
    @Override
    public int insertApplicationRequirement(ApplicationRequirement applicationRequirement)
    {
        applicationRequirement.setCreateTime(DateUtils.getNowDate());
        return applicationRequirementMapper.insertApplicationRequirement(applicationRequirement);
    }

    /**
     * 修改申请条件
     * 
     * @param applicationRequirement 申请条件
     * @return 结果
     */
    @Override
    public int updateApplicationRequirement(ApplicationRequirement applicationRequirement)
    {
        applicationRequirement.setUpdateTime(DateUtils.getNowDate());
        return applicationRequirementMapper.updateApplicationRequirement(applicationRequirement);
    }

    /**
     * 批量删除申请条件
     * 
     * @param ids 需要删除的申请条件主键
     * @return 结果
     */
    @Override
    public int deleteApplicationRequirementByIds(Long[] ids)
    {
        return applicationRequirementMapper.deleteApplicationRequirementByIds(ids);
    }

    /**
     * 删除申请条件信息
     * 
     * @param id 申请条件主键
     * @return 结果
     */
    @Override
    public int deleteApplicationRequirementById(Long id)
    {
        return applicationRequirementMapper.deleteApplicationRequirementById(id);
    }
} 