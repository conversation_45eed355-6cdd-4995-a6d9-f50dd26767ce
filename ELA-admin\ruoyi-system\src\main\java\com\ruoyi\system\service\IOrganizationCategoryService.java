package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.OrganizationCategory;

/**
 * 组织架构分类Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IOrganizationCategoryService 
{
    /**
     * 查询组织架构分类
     * 
     * @param id 组织架构分类主键
     * @return 组织架构分类
     */
    public OrganizationCategory selectOrganizationCategoryById(Long id);

    /**
     * 查询组织架构分类列表
     * 
     * @param organizationCategory 组织架构分类
     * @return 组织架构分类集合
     */
    public List<OrganizationCategory> selectOrganizationCategoryList(OrganizationCategory organizationCategory);

    /**
     * 新增组织架构分类
     * 
     * @param organizationCategory 组织架构分类
     * @return 结果
     */
    public int insertOrganizationCategory(OrganizationCategory organizationCategory);

    /**
     * 修改组织架构分类
     * 
     * @param organizationCategory 组织架构分类
     * @return 结果
     */
    public int updateOrganizationCategory(OrganizationCategory organizationCategory);

    /**
     * 批量删除组织架构分类
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrganizationCategoryByIds(Long[] ids);

    /**
     * 删除组织架构分类信息
     * 
     * @param id 组织架构分类主键
     * @return 结果
     */
    public int deleteOrganizationCategoryById(Long id);
} 