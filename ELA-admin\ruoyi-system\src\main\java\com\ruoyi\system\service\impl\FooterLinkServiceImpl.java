package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.FooterLinkMapper;
import com.ruoyi.system.domain.FooterLink;
import com.ruoyi.system.service.IFooterLinkService;

/**
 * 页脚链接Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class FooterLinkServiceImpl implements IFooterLinkService 
{
    @Autowired
    private FooterLinkMapper footerLinkMapper;

    /**
     * 查询页脚链接
     * 
     * @param id 页脚链接主键
     * @return 页脚链接
     */
    @Override
    public FooterLink selectFooterLinkById(Long id)
    {
        return footerLinkMapper.selectFooterLinkById(id);
    }

    /**
     * 查询页脚链接列表
     * 
     * @param footerLink 页脚链接
     * @return 页脚链接
     */
    @Override
    public List<FooterLink> selectFooterLinkList(FooterLink footerLink)
    {
        return footerLinkMapper.selectFooterLinkList(footerLink);
    }

    /**
     * 新增页脚链接
     * 
     * @param footerLink 页脚链接
     * @return 结果
     */
    @Override
    public int insertFooterLink(FooterLink footerLink)
    {
        return footerLinkMapper.insertFooterLink(footerLink);
    }

    /**
     * 修改页脚链接
     * 
     * @param footerLink 页脚链接
     * @return 结果
     */
    @Override
    public int updateFooterLink(FooterLink footerLink)
    {
        return footerLinkMapper.updateFooterLink(footerLink);
    }

    /**
     * 批量删除页脚链接
     * 
     * @param ids 需要删除的页脚链接主键
     * @return 结果
     */
    @Override
    public int deleteFooterLinkByIds(Long[] ids)
    {
        return footerLinkMapper.deleteFooterLinkByIds(ids);
    }

    /**
     * 删除页脚链接信息
     * 
     * @param id 页脚链接主键
     * @return 结果
     */
    @Override
    public int deleteFooterLinkById(Long id)
    {
        return footerLinkMapper.deleteFooterLinkById(id);
    }
} 