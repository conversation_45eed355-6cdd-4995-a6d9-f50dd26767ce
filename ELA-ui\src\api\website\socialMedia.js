import request from '@/utils/request'

// 查询社交媒体列表
export function listSocialMedia(query) {
  return request({
    url: '/website/socialMedia/list',
    method: 'get',
    params: query
  })
}

// 查询社交媒体详细
export function getSocialMedia(id) {
  return request({
    url: '/website/socialMedia/' + id,
    method: 'get'
  })
}

// 新增社交媒体
export function addSocialMedia(data) {
  return request({
    url: '/website/socialMedia',
    method: 'post',
    data: data
  })
}

// 修改社交媒体
export function updateSocialMedia(data) {
  return request({
    url: '/website/socialMedia',
    method: 'put',
    data: data
  })
}

// 删除社交媒体
export function delSocialMedia(id) {
  return request({
    url: '/website/socialMedia/' + id,
    method: 'delete'
  })
} 