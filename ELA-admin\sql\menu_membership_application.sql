-- 菜单SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员申请表', '3', '1', 'membership-application', 'website/membership-application/index', 1, 0, 'C', '0', '0', 'website:membership-application:list', 'form', 'admin', sysdate(), '', null, '会员申请表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员申请表查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'website:membership-application:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员申请表新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'website:membership-application:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员申请表修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'website:membership-application:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员申请表删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'website:membership-application:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('会员申请表导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'website:membership-application:export',       '#', 'admin', sysdate(), '', null, ''); 