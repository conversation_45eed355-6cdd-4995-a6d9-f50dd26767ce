<template>
  <div class="contact">
    <!-- 大背景Banner -->
    <section class="hero-banner">
      <div class="hero-overlay"></div>
      <div class="hero-content">
        <h1 class="hero-title">{{ currentLang === 'zh' ? '聯繫我們' : 'Contact Us' }}</h1>
        <p class="hero-subtitle">
          {{ currentLang === 'zh'
            ? '我們期待與您的合作，共創香港電商物流美好未來'
            : 'We look forward to working with you to create a bright future for Hong Kong e-commerce logistics'
          }}
        </p>
      </div>
    </section>

    <!-- 联系信息 -->
    <section class="contact-info section">
      <div class="container">
        <div class="contact-grid">
          <div class="contact-form-section">
            <h2 class="contact-section-title">
              {{ currentLang === 'zh' ? '給我們發送電子郵件' : 'Send us an Email' }}
            </h2>
            <form class="contact-form" @submit.prevent="submitForm">
              <div class="form-group">
                <label for="name">{{ currentLang === 'zh' ? '您的姓名' : 'Your name' }}</label>
                <input
                  type="text"
                  id="name"
                  v-model="form.name"
                  required
                  :placeholder="currentLang === 'zh' ? '請輸入您的姓名' : 'Please enter your name'"
                >
              </div>

              <div class="form-group">
                <label for="email">{{ currentLang === 'zh' ? '您的郵箱' : 'Your email' }}</label>
                <input
                  type="email"
                  id="email"
                  v-model="form.email"
                  required
                  :placeholder="currentLang === 'zh' ? '請輸入您的郵箱' : 'Please enter your email'"
                >
              </div>

              <div class="form-group">
                <label for="subject">{{ currentLang === 'zh' ? '主題' : 'Subject' }}</label>
                <input
                  type="text"
                  id="subject"
                  v-model="form.subject"
                  required
                  :placeholder="currentLang === 'zh' ? '請輸入主題' : 'Please enter subject'"
                >
              </div>

              <div class="form-group">
                <label for="message">{{ currentLang === 'zh' ? '您的留言（可選）' : 'Your message (optional)' }}</label>
                <textarea
                  id="message"
                  v-model="form.message"
                  rows="6"
                  :placeholder="currentLang === 'zh' ? '請輸入您的留言' : 'Please enter your message'"
                ></textarea>
              </div>

              <button type="submit" class="btn btn-primary" :disabled="submitting">
                {{ submitting
                  ? (currentLang === 'zh' ? '提交中...' : 'Submitting...')
                  : (currentLang === 'zh' ? '提交' : 'Submit')
                }}
              </button>
            </form>
          </div>

          <div class="contact-details-section">
            <h2 class="contact-section-title">
              {{ currentLang === 'zh' ? '請與我們聯繫' : 'Get in touch with us!' }}
            </h2>
            <div class="contact-details">
              <!-- 公司信息 -->
              <!-- <div v-if="contactInfo.companyName" class="contact-item">
                <div class="contact-icon">
                  <i v-if="contactInfo.icon" :class="contactInfo.icon"></i>
                  <span v-else>🏢</span>
                </div>
                <div class="contact-content">
                  <h3 class="contact-title">{{ contactInfo.companyName }}</h3>
                  <p v-if="contactInfo.description" class="contact-text">{{ contactInfo.description }}</p>
                </div>
              </div> -->

              <!-- 地址 -->
              <!-- <div v-if="contactInfo.address" class="contact-item">
                <div class="contact-icon">📍</div>
                <div class="contact-content">
                  <h3 class="contact-title">{{ currentLang === 'zh' ? '地址 Address:' : 'Address:' }}</h3>
                  <p class="contact-text">{{ contactInfo.address }}</p>
                </div>
              </div> -->

              <!-- 电话 -->
              <div v-if="contactInfo.phone" class="contact-item">
                <div class="contact-icon">📞</div>
                <div class="contact-content">
                  <h3 class="contact-title">{{ currentLang === 'zh' ? '電話 Phone:' : 'Phone:' }}</h3>
                  <p class="contact-text">
                    <a :href="'tel:' + contactInfo.phone" class="phone-link">{{ contactInfo.phone }}</a>
                  </p>
                </div>
              </div>

              <!-- 邮箱 -->
              <div v-if="contactInfo.email" class="contact-item">
                <div class="contact-icon">✉️</div>
                <div class="contact-content">
                  <h3 class="contact-title">{{ currentLang === 'zh' ? '電郵 Email:' : 'Email:' }}</h3>
                  <p class="contact-text">
                    <a :href="'mailto:' + contactInfo.email" class="email-link">{{ contactInfo.email }}</a>
                  </p>
                </div>
              </div>

              <!-- 网站 -->
              <div v-if="contactInfo.website" class="contact-item">
                <div class="contact-icon">🌐</div>
                <div class="contact-content">
                  <h3 class="contact-title">{{ currentLang === 'zh' ? '網站 Website:' : 'Website:' }}</h3>
                  <p class="contact-text">
                    <a :href="contactInfo.website" target="_blank" class="website-link">{{ contactInfo.website }}</a>
                  </p>
                </div>
              </div>

              <!-- 营业时间 -->
              <div v-if="contactInfo.businessHours" class="contact-item">
                <div class="contact-icon">🕒</div>
                <div class="contact-content">
                  <h3 class="contact-title">{{ currentLang === 'zh' ? '營業時間 Business Hours:' : 'Business Hours:' }}</h3>
                  <p class="contact-text">{{ contactInfo.businessHours }}</p>
                </div>
              </div>

              <!-- 社交媒体 -->
              <div v-if="socialMedia.length > 0" class="contact-item">
                <div class="contact-icon">📱</div>
                <div class="contact-content">
                  <h3 class="contact-title">{{ currentLang === 'zh' ? '社交媒體 Social Media' : 'Social Media' }}</h3>
                  <div class="social-icons">
                    <a
                      v-for="social in socialMedia"
                      :key="social.id"
                      :href="social.url"
                      :class="'social-icon-link ' + social.platform"
                      target="_blank"
                      :title="social.platformName"
                    >
                      <div class="social-icon">
                        <img
                          v-if="social.icon"
                          :src="social.icon"
                          :alt="social.platformName"
                          class="social-icon-img"
                        />
                        <span v-else class="social-icon-text">{{ social.platformName ? social.platformName.charAt(0).toUpperCase() : '?' }}</span>
                      </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 成功提示 -->
    <div v-if="showSuccess" class="success-modal" @click="showSuccess = false">
      <div class="success-content" @click.stop>
        <div class="success-icon">✅</div>
        <h3>{{ currentLang === 'zh' ? '消息發送成功！' : 'Message sent successfully!' }}</h3>
        <p>{{ currentLang === 'zh'
          ? '我們已收到您的消息，將在24小時內回復您。'
          : 'We have received your message and will reply within 24 hours.'
        }}</p>
        <button class="btn btn-primary" @click="showSuccess = false">
          {{ currentLang === 'zh' ? '確定' : 'OK' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { getContactInfoList } from '@/api/website/contactInfo'
import { getSocialMediaList } from '@/api/website/socialMedia'
import { submitContactMessage } from '@/api/website/contactMessage'

export default {
  name: 'Contact',
  props: {
    currentLang: {
      type: String,
      default: 'zh'
    }
  },
  data () {
    return {
      form: {
        name: '',
        email: '',
        subject: '',
        message: ''
      },
      submitting: false,
      showSuccess: false,
      contactInfo: {},
      socialMedia: []
    }
  },
  async created() {
    await Promise.all([
      this.loadContactInfo(),
      this.loadSocialMedia()
    ])
  },
  methods: {
    async loadContactInfo() {
      try {
        const response = await getContactInfoList()
        if (response.data && response.data.length > 0) {
          // 获取第一个有效的联系信息
          this.contactInfo = response.data.find(item => item.status === '0') || response.data[0]
        }
      } catch (error) {
        console.error('Failed to load contact info:', error)
        // 设置默认值
        this.contactInfo = {
          companyName: '香港電商物流協會 (HKELA)',
          email: '<EMAIL>',
          phone: '+852 1234 5678',
          address: '香港中環金融街8號國際金融中心二期',
          website: 'https://www.hkela.org',
          businessHours: '周一至周五 9:00-18:00',
          description: '香港電商物流協會致力於推動香港電商物流行業的發展，為會員提供專業的服務和支持。'
        }
      }
    },
    async loadSocialMedia() {
      try {
        const response = await getSocialMediaList()
        if (response.data && response.data.length > 0) {
          // 获取有效的社交媒体信息
          this.socialMedia = response.data.filter(item => item.status === '0')
        }
      } catch (error) {
        console.error('Failed to load social media:', error)
        // 设置默认值
        this.socialMedia = [
          {
            platform: 'facebook',
            platformName: 'Facebook',
            url: 'https://www.facebook.com/hkela',
            icon: 'https://www.facebook.com/favicon.ico'
          },
          {
            platform: 'instagram',
            platformName: 'Instagram',
            url: 'https://www.instagram.com/hkela',
            icon: 'https://www.instagram.com/favicon.ico'
          },
          {
            platform: 'linkedin',
            platformName: 'LinkedIn',
            url: 'https://www.linkedin.com/company/hkela',
            icon: 'https://www.linkedin.com/favicon.ico'
          }
        ]
      }
    },
    async submitForm () {
      this.submitting = true

      try {
        // 提交到后台
        await submitContactMessage(this.form)

        this.submitting = false
        this.showSuccess = true

        // 重置表单
        this.form = {
          name: '',
          email: '',
          subject: '',
          message: ''
        }
      } catch (error) {
        this.submitting = false
        console.error('Failed to submit form:', error)
        // 显示错误信息
        alert(this.currentLang === 'zh' ? '提交失败，请稍后重试' : 'Submission failed, please try again later')
      }
    }
  }
}
</script>

<style scoped>
/* Hero Banner */
.hero-banner {
  height: 500px;
  background-image: url('~@/assets/banner/contact.png');
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.2rem;
  font-weight: 300;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.6;
  opacity: 0.9;
}

/* 通用容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section {
  padding: 80px 0;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.section-title p {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 12px 32px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-primary {
  background: #1e293b;
  color: white;
  border: 2px solid #1e293b;
}

.btn-primary:hover {
  background: #334155;
  border-color: #334155;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(30, 41, 59, 0.3);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 联系信息 */
.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.contact-section-title {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 32px;
  color: #333;
}

/* 联系表单 */
.contact-form {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #1e293b;
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 4px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #fff;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #1e293b;
  box-shadow: 0 0 0 3px rgba(30, 41, 59, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
}

/* 联系详情 */
.contact-details {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 32px;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-icon {
  font-size: 24px;
  min-width: 40px;
  text-align: center;
}

.contact-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
  line-height: 1.4;
}

.contact-text {
  color: #64748b;
  margin-bottom: 4px;
}

.email-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
}

.email-link:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.phone-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
}

.phone-link:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.website-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
}

.website-link:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.social-icons {
  display: flex;
  gap: 12px;
  margin-top: 12px;
}

.social-icon-link {
  display: inline-block;
  transition: transform 0.3s ease;
}

.social-icon-link:hover {
  transform: translateY(-2px);
}

.social-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  transition: all 0.3s ease;
  overflow: hidden;
}

.social-icon:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.social-icon-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.social-icon-text {
  font-size: 24px;
  font-weight: bold;
  color: #666;
}

/* 成功提示模态框 */
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.success-content {
  background: white;
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  max-width: 400px;
  margin: 20px;
}

.success-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.success-content h3 {
  font-size: 24px;
  color: #333;
  margin-bottom: 16px;
}

.success-content p {
  color: #666;
  margin-bottom: 24px;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-banner {
    height: 400px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .contact-form,
  .contact-details,
  .office-hours {
    padding: 24px;
  }

  .social-grid {
    grid-template-columns: 1fr;
  }

  .section-title h2 {
    font-size: 2rem;
  }

  .section-title p {
    font-size: 1rem;
  }

  .container {
    padding: 0 15px;
  }
}

@media (max-width: 480px) {
  .hero-banner {
    height: 300px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 0.9rem;
  }

  .hero-content {
    padding: 0 1rem;
  }

  .section-title h2 {
    font-size: 1.75rem;
  }

  .section-title p {
    font-size: 0.9rem;
  }

  .container {
    padding: 0 10px;
  }

  .success-content {
    padding: 24px;
    margin: 10px;
  }
}
</style>
