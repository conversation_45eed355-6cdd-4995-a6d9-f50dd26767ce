package com.ruoyi.web.controller.website;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.ContactInfo;
import com.ruoyi.system.service.IContactInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 联系信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/website/contactInfo")
public class ContactInfoController extends BaseController
{
    @Autowired
    private IContactInfoService contactInfoService;

    /**
     * 查询联系信息列表
     */
    @PreAuthorize("@ss.hasPermi('website:contactInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(ContactInfo contactInfo)
    {
        startPage();
        List<ContactInfo> list = contactInfoService.selectContactInfoList(contactInfo);
        return getDataTable(list);
    }

    /**
     * 获取联系信息列表（公开接口，无需权限）
     */
    @GetMapping("/public/list")
    public AjaxResult getPublicList()
    {
        List<ContactInfo> list = contactInfoService.selectContactInfoList(new ContactInfo());
        return success(list);
    }

    /**
     * 导出联系信息列表
     */
    @PreAuthorize("@ss.hasPermi('website:contactInfo:export')")
    @Log(title = "联系信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ContactInfo contactInfo)
    {
        List<ContactInfo> list = contactInfoService.selectContactInfoList(contactInfo);
        ExcelUtil<ContactInfo> util = new ExcelUtil<ContactInfo>(ContactInfo.class);
        util.exportExcel(response, list, "联系信息数据");
    }

    /**
     * 获取联系信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('website:contactInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(contactInfoService.selectContactInfoById(id));
    }

    /**
     * 新增联系信息
     */
    @PreAuthorize("@ss.hasPermi('website:contactInfo:add')")
    @Log(title = "联系信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContactInfo contactInfo)
    {
        return toAjax(contactInfoService.insertContactInfo(contactInfo));
    }

    /**
     * 修改联系信息
     */
    @PreAuthorize("@ss.hasPermi('website:contactInfo:edit')")
    @Log(title = "联系信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContactInfo contactInfo)
    {
        return toAjax(contactInfoService.updateContactInfo(contactInfo));
    }

    /**
     * 删除联系信息
     */
    @PreAuthorize("@ss.hasPermi('website:contactInfo:remove')")
    @Log(title = "联系信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(contactInfoService.deleteContactInfoByIds(ids));
    }
} 