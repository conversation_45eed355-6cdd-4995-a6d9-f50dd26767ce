<template>
  <div class="youth-committee-page">
    <div class="page-header">
      <div class="container">
        <h1>{{ currentLang === 'zh' ? (categoryData.heroTitleZh || '青年委員會') : (categoryData.heroTitleEn || 'Youth Committee') }}</h1>
        <p class="page-description">
          {{ currentLang === 'zh' 
            ? (categoryData.heroSubtitleZh || '青年委員會致力於培養下一代領導者')
            : (categoryData.heroSubtitleEn || 'The Youth Committee is dedicated to nurturing the next generation of leaders') 
          }}
        </p>
      </div>
    </div>
    
    <div class="container" v-if="!loading">
      <!-- 动态渲染各个成员类型 -->
      <div 
        v-for="(members, typeCode) in groupedMembers" 
        :key="typeCode"
        class="content-section"
      >
        <h2>
          {{ currentLang === 'zh' 
            ? ((members[0] && members[0].memberTypeInfo && members[0].memberTypeInfo.nameZh) || typeCode)
            : ((members[0] && members[0].memberTypeInfo && members[0].memberTypeInfo.nameEn) || typeCode)
          }}
        </h2>
        <div class="members-grid">
          <div 
            v-for="member in members" 
            :key="member.id"
            class="member-card"
          >
            <!-- 社交媒体图标 - 移动到右上角 -->
            <div v-if="member.linkedinUrl || member.facebookUrl" class="member-social">
              <a v-if="member.linkedinUrl" :href="member.linkedinUrl" target="_blank" rel="noopener noreferrer" class="social-link">
                <img src="@/assets/linkedin.png" alt="LinkedIn" class="social-icon" />
              </a>
              <a v-if="member.facebookUrl" :href="member.facebookUrl" target="_blank" rel="noopener noreferrer" class="social-link">
                <img src="@/assets/facebook.png" alt="Facebook" class="social-icon" />
              </a>
            </div>
            
            <div class="member-photo">
              <img :src="member.avatarUrl || '/static/images/placeholder-person.jpg'" :alt="member.nameZh">
            </div>
            <h3>{{ currentLang === 'zh' ? member.nameZh : member.nameEn }}</h3>
            <p class="member-title">{{ currentLang === 'zh' ? member.positionZh : member.positionEn }}</p>
            <p v-if="member.departmentZh" class="member-department">{{ currentLang === 'zh' ? member.departmentZh : member.departmentEn }}</p>
            <p v-if="member.companyZh" class="member-company">{{ currentLang === 'zh' ? member.companyZh : member.companyEn }}</p>
            <p v-if="member.bioZh" class="member-description">{{ currentLang === 'zh' ? member.bioZh : member.bioEn }}</p>
          </div>
        </div>
      </div>
      
      <!-- 如果没有数据显示提示 -->
      <div v-if="Object.keys(groupedMembers).length === 0" class="no-data">
        <div class="content-section">
          <h2>{{ currentLang === 'zh' ? '暂无数据' : 'No Data Available' }}</h2>
          <p>{{ currentLang === 'zh' ? '暂无成员数据' : 'No member data available' }}</p>
        </div>
      </div>
      
      <!-- 活动与项目部分 -->
      <div class="content-section">
        <h2>{{ currentLang === 'zh' ? '活動與項目' : 'Activities & Projects' }}</h2>
        <div class="activities-list">
          <div class="activity-item" v-for="activity in activities" :key="activity.id">
            <i :class="activity.icon"></i>
            <h4>{{ currentLang === 'zh' ? activity.titleZh : activity.titleEn }}</h4>
            <p>{{ currentLang === 'zh' ? activity.descriptionZh : activity.descriptionEn }}</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Loading State -->
    <div v-else class="loading-container">
      <div class="loading-spinner"></div>
      <p>{{ currentLang === 'zh' ? '加載中...' : 'Loading...' }}</p>
    </div>
  </div>
</template>

<script>
import { getOrganizationDataByName } from '@/api/organization'

export default {
  name: 'YouthCommittee',
  props: {
    currentLang: {
      type: String,
      default: 'zh'
    }
  },
  data() {
    return {
      loading: false,
      categoryData: {},
      organizationMembers: [],
      groupedMembers: {},
      activities: [
        {
          id: 1,
          icon: 'fas fa-graduation-cap',
          titleZh: '領導力培訓',
          titleEn: 'Leadership Training',
          descriptionZh: '定期舉辦領導力發展工作坊和研討會',
          descriptionEn: 'Regular leadership development workshops and seminars'
        },
        {
          id: 2,
          icon: 'fas fa-network-wired',
          titleZh: '網絡活動',
          titleEn: 'Networking Events',
          descriptionZh: '促進青年專業人士之間的交流與合作',
          descriptionEn: 'Facilitating exchange and collaboration among young professionals'
        },
        {
          id: 3,
          icon: 'fas fa-hands-helping',
          titleZh: '社區服務',
          titleEn: 'Community Service',
          descriptionZh: '組織志願服務活動回饋社會',
          descriptionEn: 'Organizing volunteer activities to give back to society'
        }
      ]
    }
  },
  mounted() {
    this.loadOrganizationData()
  },
  methods: {
    async loadOrganizationData() {
      this.loading = true
      try {
        const categoryName = '青年委員會'
        const response = await getOrganizationDataByName(categoryName)
        this.categoryData = response.data.category
        this.organizationMembers = response.data.members
        this.groupedMembers = response.data.groupedMembers
        console.log('YouthCommittee - Loaded organization data:', this.groupedMembers)
      } catch (error) {
        console.error('加载青年委员会数据失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.youth-committee-page {
  padding-top: 80px;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #0056b3 0%, #004494 100%);
  color: white;
  padding: 60px 0;
  text-align: center;
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.page-description {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 700px;
  margin: 0 auto;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.content-section {
  padding: 60px 0;
}

.content-section h2 {
  font-size: 2rem;
  margin-bottom: 3rem;
  color: var(--text-primary);
  text-align: center;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
}

.member-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative; /* Added for positioning social icons */
}

.member-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.member-photo {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 1.5rem;
  background: #e9ecef;
}

.member-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-card h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.member-title {
  color: var(--primary-color);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.member-department {
  color: var(--text-secondary);
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
}

.member-company {
  color: #999;
  font-size: 0.85rem;
  font-style: italic;
  margin-bottom: 0.5rem;
}

.member-description {
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.6;
}

.member-social {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10; /* Ensure it's above other content */
  display: flex;
  gap: 8px;
}

.social-link {
  display: inline-block;
  width: 48px;
  height: 48px;
  transition: opacity 0.3s ease;
}

.social-link:hover {
  opacity: 0.8;
}

.social-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.no-data {
  text-align: center;
  padding: 3rem;
  color: #64748b;
}

.activities-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.activity-item {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  transition: transform 0.3s ease;
}

.activity-item:hover {
  transform: translateY(-5px);
}

.activity-item i {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.activity-item h4 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.activity-item p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0056b3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  color: #666;
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .members-grid {
    grid-template-columns: 1fr;
  }
  
  .activities-list {
    grid-template-columns: 1fr;
  }
}
</style>