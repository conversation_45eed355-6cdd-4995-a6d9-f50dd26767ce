package com.ruoyi.web.controller.website;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.MembershipApplication;
import com.ruoyi.system.service.IMembershipApplicationService;
import java.util.Date;

/**
 * 前台会员申请表Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/public/membership-application")
public class PublicMembershipApplicationController extends BaseController
{
    @Autowired
    private IMembershipApplicationService membershipApplicationService;

    /**
     * 提交会员申请表
     */
    @PostMapping("/submit")
    public AjaxResult submit(@RequestBody MembershipApplication membershipApplication)
    {
        try {
            // 设置初始状态为待处理
            membershipApplication.setStatus("0");
            // 确保创建时间为当前时间
            membershipApplication.setCreateTime(new Date());
            int result = membershipApplicationService.insertMembershipApplication(membershipApplication);
            if (result > 0) {
                return AjaxResult.success("申请提交成功，我们将在5个工作日内与您联系");
            } else {
                return AjaxResult.error("申请提交失败，请稍后重试");
            }
        } catch (Exception e) {
            logger.error("提交会员申请失败", e);
            return AjaxResult.error("申请提交失败，请稍后重试");
        }
    }
} 