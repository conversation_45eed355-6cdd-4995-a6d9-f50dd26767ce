package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.MembershipApplicationDetailedMapper;
import com.ruoyi.system.domain.MembershipApplicationDetailed;
import com.ruoyi.system.service.IMembershipApplicationDetailedService;

/**
 * 详细会员申请表Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MembershipApplicationDetailedServiceImpl implements IMembershipApplicationDetailedService
{
    @Autowired
    private MembershipApplicationDetailedMapper membershipApplicationDetailedMapper;

    /**
     * 查询详细会员申请表
     *
     * @param id 详细会员申请表主键
     * @return 详细会员申请表
     */
    @Override
    public MembershipApplicationDetailed selectMembershipApplicationDetailedById(Long id)
    {
        return membershipApplicationDetailedMapper.selectMembershipApplicationDetailedById(id);
    }

    /**
     * 查询详细会员申请表列表
     *
     * @param membershipApplicationDetailed 详细会员申请表
     * @return 详细会员申请表
     */
    @Override
    public List<MembershipApplicationDetailed> selectMembershipApplicationDetailedList(MembershipApplicationDetailed membershipApplicationDetailed)
    {
        return membershipApplicationDetailedMapper.selectMembershipApplicationDetailedList(membershipApplicationDetailed);
    }

    /**
     * 新增详细会员申请表
     *
     * @param membershipApplicationDetailed 详细会员申请表
     * @return 结果
     */
    @Override
    public int insertMembershipApplicationDetailed(MembershipApplicationDetailed membershipApplicationDetailed)
    {
        membershipApplicationDetailed.setCreateTime(DateUtils.getNowDate());
        // 设置默认值
        if (membershipApplicationDetailed.getCurrentStep() == null) {
            membershipApplicationDetailed.setCurrentStep(1); // 默认第一步
        }
        if (membershipApplicationDetailed.getIsCompleted() == null) {
            membershipApplicationDetailed.setIsCompleted(0); // 默认未完成
        }
        if (membershipApplicationDetailed.getStatus() == null) {
            membershipApplicationDetailed.setStatus("0"); // 默认待处理
        }
        return membershipApplicationDetailedMapper.insertMembershipApplicationDetailed(membershipApplicationDetailed);
    }

    /**
     * 修改详细会员申请表
     *
     * @param membershipApplicationDetailed 详细会员申请表
     * @return 结果
     */
    @Override
    public int updateMembershipApplicationDetailed(MembershipApplicationDetailed membershipApplicationDetailed)
    {
        membershipApplicationDetailed.setUpdateTime(DateUtils.getNowDate());
        return membershipApplicationDetailedMapper.updateMembershipApplicationDetailed(membershipApplicationDetailed);
    }

    /**
     * 批量删除详细会员申请表
     *
     * @param ids 需要删除的详细会员申请表主键
     * @return 结果
     */
    @Override
    public int deleteMembershipApplicationDetailedByIds(Long[] ids)
    {
        return membershipApplicationDetailedMapper.deleteMembershipApplicationDetailedByIds(ids);
    }

    /**
     * 删除详细会员申请表信息
     *
     * @param id 详细会员申请表主键
     * @return 结果
     */
    @Override
    public int deleteMembershipApplicationDetailedById(Long id)
    {
        return membershipApplicationDetailedMapper.deleteMembershipApplicationDetailedById(id);
    }

    /**
     * 根据当前步骤查询申请列表
     *
     * @param currentStep 当前步骤
     * @return 申请列表
     */
    @Override
    public List<MembershipApplicationDetailed> selectByCurrentStep(Integer currentStep)
    {
        return membershipApplicationDetailedMapper.selectByCurrentStep(currentStep);
    }

    /**
     * 根据处理状态查询申请列表
     *
     * @param status 处理状态
     * @return 申请列表
     */
    @Override
    public List<MembershipApplicationDetailed> selectByStatus(Integer status)
    {
        return membershipApplicationDetailedMapper.selectByStatus(status);
    }

    /**
     * 更新申请步骤
     *
     * @param id 申请ID
     * @param currentStep 当前步骤
     * @return 结果
     */
    @Override
    public int updateCurrentStep(Long id, Integer currentStep)
    {
        return membershipApplicationDetailedMapper.updateCurrentStep(id, currentStep);
    }

    /**
     * 更新申请状态
     *
     * @param id 申请ID
     * @param status 状态
     * @param processNotes 处理备注
     * @param processedBy 处理人
     * @return 结果
     */
    @Override
    public int updateStatus(Long id, Integer status, String processNotes, String processedBy)
    {
        return membershipApplicationDetailedMapper.updateStatus(id, status, processNotes, processedBy);
    }

    /**
     * 提交申请到下一步
     *
     * @param id 申请ID
     * @return 结果
     */
    @Override
    public int submitToNextStep(Long id)
    {
        MembershipApplicationDetailed application = selectMembershipApplicationDetailedById(id);
        if (application != null) {
            Integer currentStep = application.getCurrentStep();
            if (currentStep == null) {
                currentStep = 1;
            }
            // 假设总共有4个步骤
            if (currentStep < 4) {
                return updateCurrentStep(id, currentStep + 1);
            } else {
                // 最后一步，标记为完成
                return completeApplication(id);
            }
        }
        return 0;
    }

    /**
     * 完成申请
     *
     * @param id 申请ID
     * @return 结果
     */
    @Override
    public int completeApplication(Long id)
    {
        MembershipApplicationDetailed application = new MembershipApplicationDetailed();
        application.setId(id);
        application.setCurrentStep(4); // 设置为最后一步
        application.setIsCompleted(1); // 标记为已完成
        application.setUpdateTime(DateUtils.getNowDate());
        return membershipApplicationDetailedMapper.updateMembershipApplicationDetailed(application);
    }

    /**
     * 审核申请
     *
     * @param id 申请ID
     * @param approved 是否通过
     * @param processNotes 处理备注
     * @param processedBy 处理人
     * @return 结果
     */
    @Override
    public int reviewApplication(Long id, boolean approved, String processNotes, String processedBy)
    {
        Integer status = approved ? 1 : 2; // 1-已处理(通过), 2-已拒绝
        return updateStatus(id, status, processNotes, processedBy);
    }
}
