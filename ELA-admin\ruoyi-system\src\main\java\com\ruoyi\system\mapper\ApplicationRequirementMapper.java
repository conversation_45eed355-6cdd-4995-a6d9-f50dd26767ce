package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ApplicationRequirement;

/**
 * 申请条件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
public interface ApplicationRequirementMapper 
{
    /**
     * 查询申请条件
     * 
     * @param id 申请条件主键
     * @return 申请条件
     */
    public ApplicationRequirement selectApplicationRequirementById(Long id);

    /**
     * 查询申请条件列表
     * 
     * @param applicationRequirement 申请条件
     * @return 申请条件集合
     */
    public List<ApplicationRequirement> selectApplicationRequirementList(ApplicationRequirement applicationRequirement);

    /**
     * 查询启用的申请条件列表
     * 
     * @return 申请条件集合
     */
    public List<ApplicationRequirement> selectEnabledApplicationRequirementList();

    /**
     * 新增申请条件
     * 
     * @param applicationRequirement 申请条件
     * @return 结果
     */
    public int insertApplicationRequirement(ApplicationRequirement applicationRequirement);

    /**
     * 修改申请条件
     * 
     * @param applicationRequirement 申请条件
     * @return 结果
     */
    public int updateApplicationRequirement(ApplicationRequirement applicationRequirement);

    /**
     * 删除申请条件
     * 
     * @param id 申请条件主键
     * @return 结果
     */
    public int deleteApplicationRequirementById(Long id);

    /**
     * 批量删除申请条件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteApplicationRequirementByIds(Long[] ids);
} 