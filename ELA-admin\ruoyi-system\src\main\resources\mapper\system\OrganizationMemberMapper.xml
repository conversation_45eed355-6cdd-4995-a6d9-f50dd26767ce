<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.OrganizationMemberMapper">
    
    <resultMap type="OrganizationMember" id="OrganizationMemberResult">
        <result property="id"    column="id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="nameZh"    column="name_zh"    />
        <result property="nameEn"    column="name_en"    />
        <result property="positionZh"    column="position_zh"    />
        <result property="positionEn"    column="position_en"    />
        <result property="departmentZh"    column="department_zh"    />
        <result property="departmentEn"    column="department_en"    />
        <result property="companyZh"    column="company_zh"    />
        <result property="companyEn"    column="company_en"    />
        <result property="bioZh"    column="bio_zh"    />
        <result property="bioEn"    column="bio_en"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="email"    column="email"    />
        <result property="phone"    column="phone"    />
        <result property="linkedinUrl"    column="linkedin_url"    />
        <result property="facebookUrl"    column="facebook_url"    />
        <result property="linkedinAccount"    column="linkedin_account"    />
        <result property="facebookAccount"    column="facebook_account"    />
        <result property="memberType"    column="member_type"    />
        <result property="isLeadership"    column="is_leadership"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <association property="memberTypeInfo" javaType="MemberType">
            <result property="id"    column="mt_id"    />
            <result property="typeCode"    column="mt_type_code"    />
            <result property="nameZh"    column="mt_name_zh"    />
            <result property="nameEn"    column="mt_name_en"    />
            <result property="descriptionZh"    column="mt_description_zh"    />
            <result property="descriptionEn"    column="mt_description_en"    />
            <result property="sortOrder"    column="mt_sort_order"    />
            <result property="status"    column="mt_status"    />
        </association>
    </resultMap>

    <sql id="selectOrganizationMemberVo">
        select om.id, om.category_id, om.name_zh, om.name_en, om.position_zh, om.position_en, om.department_zh, om.department_en, om.company_zh, om.company_en, om.bio_zh, om.bio_en, om.avatar_url, om.email, om.phone, om.linkedin_url, om.facebook_url, om.linkedin_account, om.facebook_account, om.member_type, om.is_leadership, om.sort_order, om.status, om.create_by, om.create_time, om.update_by, om.update_time, om.remark,
        mt.id as mt_id, mt.type_code as mt_type_code, mt.name_zh as mt_name_zh, mt.name_en as mt_name_en, mt.description_zh as mt_description_zh, mt.description_en as mt_description_en, mt.sort_order as mt_sort_order, mt.status as mt_status
        from organization_members om
        left join member_types mt on om.member_type = mt.id
    </sql>

    <select id="selectOrganizationMemberList" parameterType="OrganizationMember" resultMap="OrganizationMemberResult">
        <include refid="selectOrganizationMemberVo"/>
        <where>  
            <if test="categoryId != null "> and om.category_id = #{categoryId}</if>
            <if test="nameZh != null  and nameZh != ''"> and om.name_zh like concat('%', #{nameZh}, '%')</if>
            <if test="nameEn != null  and nameEn != ''"> and om.name_en like concat('%', #{nameEn}, '%')</if>
            <if test="memberType != null  and memberType != ''"> and om.member_type = #{memberType}</if>
            <if test="isLeadership != null "> and om.is_leadership = #{isLeadership}</if>
            <if test="status != null  and status != ''"> and om.status = #{status}</if>
        </where>
        order by om.sort_order asc, om.create_time desc
    </select>
    
    <select id="selectOrganizationMemberById" parameterType="Long" resultMap="OrganizationMemberResult">
        <include refid="selectOrganizationMemberVo"/>
        where om.id = #{id}
    </select>
        
    <insert id="insertOrganizationMember" parameterType="OrganizationMember" useGeneratedKeys="true" keyProperty="id">
        insert into organization_members
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="nameZh != null and nameZh != ''">name_zh,</if>
            <if test="nameEn != null and nameEn != ''">name_en,</if>
            <if test="positionZh != null and positionZh != ''">position_zh,</if>
            <if test="positionEn != null and positionEn != ''">position_en,</if>
            <if test="departmentZh != null">department_zh,</if>
            <if test="departmentEn != null">department_en,</if>
            <if test="companyZh != null">company_zh,</if>
            <if test="companyEn != null">company_en,</if>
            <if test="bioZh != null">bio_zh,</if>
            <if test="bioEn != null">bio_en,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="email != null">email,</if>
            <if test="phone != null">phone,</if>
            <if test="linkedinUrl != null">linkedin_url,</if>
            <if test="facebookUrl != null">facebook_url,</if>
            <if test="linkedinAccount != null">linkedin_account,</if>
            <if test="facebookAccount != null">facebook_account,</if>
            <if test="memberType != null">member_type,</if>
            <if test="isLeadership != null">is_leadership,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="nameZh != null and nameZh != ''">#{nameZh},</if>
            <if test="nameEn != null and nameEn != ''">#{nameEn},</if>
            <if test="positionZh != null and positionZh != ''">#{positionZh},</if>
            <if test="positionEn != null and positionEn != ''">#{positionEn},</if>
            <if test="departmentZh != null">#{departmentZh},</if>
            <if test="departmentEn != null">#{departmentEn},</if>
            <if test="companyZh != null">#{companyZh},</if>
            <if test="companyEn != null">#{companyEn},</if>
            <if test="bioZh != null">#{bioZh},</if>
            <if test="bioEn != null">#{bioEn},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="email != null">#{email},</if>
            <if test="phone != null">#{phone},</if>
            <if test="linkedinUrl != null">#{linkedinUrl},</if>
            <if test="facebookUrl != null">#{facebookUrl},</if>
            <if test="linkedinAccount != null">#{linkedinAccount},</if>
            <if test="facebookAccount != null">#{facebookAccount},</if>
            <if test="memberType != null">#{memberType},</if>
            <if test="isLeadership != null">#{isLeadership},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateOrganizationMember" parameterType="OrganizationMember">
        update organization_members
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="nameZh != null and nameZh != ''">name_zh = #{nameZh},</if>
            <if test="nameEn != null and nameEn != ''">name_en = #{nameEn},</if>
            <if test="positionZh != null and positionZh != ''">position_zh = #{positionZh},</if>
            <if test="positionEn != null and positionEn != ''">position_en = #{positionEn},</if>
            <if test="departmentZh != null">department_zh = #{departmentZh},</if>
            <if test="departmentEn != null">department_en = #{departmentEn},</if>
            <if test="companyZh != null">company_zh = #{companyZh},</if>
            <if test="companyEn != null">company_en = #{companyEn},</if>
            <if test="bioZh != null">bio_zh = #{bioZh},</if>
            <if test="bioEn != null">bio_en = #{bioEn},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="linkedinUrl != null">linkedin_url = #{linkedinUrl},</if>
            <if test="facebookUrl != null">facebook_url = #{facebookUrl},</if>
            <if test="linkedinAccount != null">linkedin_account = #{linkedinAccount},</if>
            <if test="facebookAccount != null">facebook_account = #{facebookAccount},</if>
            <if test="memberType != null">member_type = #{memberType},</if>
            <if test="isLeadership != null">is_leadership = #{isLeadership},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOrganizationMemberById" parameterType="Long">
        delete from organization_members where id = #{id}
    </delete>

    <delete id="deleteOrganizationMemberByIds" parameterType="String">
        delete from organization_members where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>