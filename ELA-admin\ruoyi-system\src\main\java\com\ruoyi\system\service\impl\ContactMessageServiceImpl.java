package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ContactMessageMapper;
import com.ruoyi.system.domain.ContactMessage;
import com.ruoyi.system.service.IContactMessageService;

/**
 * 联系消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ContactMessageServiceImpl implements IContactMessageService 
{
    @Autowired
    private ContactMessageMapper contactMessageMapper;

    /**
     * 查询联系消息
     * 
     * @param id 联系消息主键
     * @return 联系消息
     */
    @Override
    public ContactMessage selectContactMessageById(Long id)
    {
        return contactMessageMapper.selectContactMessageById(id);
    }

    /**
     * 查询联系消息列表
     * 
     * @param contactMessage 联系消息
     * @return 联系消息
     */
    @Override
    public List<ContactMessage> selectContactMessageList(ContactMessage contactMessage)
    {
        return contactMessageMapper.selectContactMessageList(contactMessage);
    }

    /**
     * 新增联系消息
     * 
     * @param contactMessage 联系消息
     * @return 结果
     */
    @Override
    public int insertContactMessage(ContactMessage contactMessage)
    {
        return contactMessageMapper.insertContactMessage(contactMessage);
    }

    /**
     * 修改联系消息
     * 
     * @param contactMessage 联系消息
     * @return 结果
     */
    @Override
    public int updateContactMessage(ContactMessage contactMessage)
    {
        return contactMessageMapper.updateContactMessage(contactMessage);
    }

    /**
     * 批量删除联系消息
     * 
     * @param ids 需要删除的联系消息主键
     * @return 结果
     */
    @Override
    public int deleteContactMessageByIds(Long[] ids)
    {
        return contactMessageMapper.deleteContactMessageByIds(ids);
    }

    /**
     * 删除联系消息信息
     * 
     * @param id 联系消息主键
     * @return 结果
     */
    @Override
    public int deleteContactMessageById(Long id)
    {
        return contactMessageMapper.deleteContactMessageById(id);
    }
} 