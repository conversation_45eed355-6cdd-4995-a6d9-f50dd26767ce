<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MembershipApplicationMapper">
    
    <resultMap type="MembershipApplication" id="MembershipApplicationResult">
        <result property="id"    column="id"    />
        <result property="membershipType"    column="membership_type"    />
        <result property="applicantName"    column="applicant_name"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="phone"    column="phone"    />
        <result property="email"    column="email"    />
        <result property="address"    column="address"    />
        <result property="businessDescription"    column="business_description"    />
        <result property="recommender1"    column="recommender1"    />
        <result property="recommender2"    column="recommender2"    />
        <result property="status"    column="status"    />
        <result property="processNotes"    column="process_notes"    />
        <result property="processedBy"    column="processed_by"    />
        <result property="processedTime"    column="processed_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMembershipApplicationVo">
        select id, membership_type, applicant_name, contact_person, phone, email, address, business_description, recommender1, recommender2, status, process_notes, processed_by, processed_time, create_time, update_time, create_by, update_by, remark from membership_applications
    </sql>

    <select id="selectMembershipApplicationList" parameterType="MembershipApplication" resultMap="MembershipApplicationResult">
        <include refid="selectMembershipApplicationVo"/>
        <where>  
            <if test="membershipType != null  and membershipType != ''"> and membership_type = #{membershipType}</if>
            <if test="applicantName != null  and applicantName != ''"> and applicant_name like concat('%', #{applicantName}, '%')</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person like concat('%', #{contactPerson}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
            <if test="email != null  and email != ''"> and email like concat('%', #{email}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="processedBy != null  and processedBy != ''"> and processed_by like concat('%', #{processedBy}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMembershipApplicationById" parameterType="Long" resultMap="MembershipApplicationResult">
        <include refid="selectMembershipApplicationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMembershipApplication" parameterType="MembershipApplication" useGeneratedKeys="true" keyProperty="id">
        insert into membership_applications
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="membershipType != null and membershipType != ''">membership_type,</if>
            <if test="applicantName != null and applicantName != ''">applicant_name,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="businessDescription != null and businessDescription != ''">business_description,</if>
            <if test="recommender1 != null">recommender1,</if>
            <if test="recommender2 != null">recommender2,</if>
            <if test="status != null">status,</if>
            <if test="processNotes != null">process_notes,</if>
            <if test="processedBy != null">processed_by,</if>
            <if test="processedTime != null">processed_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="membershipType != null and membershipType != ''">#{membershipType},</if>
            <if test="applicantName != null and applicantName != ''">#{applicantName},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="businessDescription != null and businessDescription != ''">#{businessDescription},</if>
            <if test="recommender1 != null">#{recommender1},</if>
            <if test="recommender2 != null">#{recommender2},</if>
            <if test="status != null">#{status},</if>
            <if test="processNotes != null">#{processNotes},</if>
            <if test="processedBy != null">#{processedBy},</if>
            <if test="processedTime != null">#{processedTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMembershipApplication" parameterType="MembershipApplication">
        update membership_applications
        <trim prefix="SET" suffixOverrides=",">
            <if test="membershipType != null and membershipType != ''">membership_type = #{membershipType},</if>
            <if test="applicantName != null and applicantName != ''">applicant_name = #{applicantName},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="businessDescription != null and businessDescription != ''">business_description = #{businessDescription},</if>
            <if test="recommender1 != null">recommender1 = #{recommender1},</if>
            <if test="recommender2 != null">recommender2 = #{recommender2},</if>
            <if test="status != null">status = #{status},</if>
            <if test="processNotes != null">process_notes = #{processNotes},</if>
            <if test="processedBy != null">processed_by = #{processedBy},</if>
            <if test="processedTime != null">processed_time = #{processedTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMembershipApplicationById" parameterType="Long">
        delete from membership_applications where id = #{id}
    </delete>

    <delete id="deleteMembershipApplicationByIds" parameterType="String">
        delete from membership_applications where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 