<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ApplicationProcessMapper">
    
    <resultMap type="ApplicationProcess" id="ApplicationProcessResult">
        <result property="id"    column="id"    />
        <result property="stepNumber"    column="step_number"    />
        <result property="titleZh"    column="title_zh"    />
        <result property="titleEn"    column="title_en"    />
        <result property="descriptionZh"    column="description_zh"    />
        <result property="descriptionEn"    column="description_en"    />
        <result property="icon"    column="icon"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectApplicationProcessVo">
        select id, step_number, title_zh, title_en, description_zh, description_en, icon, sort_order, status, create_by, create_time, update_by, update_time, remark from application_process
    </sql>

    <select id="selectApplicationProcessList" parameterType="ApplicationProcess" resultMap="ApplicationProcessResult">
        <include refid="selectApplicationProcessVo"/>
        <where>  
            <if test="stepNumber != null "> and step_number = #{stepNumber}</if>
            <if test="titleZh != null  and titleZh != ''"> and title_zh like concat('%', #{titleZh}, '%')</if>
            <if test="titleEn != null  and titleEn != ''"> and title_en like concat('%', #{titleEn}, '%')</if>
            <if test="icon != null  and icon != ''"> and icon = #{icon}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, step_number asc
    </select>
    
    <select id="selectApplicationProcessById" parameterType="Long" resultMap="ApplicationProcessResult">
        <include refid="selectApplicationProcessVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertApplicationProcess" parameterType="ApplicationProcess" useGeneratedKeys="true" keyProperty="id">
        insert into application_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stepNumber != null">step_number,</if>
            <if test="titleZh != null and titleZh != ''">title_zh,</if>
            <if test="titleEn != null and titleEn != ''">title_en,</if>
            <if test="descriptionZh != null">description_zh,</if>
            <if test="descriptionEn != null">description_en,</if>
            <if test="icon != null">icon,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stepNumber != null">#{stepNumber},</if>
            <if test="titleZh != null and titleZh != ''">#{titleZh},</if>
            <if test="titleEn != null and titleEn != ''">#{titleEn},</if>
            <if test="descriptionZh != null">#{descriptionZh},</if>
            <if test="descriptionEn != null">#{descriptionEn},</if>
            <if test="icon != null">#{icon},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateApplicationProcess" parameterType="ApplicationProcess">
        update application_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="stepNumber != null">step_number = #{stepNumber},</if>
            <if test="titleZh != null and titleZh != ''">title_zh = #{titleZh},</if>
            <if test="titleEn != null and titleEn != ''">title_en = #{titleEn},</if>
            <if test="descriptionZh != null">description_zh = #{descriptionZh},</if>
            <if test="descriptionEn != null">description_en = #{descriptionEn},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteApplicationProcessById" parameterType="Long">
        delete from application_process where id = #{id}
    </delete>

    <delete id="deleteApplicationProcessByIds" parameterType="String">
        delete from application_process where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 