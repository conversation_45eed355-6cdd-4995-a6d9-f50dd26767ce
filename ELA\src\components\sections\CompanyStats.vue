<template>
  <section class="company-stats">
    <div class="stats-grid">
      <div v-for="stat in stats" :key="stat.label" class="stat-item">
        <div class="stat-number">{{ stat.number }}</div>
        <div class="stat-label">{{ stat.label }}</div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'CompanyStats',
  data() {
    return {
      stats: [
        { number: '1个', label: '国家重点实验室' },
        { number: '4个', label: '国家级创新平台' },
        { number: '40余项', label: '全国科学大会奖、国家/省部级科技奖等' },
        { number: '170余项', label: '制定国家标准和行业标准' },
        { number: '90%', label: '产品自主开发产业化' },
        { number: '400余项', label: '获省部级及以上各类奖项' },
        { number: '20余项', label: '承担国家"863"和科技支撑计划项目' },
        { number: '2300余项', label: '累计申请专利' }
      ]
    }
  }
}
</script>

<style scoped>
.company-stats {
  padding: 60px 0;
  background-color: #f9f9f9;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.stat-item {
  text-align: center;
  padding: 20px;
}

.stat-number {
  font-size: 48px;
  font-weight: 600;
  color: #0056b3;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 16px;
  color: #555;
  line-height: 1.5;
}
</style> 