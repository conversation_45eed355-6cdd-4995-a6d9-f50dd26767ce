<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HomeBannerMapper">
    
    <resultMap type="HomeBanner" id="HomeBannerResult">
        <result property="id"           column="id"           />
        <result property="imageUrl"     column="image_url"    />
        <result property="sort"         column="sort"         />
        <result property="status"       column="status"       />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
    </resultMap>

    <sql id="selectHomeBannerVo">
        select id, image_url, sort, status, create_by, create_time, update_by, update_time, remark from home_banner
    </sql>

    <select id="selectHomeBannerList" parameterType="HomeBanner" resultMap="HomeBannerResult">
        <include refid="selectHomeBannerVo"/>
        <where>  
            <if test="imageUrl != null  and imageUrl != ''"> and image_url like concat('%', #{imageUrl}, '%')</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort asc, create_time desc
    </select>
    
    <select id="selectHomeBannerById" parameterType="Long" resultMap="HomeBannerResult">
        <include refid="selectHomeBannerVo"/>
        where id = #{id}
    </select>

    <select id="selectEnabledHomeBannerList" resultMap="HomeBannerResult">
        <include refid="selectHomeBannerVo"/>
        where status = '0'
        order by sort asc, create_time desc
    </select>
        
    <insert id="insertHomeBanner" parameterType="HomeBanner" useGeneratedKeys="true" keyProperty="id">
        insert into home_banner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
         </trim>
    </insert>

    <update id="updateHomeBanner" parameterType="HomeBanner">
        update home_banner
        <trim prefix="SET" suffixOverrides=",">
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeBannerById" parameterType="Long">
        delete from home_banner where id = #{id}
    </delete>

    <delete id="deleteHomeBannerByIds" parameterType="String">
        delete from home_banner where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 