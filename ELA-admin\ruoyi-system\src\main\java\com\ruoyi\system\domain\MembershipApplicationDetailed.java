package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 详细会员申请表对象 membership_applications_detailed
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class MembershipApplicationDetailed extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 申请ID */
    private Long id;

    /** 会员类型 */
    @Excel(name = "会员类型")
    private String membershipType;

    // 个人资料 (Personal Particulars)
    /** 称谓 (Dr./Mr./Mrs./Ms.) */
    @Excel(name = "称谓")
    private String title;

    /** 英文姓氏 */
    @Excel(name = "英文姓氏")
    private String surnameEn;

    /** 英文名字 */
    @Excel(name = "英文名字")
    private String otherNameEn;

    /** 中文姓名 */
    @Excel(name = "中文姓名")
    private String nameZh;

    /** 护照/身分证号码 */
    @Excel(name = "护照/身分证号码")
    private String passportId;

    /** 出生日期 */
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthDate;

    // 联系方式 (Contact Information)
    /** 通讯地址 */
    @Excel(name = "通讯地址")
    private String correspondenceAddress;

    /** 电子邮箱 */
    @Excel(name = "电子邮箱")
    private String email;

    /** 手提电话号码 */
    @Excel(name = "手提电话号码")
    private String mobile;

    /** 办公室电话号码 */
    @Excel(name = "办公室电话号码")
    private String workplacePhone;

    /** 家居电话号码 */
    @Excel(name = "家居电话号码")
    private String residencePhone;

    // 学历信息 (Academic Qualifications)
    /** 学历1-院校 */
    @Excel(name = "学历1-院校")
    private String education1School;

    /** 学历1-授予日期 */
    @Excel(name = "学历1-授予日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date education1AwardDate;

    /** 学历1-资历 */
    @Excel(name = "学历1-资历")
    private String education1Qualification;

    /** 学历2-院校 */
    @Excel(name = "学历2-院校")
    private String education2School;

    /** 学历2-授予日期 */
    @Excel(name = "学历2-授予日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date education2AwardDate;

    /** 学历2-资历 */
    @Excel(name = "学历2-资历")
    private String education2Qualification;

    // 专业资历 (Professional Qualifications)
    /** 专业资历1-资格名称 */
    @Excel(name = "专业资历1-资格名称")
    private String professional1Qualification;

    /** 专业资历1-颁授机构 */
    @Excel(name = "专业资历1-颁授机构")
    private String professional1Institution;

    /** 专业资历1-授予日期 */
    @Excel(name = "专业资历1-授予日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date professional1AwardDate;

    /** 专业资历2-资格名称 */
    @Excel(name = "专业资历2-资格名称")
    private String professional2Qualification;

    /** 专业资历2-颁授机构 */
    @Excel(name = "专业资历2-颁授机构")
    private String professional2Institution;

    /** 专业资历2-授予日期 */
    @Excel(name = "专业资历2-授予日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date professional2AwardDate;

    // 工作经验 (Employment Record)
    /** 工作经验1-公司 */
    @Excel(name = "工作经验1-公司")
    private String employment1Company;

    /** 工作经验1-开始日期 */
    @Excel(name = "工作经验1-开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date employment1FromDate;

    /** 工作经验1-结束日期 */
    @Excel(name = "工作经验1-结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date employment1ToDate;

    /** 工作经验1-是否在职 */
    @Excel(name = "工作经验1-是否在职", readConverterExp = "0=否,1=是")
    private Integer employment1IsCurrent;

    /** 工作经验1-职位 */
    @Excel(name = "工作经验1-职位")
    private String employment1JobTitle;

    /** 工作经验1-主要职责 */
    @Excel(name = "工作经验1-主要职责")
    private String employment1MainDuties;

    /** 工作经验2-公司 */
    @Excel(name = "工作经验2-公司")
    private String employment2Company;

    /** 工作经验2-开始日期 */
    @Excel(name = "工作经验2-开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date employment2FromDate;

    /** 工作经验2-结束日期 */
    @Excel(name = "工作经验2-结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date employment2ToDate;

    /** 工作经验2-是否在职 */
    @Excel(name = "工作经验2-是否在职", readConverterExp = "0=否,1=是")
    private Integer employment2IsCurrent;

    /** 工作经验2-职位 */
    @Excel(name = "工作经验2-职位")
    private String employment2JobTitle;

    /** 工作经验2-主要职责 */
    @Excel(name = "工作经验2-主要职责")
    private String employment2MainDuties;

    // 申请者须知确认
    /** 文件提交要求确认 */
    @Excel(name = "文件提交要求确认", readConverterExp = "0=否,1=是")
    private Integer documentRequirementsAgreed;

    /** 保密条款确认 */
    @Excel(name = "保密条款确认", readConverterExp = "0=否,1=是")
    private Integer privacyPolicyAgreed;

    // 声明确认
    /** 声明确认 */
    @Excel(name = "声明确认", readConverterExp = "0=否,1=是")
    private Integer declarationAgreed;

    /** 章程遵守确认 */
    @Excel(name = "章程遵守确认", readConverterExp = "0=否,1=是")
    private Integer rulesAgreement;

    // 申请状态和处理信息
    /** 当前步骤 (1-6) */
    @Excel(name = "当前步骤")
    private Integer currentStep;

    /** 是否完成申请 */
    @Excel(name = "是否完成申请", readConverterExp = "0=否,1=是")
    private Integer isCompleted;

    /** 处理状态（0待处理 1已处理 2已拒绝） */
    @Excel(name = "处理状态", readConverterExp = "0=待处理,1=已处理,2=已拒绝")
    private String status;

    /** 处理备注 */
    @Excel(name = "处理备注")
    private String processNotes;

    /** 处理人 */
    @Excel(name = "处理人")
    private String processedBy;

    /** 处理时间 */
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processedTime;

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMembershipType() {
        return membershipType;
    }

    public void setMembershipType(String membershipType) {
        this.membershipType = membershipType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSurnameEn() {
        return surnameEn;
    }

    public void setSurnameEn(String surnameEn) {
        this.surnameEn = surnameEn;
    }

    public String getOtherNameEn() {
        return otherNameEn;
    }

    public void setOtherNameEn(String otherNameEn) {
        this.otherNameEn = otherNameEn;
    }

    public String getNameZh() {
        return nameZh;
    }

    public void setNameZh(String nameZh) {
        this.nameZh = nameZh;
    }

    public String getPassportId() {
        return passportId;
    }

    public void setPassportId(String passportId) {
        this.passportId = passportId;
    }

    public Date getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(Date birthDate) {
        this.birthDate = birthDate;
    }

    public String getCorrespondenceAddress() {
        return correspondenceAddress;
    }

    public void setCorrespondenceAddress(String correspondenceAddress) {
        this.correspondenceAddress = correspondenceAddress;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getWorkplacePhone() {
        return workplacePhone;
    }

    public void setWorkplacePhone(String workplacePhone) {
        this.workplacePhone = workplacePhone;
    }

    public String getResidencePhone() {
        return residencePhone;
    }

    public void setResidencePhone(String residencePhone) {
        this.residencePhone = residencePhone;
    }

    public String getEducation1School() {
        return education1School;
    }

    public void setEducation1School(String education1School) {
        this.education1School = education1School;
    }

    public Date getEducation1AwardDate() {
        return education1AwardDate;
    }

    public void setEducation1AwardDate(Date education1AwardDate) {
        this.education1AwardDate = education1AwardDate;
    }

    public String getEducation1Qualification() {
        return education1Qualification;
    }

    public void setEducation1Qualification(String education1Qualification) {
        this.education1Qualification = education1Qualification;
    }

    public String getEducation2School() {
        return education2School;
    }

    public void setEducation2School(String education2School) {
        this.education2School = education2School;
    }

    public Date getEducation2AwardDate() {
        return education2AwardDate;
    }

    public void setEducation2AwardDate(Date education2AwardDate) {
        this.education2AwardDate = education2AwardDate;
    }

    public String getEducation2Qualification() {
        return education2Qualification;
    }

    public void setEducation2Qualification(String education2Qualification) {
        this.education2Qualification = education2Qualification;
    }

    public String getProfessional1Qualification() {
        return professional1Qualification;
    }

    public void setProfessional1Qualification(String professional1Qualification) {
        this.professional1Qualification = professional1Qualification;
    }

    public String getProfessional1Institution() {
        return professional1Institution;
    }

    public void setProfessional1Institution(String professional1Institution) {
        this.professional1Institution = professional1Institution;
    }

    public Date getProfessional1AwardDate() {
        return professional1AwardDate;
    }

    public void setProfessional1AwardDate(Date professional1AwardDate) {
        this.professional1AwardDate = professional1AwardDate;
    }

    public String getProfessional2Qualification() {
        return professional2Qualification;
    }

    public void setProfessional2Qualification(String professional2Qualification) {
        this.professional2Qualification = professional2Qualification;
    }

    public String getProfessional2Institution() {
        return professional2Institution;
    }

    public void setProfessional2Institution(String professional2Institution) {
        this.professional2Institution = professional2Institution;
    }

    public Date getProfessional2AwardDate() {
        return professional2AwardDate;
    }

    public void setProfessional2AwardDate(Date professional2AwardDate) {
        this.professional2AwardDate = professional2AwardDate;
    }

    public String getEmployment1Company() {
        return employment1Company;
    }

    public void setEmployment1Company(String employment1Company) {
        this.employment1Company = employment1Company;
    }

    public Date getEmployment1FromDate() {
        return employment1FromDate;
    }

    public void setEmployment1FromDate(Date employment1FromDate) {
        this.employment1FromDate = employment1FromDate;
    }

    public Date getEmployment1ToDate() {
        return employment1ToDate;
    }

    public void setEmployment1ToDate(Date employment1ToDate) {
        this.employment1ToDate = employment1ToDate;
    }

    public Integer getEmployment1IsCurrent() {
        return employment1IsCurrent;
    }

    public void setEmployment1IsCurrent(Integer employment1IsCurrent) {
        this.employment1IsCurrent = employment1IsCurrent;
    }

    public String getEmployment1JobTitle() {
        return employment1JobTitle;
    }

    public void setEmployment1JobTitle(String employment1JobTitle) {
        this.employment1JobTitle = employment1JobTitle;
    }

    public String getEmployment1MainDuties() {
        return employment1MainDuties;
    }

    public void setEmployment1MainDuties(String employment1MainDuties) {
        this.employment1MainDuties = employment1MainDuties;
    }

    public String getEmployment2Company() {
        return employment2Company;
    }

    public void setEmployment2Company(String employment2Company) {
        this.employment2Company = employment2Company;
    }

    public Date getEmployment2FromDate() {
        return employment2FromDate;
    }

    public void setEmployment2FromDate(Date employment2FromDate) {
        this.employment2FromDate = employment2FromDate;
    }

    public Date getEmployment2ToDate() {
        return employment2ToDate;
    }

    public void setEmployment2ToDate(Date employment2ToDate) {
        this.employment2ToDate = employment2ToDate;
    }

    public Integer getEmployment2IsCurrent() {
        return employment2IsCurrent;
    }

    public void setEmployment2IsCurrent(Integer employment2IsCurrent) {
        this.employment2IsCurrent = employment2IsCurrent;
    }

    public String getEmployment2JobTitle() {
        return employment2JobTitle;
    }

    public void setEmployment2JobTitle(String employment2JobTitle) {
        this.employment2JobTitle = employment2JobTitle;
    }

    public String getEmployment2MainDuties() {
        return employment2MainDuties;
    }

    public void setEmployment2MainDuties(String employment2MainDuties) {
        this.employment2MainDuties = employment2MainDuties;
    }

    public Integer getDocumentRequirementsAgreed() {
        return documentRequirementsAgreed;
    }

    public void setDocumentRequirementsAgreed(Integer documentRequirementsAgreed) {
        this.documentRequirementsAgreed = documentRequirementsAgreed;
    }

    public Integer getPrivacyPolicyAgreed() {
        return privacyPolicyAgreed;
    }

    public void setPrivacyPolicyAgreed(Integer privacyPolicyAgreed) {
        this.privacyPolicyAgreed = privacyPolicyAgreed;
    }

    public Integer getDeclarationAgreed() {
        return declarationAgreed;
    }

    public void setDeclarationAgreed(Integer declarationAgreed) {
        this.declarationAgreed = declarationAgreed;
    }

    public Integer getRulesAgreement() {
        return rulesAgreement;
    }

    public void setRulesAgreement(Integer rulesAgreement) {
        this.rulesAgreement = rulesAgreement;
    }

    public Integer getCurrentStep() {
        return currentStep;
    }

    public void setCurrentStep(Integer currentStep) {
        this.currentStep = currentStep;
    }

    public Integer getIsCompleted() {
        return isCompleted;
    }

    public void setIsCompleted(Integer isCompleted) {
        this.isCompleted = isCompleted;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getProcessNotes() {
        return processNotes;
    }

    public void setProcessNotes(String processNotes) {
        this.processNotes = processNotes;
    }

    public String getProcessedBy() {
        return processedBy;
    }

    public void setProcessedBy(String processedBy) {
        this.processedBy = processedBy;
    }

    public Date getProcessedTime() {
        return processedTime;
    }

    public void setProcessedTime(Date processedTime) {
        this.processedTime = processedTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("membershipType", getMembershipType())
            .append("title", getTitle())
            .append("surnameEn", getSurnameEn())
            .append("otherNameEn", getOtherNameEn())
            .append("nameZh", getNameZh())
            .append("passportId", getPassportId())
            .append("birthDate", getBirthDate())
            .append("correspondenceAddress", getCorrespondenceAddress())
            .append("email", getEmail())
            .append("mobile", getMobile())
            .append("workplacePhone", getWorkplacePhone())
            .append("residencePhone", getResidencePhone())
            .append("education1School", getEducation1School())
            .append("education1AwardDate", getEducation1AwardDate())
            .append("education1Qualification", getEducation1Qualification())
            .append("education2School", getEducation2School())
            .append("education2AwardDate", getEducation2AwardDate())
            .append("education2Qualification", getEducation2Qualification())
            .append("professional1Qualification", getProfessional1Qualification())
            .append("professional1Institution", getProfessional1Institution())
            .append("professional1AwardDate", getProfessional1AwardDate())
            .append("professional2Qualification", getProfessional2Qualification())
            .append("professional2Institution", getProfessional2Institution())
            .append("professional2AwardDate", getProfessional2AwardDate())
            .append("employment1Company", getEmployment1Company())
            .append("employment1FromDate", getEmployment1FromDate())
            .append("employment1ToDate", getEmployment1ToDate())
            .append("employment1IsCurrent", getEmployment1IsCurrent())
            .append("employment1JobTitle", getEmployment1JobTitle())
            .append("employment1MainDuties", getEmployment1MainDuties())
            .append("employment2Company", getEmployment2Company())
            .append("employment2FromDate", getEmployment2FromDate())
            .append("employment2ToDate", getEmployment2ToDate())
            .append("employment2IsCurrent", getEmployment2IsCurrent())
            .append("employment2JobTitle", getEmployment2JobTitle())
            .append("employment2MainDuties", getEmployment2MainDuties())
            .append("documentRequirementsAgreed", getDocumentRequirementsAgreed())
            .append("privacyPolicyAgreed", getPrivacyPolicyAgreed())
            .append("declarationAgreed", getDeclarationAgreed())
            .append("rulesAgreement", getRulesAgreement())
            .append("currentStep", getCurrentStep())
            .append("isCompleted", getIsCompleted())
            .append("status", getStatus())
            .append("processNotes", getProcessNotes())
            .append("processedBy", getProcessedBy())
            .append("processedTime", getProcessedTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("remark", getRemark())
            .toString();
    }
}