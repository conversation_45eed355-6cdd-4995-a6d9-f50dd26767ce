import request from '@/utils/request'

// 查询组织架构成员列表
export function listOrganizationMember(query) {
  return request({
    url: '/system/organization/member/list',
    method: 'get',
    params: query
  })
}

// 查询组织架构成员详细
export function getOrganizationMember(id) {
  return request({
    url: '/system/organization/member/' + id,
    method: 'get'
  })
}

// 新增组织架构成员
export function addOrganizationMember(data) {
  return request({
    url: '/system/organization/member',
    method: 'post',
    data: data
  })
}

// 修改组织架构成员
export function updateOrganizationMember(data) {
  return request({
    url: '/system/organization/member',
    method: 'put',
    data: data
  })
}

// 删除组织架构成员
export function delOrganizationMember(id) {
  return request({
    url: '/system/organization/member/' + id,
    method: 'delete'
  })
}

// 获取组织架构分类选项
export function getOrganizationCategoryOptions() {
  return request({
    url: '/system/organization/category/options',
    method: 'get'
  })
}

// 获取成员类型选项
export function getMemberTypeOptions() {
  return request({
    url: '/system/organization/membertype/options',
    method: 'get'
  })
}

// 根据组织架构分类ID获取成员类型选项
export function getMemberTypeOptionsByCategoryId(categoryId) {
  return request({
    url: '/system/organization/membertype/options/' + categoryId,
    method: 'get'
  })
} 