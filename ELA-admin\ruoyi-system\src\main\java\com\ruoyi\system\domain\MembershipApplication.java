package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 会员申请表对象 membership_applications
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class MembershipApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 申请ID */
    private Long id;

    /** 会员类型 */
    @Excel(name = "会员类型")
    private String membershipType;

    /** 申请人姓名/企业名称 */
    @Excel(name = "申请人姓名/企业名称")
    private String applicantName;

    /** 联络人 */
    @Excel(name = "联络人")
    private String contactPerson;

    /** 电话号码 */
    @Excel(name = "电话号码")
    private String phone;

    /** 电子邮箱 */
    @Excel(name = "电子邮箱")
    private String email;

    /** 地址 */
    @Excel(name = "地址")
    private String address;

    /** 业务描述 */
    @Excel(name = "业务描述")
    private String businessDescription;

    /** 推荐人一 */
    @Excel(name = "推荐人一")
    private String recommender1;

    /** 推荐人二 */
    @Excel(name = "推荐人二")
    private String recommender2;

    /** 处理状态（0待处理 1已处理 2已拒绝） */
    @Excel(name = "处理状态", readConverterExp = "0=待处理,1=已处理,2=已拒绝")
    private String status;

    /** 处理备注 */
    @Excel(name = "处理备注")
    private String processNotes;

    /** 处理人 */
    @Excel(name = "处理人")
    private String processedBy;

    /** 处理时间 */
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processedTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setMembershipType(String membershipType) 
    {
        this.membershipType = membershipType;
    }

    public String getMembershipType() 
    {
        return membershipType;
    }
    public void setApplicantName(String applicantName) 
    {
        this.applicantName = applicantName;
    }

    public String getApplicantName() 
    {
        return applicantName;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setEmail(String email) 
    {
        this.email = email;
    }

    public String getEmail() 
    {
        return email;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setBusinessDescription(String businessDescription) 
    {
        this.businessDescription = businessDescription;
    }

    public String getBusinessDescription() 
    {
        return businessDescription;
    }
    public void setRecommender1(String recommender1) 
    {
        this.recommender1 = recommender1;
    }

    public String getRecommender1() 
    {
        return recommender1;
    }
    public void setRecommender2(String recommender2) 
    {
        this.recommender2 = recommender2;
    }

    public String getRecommender2() 
    {
        return recommender2;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setProcessNotes(String processNotes) 
    {
        this.processNotes = processNotes;
    }

    public String getProcessNotes() 
    {
        return processNotes;
    }
    public void setProcessedBy(String processedBy) 
    {
        this.processedBy = processedBy;
    }

    public String getProcessedBy() 
    {
        return processedBy;
    }
    public void setProcessedTime(Date processedTime) 
    {
        this.processedTime = processedTime;
    }

    public Date getProcessedTime() 
    {
        return processedTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("membershipType", getMembershipType())
            .append("applicantName", getApplicantName())
            .append("contactPerson", getContactPerson())
            .append("phone", getPhone())
            .append("email", getEmail())
            .append("address", getAddress())
            .append("businessDescription", getBusinessDescription())
            .append("recommender1", getRecommender1())
            .append("recommender2", getRecommender2())
            .append("status", getStatus())
            .append("processNotes", getProcessNotes())
            .append("processedBy", getProcessedBy())
            .append("processedTime", getProcessedTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("remark", getRemark())
            .toString();
    }
} 