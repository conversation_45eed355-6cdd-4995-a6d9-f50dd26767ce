package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.WebsiteBannerMapper;
import com.ruoyi.system.domain.WebsiteBanner;
import com.ruoyi.system.service.IWebsiteBannerService;

/**
 * 网站BannerService业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class WebsiteBannerServiceImpl implements IWebsiteBannerService 
{
    @Autowired
    private WebsiteBannerMapper websiteBannerMapper;

    /**
     * 查询网站Banner
     * 
     * @param id 网站Banner主键
     * @return 网站Banner
     */
    @Override
    public WebsiteBanner selectWebsiteBannerById(Long id)
    {
        return websiteBannerMapper.selectWebsiteBannerById(id);
    }

    /**
     * 查询网站Banner列表
     * 
     * @param websiteBanner 网站Banner
     * @return 网站Banner
     */
    @Override
    public List<WebsiteBanner> selectWebsiteBannerList(WebsiteBanner websiteBanner)
    {
        return websiteBannerMapper.selectWebsiteBannerList(websiteBanner);
    }

    /**
     * 查询启用的网站Banner列表
     * 
     * @return 网站Banner集合
     */
    @Override
    public List<WebsiteBanner> selectEnabledWebsiteBannerList()
    {
        return websiteBannerMapper.selectEnabledWebsiteBannerList();
    }

    /**
     * 新增网站Banner
     * 
     * @param websiteBanner 网站Banner
     * @return 结果
     */
    @Override
    public int insertWebsiteBanner(WebsiteBanner websiteBanner)
    {
        websiteBanner.setCreateTime(DateUtils.getNowDate());
        return websiteBannerMapper.insertWebsiteBanner(websiteBanner);
    }

    /**
     * 修改网站Banner
     * 
     * @param websiteBanner 网站Banner
     * @return 结果
     */
    @Override
    public int updateWebsiteBanner(WebsiteBanner websiteBanner)
    {
        websiteBanner.setUpdateTime(DateUtils.getNowDate());
        return websiteBannerMapper.updateWebsiteBanner(websiteBanner);
    }

    /**
     * 批量删除网站Banner
     * 
     * @param ids 需要删除的网站Banner主键
     * @return 结果
     */
    @Override
    public int deleteWebsiteBannerByIds(Long[] ids)
    {
        return websiteBannerMapper.deleteWebsiteBannerByIds(ids);
    }

    /**
     * 删除网站Banner信息
     * 
     * @param id 网站Banner主键
     * @return 结果
     */
    @Override
    public int deleteWebsiteBannerById(Long id)
    {
        return websiteBannerMapper.deleteWebsiteBannerById(id);
    }
} 